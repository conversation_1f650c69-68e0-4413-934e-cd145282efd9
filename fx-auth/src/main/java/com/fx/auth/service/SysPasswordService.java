package com.fx.auth.service;

import com.fx.common.core.constant.CacheConstants;
import com.fx.common.core.constant.Constants;
import com.fx.common.core.exception.ServiceException;
import com.fx.common.core.utils.SecurityUtils;
import com.fx.common.redis.service.RedisService;
import com.fx.system.api.domain.SysUser;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * 登录密码方法
 *
 * <AUTHOR>
 */
@Component
public class SysPasswordService {

    private static final int maxRetryCount = CacheConstants.PASSWORD_MAX_RETRY_COUNT;
    private static final Long lockTime = CacheConstants.PASSWORD_LOCK_TIME;

    @Resource
    private RedisService redisService;
    @Resource
    private SysRecordLogService recordLogService;

    /**
     * 登录账户密码错误次数缓存键名
     *
     * @param username 用户名
     * @return 缓存键key
     */
    private String getCacheKey(String username) {
        return CacheConstants.PWD_ERR_CNT_KEY + username;
    }

    /**
     * 验证密码
     *
     * @param user     用户
     * @param password 密码
     */
    public void validate(SysUser user, String password) {
        String username = user.getUserName();

        Integer retryCount = redisService.getCacheObject(getCacheKey(username));

        if (retryCount == null) {
            retryCount = 0;
        }

        if (retryCount >= maxRetryCount) {
            String errMsg = "用户名或密码错误";
            recordLogService.recordLoginInformation(username, Constants.LOGIN_FAIL,
                    String.format("密码错误次数超过%d次,账户已锁定%d分钟", maxRetryCount, lockTime));
            redisService.setCacheObject(getCacheKey(username), retryCount);
            throw new ServiceException(errMsg);
        }
        if (!matches(user, password)) {
            retryCount = retryCount + 1;
            recordLogService.recordLoginInformation(username, Constants.LOGIN_FAIL,
                    String.format("密码错误,已尝试%d次,剩余%d次", retryCount, maxRetryCount - retryCount));
            redisService.setCacheObject(getCacheKey(username), retryCount, lockTime, TimeUnit.MINUTES);
            throw new ServiceException("用户名或密码错误");
        } else {
            clearLoginRecordCache(username);
        }
    }

    /**
     * 匹配密码
     *
     * @param user        用户
     * @param rawPassword 原始密码
     * @return 是否匹配
     */
    public boolean matches(SysUser user, String rawPassword) {
        return SecurityUtils.matchesPassword(rawPassword, user.getPassword());
    }

    /**
     * 清除登录记录缓存
     *
     * @param loginName 登录名
     */
    public void clearLoginRecordCache(String loginName) {
        if (redisService.hasKey(getCacheKey(loginName))) {
            redisService.deleteObject(getCacheKey(loginName));
        }
    }

}
