package com.fx.auth.service;

import com.fx.common.core.constant.Constants;
import com.fx.common.core.constant.SecurityConstants;
import com.fx.common.core.constant.UserConstants;
import com.fx.common.core.domain.R;
import com.fx.common.core.enums.UserStatus;
import com.fx.common.core.exception.ServiceException;
import com.fx.common.core.utils.SecurityUtils;
import com.fx.common.core.utils.ServletUtils;
import com.fx.common.core.utils.StringUtils;
import com.fx.common.core.utils.ip.IpUtils;
import com.fx.system.api.RemoteLogService;
import com.fx.system.api.RemoteUserService;
import com.fx.system.api.domain.SysLogininfor;
import com.fx.system.api.domain.SysUser;
import com.fx.system.api.model.LoginUser;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 登录校验方法
 *
 * <AUTHOR>
 */
@Component
public class SysLoginService {

    @Resource
    private RemoteLogService remoteLogService;

    @Resource
    private RemoteUserService remoteUserService;

    @Resource
    private SysPasswordService passwordService;

    /**
     * 登录
     *
     * @param username 用户名
     * @param password 密码
     * @return 结果
     */
    public LoginUser login(String username, String password) {
        // 用户名或密码为空 错误
        if (StringUtils.isAnyBlank(username, password)) {
            recordLoginInformation(username, Constants.LOGIN_FAIL, "用户/密码必须填写");
            throw new ServiceException("用户/密码必须填写");
        }
        // 密码如果不在指定范围内 错误
        if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                || password.length() > UserConstants.PASSWORD_MAX_LENGTH) {
            recordLoginInformation(username, Constants.LOGIN_FAIL, "用户密码不在指定范围");
            throw new ServiceException("用户密码不在指定范围");
        }
        // 用户名不在指定范围内 错误
        if (username.length() < UserConstants.USERNAME_MIN_LENGTH
                || username.length() > UserConstants.USERNAME_MAX_LENGTH) {
            recordLoginInformation(username, Constants.LOGIN_FAIL, "用户名不在指定范围");
            throw new ServiceException("用户名不在指定范围");
        }
        // 查询用户信息
        R<LoginUser> userResult = remoteUserService.getUserInfo(username, SecurityConstants.INNER);

        if (R.FAIL == userResult.getCode()) {
            recordLoginInformation(username, Constants.LOGIN_FAIL, "远程调用用户服务失败: " + userResult.getMsg());
            throw new ServiceException("用户名或密码错误");
        }

        if (StringUtils.isNull(userResult.getData())) {
            recordLoginInformation(username, Constants.LOGIN_FAIL, "登录用户不存在");
            throw new ServiceException("用户名或密码错误");
        }
        LoginUser userInfo = userResult.getData();
        SysUser user = userResult.getData().getSysUser();
        if (UserStatus.DELETED.getCode().equals(user.getDelFlag())) {
            recordLoginInformation(username, Constants.LOGIN_FAIL, "用户账号已被删除");
            throw new ServiceException("用户名或密码错误");
        }
        if (UserStatus.DISABLE.getCode().equals(user.getStatus())) {
            recordLoginInformation(username, Constants.LOGIN_FAIL, "用户账号已被停用");
            throw new ServiceException("用户名或密码错误");
        }
        passwordService.validate(user, password);
        recordLoginInformation(username, Constants.LOGIN_SUCCESS, "登录成功");
        return userInfo;
    }

    public void logout(String loginName) {
        recordLoginInformation(loginName, Constants.LOGOUT, "退出成功");
    }

    /**
     * 注册
     *
     * @param username 用户名
     * @param password 密码
     */
    public void register(String username, String password) {
        // 用户名或密码为空 错误
        if (StringUtils.isAnyBlank(username, password)) {
            throw new ServiceException("用户/密码必须填写");
        }
        if (username.length() < UserConstants.USERNAME_MIN_LENGTH
                || username.length() > UserConstants.USERNAME_MAX_LENGTH) {
            throw new ServiceException("账户长度必须在2到20个字符之间");
        }
        if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                || password.length() > UserConstants.PASSWORD_MAX_LENGTH) {
            throw new ServiceException("密码长度必须在5到20个字符之间");
        }

        // 注册用户信息
        SysUser sysUser = new SysUser();
        sysUser.setUserName(username);
        sysUser.setNickName(username);
        sysUser.setPassword(SecurityUtils.encryptPassword(password));
        R<?> registerResult = remoteUserService.registerUserInfo(sysUser, SecurityConstants.INNER);

        if (R.FAIL == registerResult.getCode()) {
            throw new ServiceException(registerResult.getMsg());
        }
        recordLoginInformation(username, Constants.REGISTER, "注册成功");
    }

    /**
     * 记录登录信息
     *
     * @param username 用户名
     * @param status   状态
     * @param message  消息内容
     */
    public void recordLoginInformation(String username, String status, String message) {
        SysLogininfor loginLog = new SysLogininfor();
        loginLog.setUserName(username);
        loginLog.setIpaddr(IpUtils.getIpAddr(ServletUtils.getRequest()));
        loginLog.setMsg(message);
        // 日志状态
        if (StringUtils.equalsAny(status, Constants.LOGIN_SUCCESS, Constants.LOGOUT, Constants.REGISTER)) {
            loginLog.setStatus("0");
        } else if (Constants.LOGIN_FAIL.equals(status)) {
            loginLog.setStatus("1");
        }
        remoteLogService.saveLogininfor(loginLog, SecurityConstants.INNER);
    }

}