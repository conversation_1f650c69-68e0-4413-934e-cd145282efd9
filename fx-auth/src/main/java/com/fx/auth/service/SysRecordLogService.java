package com.fx.auth.service;

import com.fx.common.core.constant.Constants;
import com.fx.common.core.constant.SecurityConstants;
import com.fx.common.core.utils.ServletUtils;
import com.fx.common.core.utils.StringUtils;
import com.fx.common.core.utils.ip.IpUtils;
import com.fx.system.api.RemoteLogService;
import com.fx.system.api.domain.SysLogininfor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 记录日志方法
 *
 * <AUTHOR>
 */
@Component
public class SysRecordLogService {

    @Resource
    private RemoteLogService remoteLogService;

    /**
     * 记录登录信息
     *
     * @param username 用户名
     * @param status   状态
     * @param message  消息内容
     */
    public void recordLoginInformation(String username, String status, String message) {
        // 1. 获取用户登录信息
        SysLogininfor loginLog = new SysLogininfor();
        loginLog.setUserName(username);
        loginLog.setIpaddr(IpUtils.getIpAddr(ServletUtils.getRequest()));
        loginLog.setMsg(message);
        // 2. 设置状态
        if (StringUtils.equalsAny(status, Constants.LOGIN_SUCCESS, Constants.LOGOUT, Constants.REGISTER)) {
            loginLog.setStatus(Constants.LOGIN_SUCCESS_STATUS);
        } else if (Constants.LOGIN_FAIL.equals(status)) {
            loginLog.setStatus(Constants.LOGIN_FAIL_STATUS);
        }
        // 3. 保存登录日志
        remoteLogService.saveLogininfor(loginLog, SecurityConstants.INNER);
    }

}
