package com.fx.auth.controller;

import com.fx.common.core.constant.CacheConstants;
import com.fx.common.core.domain.R;
import com.fx.common.datascope.info.LicenseConstants;
import com.fx.common.datascope.manager.CustomLicenseManager;
import com.fx.common.datascope.model.LicenseCheckModel;
import com.fx.common.datascope.verify.LicenseVerify;
import com.fx.common.datascope.verify.LicenseVerifyParam;
import com.fx.common.redis.service.RedisService;
import de.schlichtherle.license.LicenseContent;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * 授权控制器类
 */
@Api(tags = "授权")
@RestController
@Slf4j
public class LicenseController {

    @Resource
    private RedisService redisService;

    /**
     * 读取文件内容为字节数组
     *
     * @param filePath 文件路径
     * @return 文件内容字节数组
     * @throws IOException IO异常
     */
    private static byte[] readFileToBytes(String filePath) throws IOException {
        File file = new File(filePath);
        byte[] fileContent = new byte[(int) file.length()];
        try (FileInputStream fis = new FileInputStream(file)) {
            fis.read(fileContent);
        }
        return fileContent;
    }

    /**
     * 初始化方法，在Bean创建后调用
     * 许可证安装失败不会阻止服务器启动
     */
    @PostConstruct
    public void init() {
        try {
            String licenseFile = redisService.getCacheObject(CacheConstants.getLicenseCacheKey("licenseFile"));
            if (licenseFile != null) {
                installLicenseForInit();
            } else {
                log.info("未找到缓存的许可证文件，服务器将正常启动");
            }
        } catch (Exception e) {
            log.warn("许可证初始化失败，服务器将正常启动，但功能可能受限: {}", e.getMessage());
        }
    }

    /**
     * 授权证书上传接口
     *
     * @param file 上传的文件
     * @return 响应结果
     * @throws Exception 异常
     */
    @PostMapping("/licenseUpload")
    @ApiOperation(value = "授权证书上传")
    public R<?> licenseUpload(@RequestParam("file") MultipartFile file) throws Exception {
        if (file.isEmpty()) {
            throw new Exception("授权证书不能为空");
        }
        File dest = saveFile(file);
        LicenseContent licenseContent = installLicenseForUpload();

        // 构建返回数据
        Map<String, Object> data = new HashMap<>();
        data.put("info", licenseContent.getInfo());
        data.put("notBefore", licenseContent.getNotBefore());
        data.put("notAfter", licenseContent.getNotAfter());

        return R.ok(data);
    }

    /**
     * 查询MAC地址接口
     *
     * @return 响应结果
     */
    @GetMapping("findMacAddress")
    @ApiOperation(value = "查询mac地址")
    public R<?> findMacAddress() {
        CustomLicenseManager customLicenseManager = new CustomLicenseManager();
        LicenseCheckModel serverInfos = customLicenseManager.getServerInfos();
        return R.ok(serverInfos.getMacAddress().get(0));
    }

    /**
     * 安装许可证（用于初始化，失败不抛异常）
     *
     * @return 许可证内容对象，失败时返回null
     */
    private LicenseContent installLicenseForInit() {
        try {
            return doInstallLicense();
        } catch (Exception e) {
            log.warn("许可证安装失败: {}", e.getMessage());
            redisService.setCacheObject(CacheConstants.getLicenseCacheKey("licenseFile"), null);
            return null;
        }
    }

    /**
     * 安装许可证（用于上传，失败抛异常）
     *
     * @return 许可证内容对象
     * @throws Exception 安装失败时抛出异常
     */
    private LicenseContent installLicenseForUpload() throws Exception {
        try {
            LicenseContent licenseContent = doInstallLicense();
            if (licenseContent == null) {
                throw new Exception("证书不存在或已失效");
            }
            return licenseContent;
        } catch (Exception e) {
            redisService.setCacheObject(CacheConstants.getLicenseCacheKey("licenseFile"), null);
            throw new Exception("证书安装失败: " + e.getMessage());
        }
    }

    /**
     * 执行许可证安装的核心逻辑
     *
     * @return 许可证内容对象
     * @throws IOException IO异常
     */
    private LicenseContent doInstallLicense() throws IOException {
        LicenseVerifyParam param = new LicenseVerifyParam();
        param.setSubject(LicenseConstants.SUBJECT);
        param.setPublicAlias(LicenseConstants.PUBLIC_ALIAS);
        param.setStorePass(LicenseConstants.STORE_PASS);
        param.setLicensePath(LicenseConstants.LICENSE_PATH);
        param.setPublicKeysStorePath(LicenseConstants.PUBLIC_KEYS_STORE_PATH);
        LicenseVerify licenseVerify = new LicenseVerify();
        log.info("安装证书,参数:{}", param);
        LicenseContent licenseContent = licenseVerify.install(param);
        if (licenseContent != null) {
            byte[] fileContent = readFileToBytes(LicenseConstants.LICENSE_PATH);
            String encodedString = Base64.getEncoder().encodeToString(fileContent);
            redisService.setCacheObject(CacheConstants.getLicenseCacheKey("licenseFile"), encodedString);
            log.info("证书安装成功");
            return licenseContent;
        } else {
            log.warn("证书安装失败：证书内容为空");
            return null;
        }
    }

    /**
     * 保存上传的文件
     *
     * @param file 上传的文件
     * @return 保存后的文件
     * @throws IOException IO异常
     */
    private File saveFile(MultipartFile file) throws IOException {
        File dir = new File(System.getProperty("user.dir") + File.separator + "upload" + File.separator);
        if (!dir.exists()) {
            dir.mkdirs();
        }
        String suffix = "";
        String originalFilename = file.getOriginalFilename();
        int index = 0;
        if (originalFilename != null) {
            index = originalFilename.indexOf(".");
        }
        if (index > 0) {
            suffix = originalFilename.substring(index);
        }
        String fileName = "license" + suffix;
        File file1 = new File(LicenseConstants.LICENSE_PATH);
        file1.delete();
        File dest = new File(dir, fileName);
        file.transferTo(dest);
        return dest;
    }
}