package com.fx.auth.controller;

import com.fx.auth.form.LoginBody;
import com.fx.auth.form.RegisterBody;
import com.fx.auth.service.SysLoginService;
import com.fx.common.core.domain.R;
import com.fx.common.core.utils.StringUtils;
import com.fx.common.datascope.annotation.License;
import com.fx.common.security.service.TokenService;
import com.fx.system.api.model.LoginUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * token 控制
 *
 * <AUTHOR>
 */
@Api(tags = "认证")
@RestController
public class TokenController {

    @Resource
    private TokenService tokenService;
    @Resource
    private SysLoginService sysLoginService;

    @PostMapping("login")
    @ApiOperation(value = "登录")
    @License
    public R<?> login(@RequestBody LoginBody form) {
        // 用户登录
        LoginUser userInfo = sysLoginService.login(form.getUsername(), form.getPassword());
        // 获取登录token
        return R.ok(tokenService.createToken(userInfo));
    }

    @DeleteMapping("logout")
    @ApiOperation(value = "退出")
    public R<?> logout(HttpServletRequest request) {
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (StringUtils.isNotNull(loginUser)) {
            String username = loginUser.getUsername();
            // 删除用户缓存记录
            tokenService.delLoginUser(loginUser.getToken());
            // 记录用户退出日志
            sysLoginService.logout(username);
        }
        return R.ok();
    }

    @PostMapping("refresh")
    @ApiOperation(value = "刷新令牌")
    public R<?> refresh(HttpServletRequest request) {
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (StringUtils.isNotNull(loginUser)) {
            // 刷新令牌有效期
            tokenService.refreshToken(loginUser);
            return R.ok();
        }
        return R.ok();
    }

    @PostMapping("register")
    @ApiOperation(value = "注册")
    public R<?> register(@RequestBody RegisterBody registerBody) {
        // 用户注册
        sysLoginService.register(registerBody.getUsername(), registerBody.getPassword());
        return R.ok();
    }

}
