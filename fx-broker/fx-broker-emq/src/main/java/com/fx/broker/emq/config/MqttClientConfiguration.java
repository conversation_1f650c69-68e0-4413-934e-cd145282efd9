package com.fx.broker.emq.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class MqttClientConfiguration {

    @Autowired
    private MqttConfig mqttConfig;

    private static EmqxClient emqxClient;

    @Bean
    public EmqxClient mqttClient() {
        if (emqxClient == null) {
            emqxClient = new EmqxClient("fx" + mqttConfig.getClientId(),
                    mqttConfig.getUsername(),
                    mqttConfig.getPassword(),
                    mqttConfig.getHostUrl(),
                    mqttConfig.getTimeout(),
                    mqttConfig.getKeepalive(),
                    mqttConfig.isClearSession()
            );
        }
        return emqxClient;
    }

    public static EmqxClient getClientInstance() {
        return emqxClient;
    }
}
