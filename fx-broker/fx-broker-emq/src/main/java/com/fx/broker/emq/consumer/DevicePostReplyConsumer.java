package com.fx.broker.emq.consumer;

import com.fx.broker.emq.config.EmqxClient;
import com.fx.common.core.domain.ThingModelMessage;
import com.fx.common.core.utils.JsonUtil;
import com.fx.common.rocketmq.constant.ConsumerGroupConstant;
import com.fx.common.rocketmq.constant.ConsumerTopicConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 消息会调
 *
 * <AUTHOR>
 * @since 2024-06-07
 */
@Slf4j
@Component
@RocketMQMessageListener(consumerGroup = ConsumerGroupConstant.FX_EMQ_DEVICE_POST_REPLY_CONSUMER, topic = ConsumerTopicConstant.FX_LINKS_DEVICE_POST_REPLY)
public class DevicePostReplyConsumer implements RocketMQListener<ThingModelMessage> {

    @Resource
    private EmqxClient emqxClient;

    @Override
    public void onMessage(ThingModelMessage thingModelMessage) {
        if (thingModelMessage != null) {
            ///sys/jyrXQ6Aennk3dNEc/aM8mTpd7kJFHNBjT/thing/event/property/post_reply
            log.info("监听消息回调，消息内容：{}", thingModelMessage);
            String topic = "/sys/" + thingModelMessage.getProductKey() + "/" + thingModelMessage.getDeviceKey() + "/thing/event/property/post_reply";
            Object data = thingModelMessage.getData();
            // 判断是否是json字符串
            if (data instanceof String) {
                emqxClient.publish(1, false, topic, (String) data);
            } else {
                emqxClient.publish(1, false, topic, JsonUtil.toJsonString(thingModelMessage.getData()));
            }
        }
    }
}
