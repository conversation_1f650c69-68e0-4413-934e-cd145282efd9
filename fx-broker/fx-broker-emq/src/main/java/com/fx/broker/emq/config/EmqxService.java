package com.fx.broker.emq.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fx.broker.emq.service.DeviceMessageService;
import com.fx.common.core.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.MqttAsyncClient;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;

@Service
@Slf4j
public class EmqxService {

    private static final String SHARED_SUBSCRIPTION_PREFIX = "$share/fx-links/";
    private static final String TOPIC_PREFIX = "/";

    @Resource
    private DeviceMessageService deviceMessageService;

    /**
     * Topic信息类，用于存储解析后的topic信息
     */
    private static class TopicInfo {
        public String productKey;
        public String deviceKey;
        public String[] segments;

        public TopicInfo(String productKey, String deviceKey, String[] segments) {
            this.productKey = productKey;
            this.deviceKey = deviceKey;
            this.segments = segments;
        }
    }

    /**
     * 解析topic信息
     * 
     * @param topic 原始topic
     * @return 解析后的topic信息，如果解析失败返回null
     */
    private TopicInfo parseTopicInfo(String topic) {
        try {
            if (topic == null || topic.isEmpty()) {
                log.error("topic为空");
                return null;
            }

            log.debug("开始解析topic: {}", topic);

            // 移除开头的斜杠并分割
            String[] topicSegments = topic.substring(1).split("/");

            if (topicSegments.length <= 3) {
                log.error("topic格式不符合规则，段数不足: topic={}, 段数={}", topic, topicSegments.length);
                return null;
            }

            // 对于标准的IoT topic格式: /sys/productKey/deviceKey/...
            String productKey = topicSegments[1];
            String deviceKey = topicSegments[2];

            // 验证关键参数
            if (productKey == null || productKey.isEmpty()) {
                log.error("productKey为空或无效: topic={}", topic);
                return null;
            }

            if (deviceKey == null || deviceKey.isEmpty()) {
                log.error("deviceKey为空或无效: topic={}", topic);
                return null;
            }

            // 验证productKey和deviceKey的格式
            if (!isValidKey(productKey)) {
                log.error("productKey格式无效: topic={}, productKey={}", topic, productKey);
                return null;
            }

            if (!isValidKey(deviceKey)) {
                log.error("deviceKey格式无效: topic={}, deviceKey={}", topic, deviceKey);
                return null;
            }

            log.debug("成功解析topic: topic={}, productKey={}, deviceKey={}", topic, productKey, deviceKey);

            return new TopicInfo(productKey, deviceKey, topicSegments);

        } catch (Exception e) {
            log.error("解析topic异常: topic={}, 错误: {}", topic, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 验证key的有效性
     * 
     * @param key 待验证的key
     * @return 是否有效
     */
    private boolean isValidKey(String key) {
        // 基本格式验证：只允许字母、数字和常见符号
        return key != null && key.matches("^[a-zA-Z0-9_-]+$") && !key.isEmpty() && key.length() <= 64;
    }

    /**
     * 发布的主题
     */
    public void subscribe(MqttAsyncClient client) throws MqttException {
        client.subscribe(SHARED_SUBSCRIPTION_PREFIX + TOPIC_PREFIX + "iot/v1/#", 1);
        client.subscribe(SHARED_SUBSCRIPTION_PREFIX + TOPIC_PREFIX + "sys/#", 1);
        log.info("mqtt订阅了设备信息和物模型主题");
    }

    /**
     * 消息回调方法
     *
     * @param topic       主题
     * @param mqttMessage 消息体
     */
    @Async("emqAsync")
    public void subscribeCallback(String topic, MqttMessage mqttMessage) {
        try {
            // 获取原始字节数组
            byte[] payload = mqttMessage.getPayload();

            // 尝试按不同格式解码消息
            String message;

            if (JsonUtil.isJsonFormat(payload)) {
                // 按UTF-8解码为JSON字符串
                message = new String(payload, StandardCharsets.UTF_8);
                log.info("topic:{},接收到 JSON 格式消息：{}", topic, message);
                // 处理JSON格式的数据
                processJsonMessage(topic, message);
            } else if (isStringFormat(payload)) {
                // 按UTF-8解码为普通字符串
                message = new String(payload, StandardCharsets.UTF_8);
                log.info("topic:{},接收到字符串格式消息：{}", topic, message);
                // 处理字符串格式的数据
                processStringMessage(topic, message);
            } else {
                // 将字节数组转换为十六进制字符串
                message = bytesToHex(payload);
                log.info("topic:{},接收到十六进制格式消息：{}", topic, message);
                // 处理十六进制格式的数据
                processHexMessage(topic, message);
            }
        } catch (IllegalArgumentException e) {
            log.error("接收到无效的 UTF-8 字符，主题：{}，错误：{}", topic, e.getMessage());
        } catch (Exception e) {
            log.error("topic:{},接收消息时出错", topic, e);
        }
    }

    // 判断是否为JSON格式
    private boolean isJsonFormat(byte[] payload) {
        try {
            String str = new String(payload, StandardCharsets.UTF_8);
            // 使用 Jackson 或 Gson 解析 JSON
            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.readTree(str);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    // 判断是否为字符串格式
    private boolean isStringFormat(byte[] payload) {
        try {
            String str = new String(payload, StandardCharsets.UTF_8);
            // 检查字符串是否包含非可见字符
            if (str.matches("^\\p{Print}+$")) {
                return true;
            } else {
                return false;
            }
        } catch (Exception e) {
            return false;
        }
    }

    // 处理JSON格式消息
    private void processJsonMessage(String topic, String message) {
        try {
            log.debug("处理JSON格式消息: topic={}, message={}", topic, message);

            // 验证topic格式并解析参数
            TopicInfo topicInfo = parseTopicInfo(topic);
            if (topicInfo == null) {
                return; // 错误日志已在parseTopicInfo中打印
            }

            // 根据topic类型处理消息
            if (topic.contains("/event/property/post")) {
                log.debug("处理属性上报消息: productKey={}, deviceKey={}", topicInfo.productKey, topicInfo.deviceKey);
                deviceMessageService.reportDeviceData(topicInfo.productKey, topicInfo.deviceKey, message);
            } else if (topic.contains("/iot/v1")) {
                log.debug("处理IoT v1消息");
                deviceMessageService.reportDeviceData(message);
            } else if (topic.contains("/thing/shadow/update")) {
                log.debug("处理影子更新消息: productKey={}, deviceKey={}", topicInfo.productKey, topicInfo.deviceKey);
                deviceMessageService.reportShadowData(topicInfo.productKey, topicInfo.deviceKey, message);
            } else {
                log.warn("未知的topic类型: {}", topic);
            }
        } catch (Exception e) {
            log.error("处理JSON格式消息异常: topic={}, 错误: {}", topic, e.getMessage(), e);
        }
    }

    // 处理字符串格式消息
    private void processStringMessage(String topic, String message) {
        try {
            log.debug("处理字符串格式消息: topic={}, message={}", topic, message);

            // 验证topic格式并解析参数
            TopicInfo topicInfo = parseTopicInfo(topic);
            if (topicInfo == null) {
                return; // 错误日志已在parseTopicInfo中打印
            }

            // 根据topic类型处理消息
            if (topic.contains("/event/property/post")) {
                log.debug("处理属性上报消息: productKey={}, deviceKey={}", topicInfo.productKey, topicInfo.deviceKey);
                deviceMessageService.reportDeviceData(topicInfo.productKey, topicInfo.deviceKey, message);
            } else if (topic.contains("/iot/v1")) {
                log.debug("处理IoT v1消息");
                deviceMessageService.reportDeviceData(message);
            } else if (topic.contains("/thing/shadow/update")) {
                log.debug("处理影子更新消息: productKey={}, deviceKey={}", topicInfo.productKey, topicInfo.deviceKey);
                deviceMessageService.reportShadowData(topicInfo.productKey, topicInfo.deviceKey, message);
            } else {
                log.warn("未知的topic类型: {}", topic);
            }
        } catch (Exception e) {
            log.error("处理字符串格式消息异常: topic={}, 错误: {}", topic, e.getMessage(), e);
        }
    }

    // 处理十六进制格式消息
    private void processHexMessage(String topic, String message) {
        try {
            log.debug("处理十六进制格式消息: topic={}, message={}", topic, message);

            // 验证topic格式并解析参数
            TopicInfo topicInfo = parseTopicInfo(topic);
            if (topicInfo == null) {
                return; // 错误日志已在parseTopicInfo中打印
            }

            // 根据topic类型处理消息
            if (topic.contains("/event/property/post")) {
                log.debug("处理属性上报消息: productKey={}, deviceKey={}", topicInfo.productKey, topicInfo.deviceKey);
                deviceMessageService.reportDeviceData(topicInfo.productKey, topicInfo.deviceKey, message);
            } else if (topic.contains("/iot/v1")) {
                log.debug("处理IoT v1消息");
                deviceMessageService.reportDeviceData(message);
            } else if (topic.contains("/thing/shadow/update")) {
                log.debug("处理影子更新消息: productKey={}, deviceKey={}", topicInfo.productKey, topicInfo.deviceKey);
                deviceMessageService.reportShadowData(topicInfo.productKey, topicInfo.deviceKey, message);
            } else {
                log.warn("未知的topic类型: {}", topic);
            }
        } catch (Exception e) {
            log.error("处理十六进制格式消息异常: topic={}, 错误: {}", topic, e.getMessage(), e);
        }
    }

    // 辅助方法：将字节数组转换为十六进制字符串
    private String bytesToHex(byte[] bytes) {
        StringBuilder sb = new StringBuilder(bytes.length * 2);
        for (byte b : bytes) {
            sb.append(String.format("%02x", b & 0xFF));
        }
        return sb.toString();
    }

}
