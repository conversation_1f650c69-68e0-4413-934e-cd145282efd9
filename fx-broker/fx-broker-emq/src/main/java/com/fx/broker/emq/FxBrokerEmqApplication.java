package com.fx.broker.emq;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * Broker emq
 *
 * <AUTHOR>
 */
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class, HibernateJpaAutoConfiguration.class}, scanBasePackages = "com.fx")
@EnableFeignClients(basePackages = {"com.fx.link.api"})
public class FxBrokerEmqApplication {

    public static void main(String[] args) {
        SpringApplication.run(FxBrokerEmqApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  Broker-Emq 模块启动成功   ლ(´ڡ`ლ)ﾞ  ");
    }

}
