package com.fx.broker.emq.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fx.broker.emq.service.DeviceMessageService;
import com.fx.broker.emq.service.IConverter;
import com.fx.common.core.constant.IotConstant;
import com.fx.common.core.domain.R;
import com.fx.common.core.domain.ThingModelMessage;
import com.fx.common.core.utils.JsonUtil;
import com.fx.common.core.utils.StringUtils;
import com.fx.common.iot.util.UniqueIdUtil;
import com.fx.common.redis.service.RedisService;
import com.fx.common.rocketmq.constant.ConsumerTopicConstant;
import com.fx.link.api.RemoteDeviceService;
import com.fx.link.api.RemoteProtocolService;
import com.fx.link.api.domain.device.model.DeviceInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 设备服务 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class DeviceMessageServiceImpl implements DeviceMessageService {

    public static final String TIME = "time";
    public static final String DEVICES = "devices";
    public static final String IDENTITY = "identity";
    public static final String TEMPLATE = "template";
    public static final String DEVICE = "device";
    public static final String DEVICE_KEY = "deviceKey";
    public static final String DEVICE_NO = "deviceNo";
    public static final String DATA = "data";

    @Resource
    private RocketMQTemplate rocketMQTemplate;

    @Resource
    private RedisService redisService;

    @Resource
    private IConverter iConverter;

    @Resource
    private RemoteDeviceService remoteDeviceService;

    @Resource
    private RemoteProtocolService remoteProtocolService;

    /**
     * 获取报告时间的时间戳（毫秒级）。
     *
     * @param dataMap 包含时间字段的映射
     * @return 时间戳（毫秒级）
     */
    public static Long getReportingTime(Map<String, Object> dataMap) {
        // 从 map 中获取时间字段
        Object timeObj = dataMap.get(TIME);

        if (timeObj == null) {
            // 如果没有时间字段，返回当前时间的时间戳
            log.warn("数据中缺少时间字段，使用当前时间。");
            return System.currentTimeMillis();
        }

        Date time;

        if (timeObj instanceof Number) {
            // 如果时间字段是数字类型
            long timestamp = ((Number) timeObj).longValue();
            int length = String.valueOf(timestamp).length();
            if (length == 10) {
                // 10位的秒级时间戳，转换为毫秒级
                time = new Date(timestamp * 1000);
                log.debug("解析到10位秒级时间戳: {}", timestamp);
            } else if (length == 13) {
                // 13位的毫秒级时间戳
                time = new Date(timestamp);
                log.debug("解析到13位毫秒级时间戳: {}", timestamp);
            } else {
                // 无法识别的数字长度，使用当前时间
                log.warn("无法识别的时间戳长度（{} 位），使用当前时间。", length);
                time = new Date();
            }
        } else if (timeObj instanceof String) {
            // 如果时间字段是字符串类型
            String dataTime = (String) timeObj;
            try {
                if (dataTime.matches("^\\d{10}$")) {
                    // 10位的秒级时间戳
                    long timestamp = Long.parseLong(dataTime) * 1000;
                    time = new Date(timestamp);
                    log.debug("解析到10位秒级时间戳字符串: {}", dataTime);
                } else if (dataTime.matches("^\\d{13}$")) {
                    // 13位的毫秒级时间戳
                    long timestamp = Long.parseLong(dataTime);
                    time = new Date(timestamp);
                    log.debug("解析到13位毫秒级时间戳字符串: {}", dataTime);
                } else {
                    // 解析为日期字符串（使用 Hutool 的 DateUtil）
                    time = DateUtil.parse(dataTime);
                    log.debug("解析到日期字符串: {}", dataTime);
                }
            } catch (NumberFormatException e) {
                // 捕获数字格式异常，使用当前时间
                log.warn("无法解析时间戳字符串: {}，使用当前时间。", dataTime, e);
                time = new Date();
            } catch (Exception e) {
                // 捕获其他异常，如日期解析异常，使用当前时间
                log.warn("解析日期字符串时出错: {}，使用当前时间。", dataTime, e);
                time = new Date();
            }
        } else if (timeObj instanceof Date) {
            // 如果时间字段是 Date 类型
            time = (Date) timeObj;
            log.debug("直接获取到 Date 对象: {}", time);
        } else {
            // 其他类型，使用当前时间
            log.warn("未知类型的时间字段（类型：{}），使用当前时间。", timeObj.getClass().getName());
            time = new Date();
        }

        // 返回时间戳（毫秒级）
        return time.getTime();
    }

    /**
     * 获取设备信息并进行验证
     *
     * @param deviceKey 设备Key
     * @return 设备信息，如果获取失败返回null
     */
    private DeviceInfo getDeviceInfoWithValidation(String deviceKey) {
        try {
            log.debug("开始获取设备信息: deviceKey={}", deviceKey);

            // 从远程服务获取设备信息
            R<DeviceInfo> deviceInfoResult = remoteDeviceService.getDeviceInfoByKey(deviceKey);

            if (deviceInfoResult == null) {
                log.error("获取设备信息失败，远程调用返回null: deviceKey={}", deviceKey);
                return null;
            }

            if (deviceInfoResult.getData() == null) {
                log.error("无此设备或设备信息为空: deviceKey={}, resultCode={}, resultMsg={}",
                        deviceKey, deviceInfoResult.getCode(), deviceInfoResult.getMsg());
                return null;
            }

            DeviceInfo deviceInfo = deviceInfoResult.getData();

            // 验证设备信息的完整性
            if (StringUtils.isEmpty(deviceInfo.getDeviceKey())) {
                log.error("设备信息异常，deviceKey为空: originalDeviceKey={}, deviceNo={}, productKey={}",
                        deviceKey, deviceInfo.getDeviceNo(), deviceInfo.getProductKey());
                return null;
            }

            if (StringUtils.isEmpty(deviceInfo.getConvertKey())) {
                log.warn("设备转换key为空: deviceKey={}, deviceNo={}, productKey={}",
                        deviceInfo.getDeviceKey(), deviceInfo.getDeviceNo(), deviceInfo.getProductKey());
            }

            log.debug("成功获取设备信息: deviceKey={}, deviceNo={}, productKey={}, convertKey={}",
                    deviceInfo.getDeviceKey(), deviceInfo.getDeviceNo(), deviceInfo.getProductKey(),
                    deviceInfo.getConvertKey());

            return deviceInfo;

        } catch (Exception e) {
            log.error("获取设备信息异常: deviceKey={}, 错误: {}", deviceKey, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 获取数据转换脚本并进行验证
     *
     * @param deviceInfo 设备信息
     * @return 转换脚本，如果获取失败返回null
     */
    private String getConvertScriptWithValidation(DeviceInfo deviceInfo) {
        try {
            if (StringUtils.isEmpty(deviceInfo.getConvertKey())) {
                log.error("设备转换key为空，无法获取转换脚本: deviceKey={}, deviceNo={}, productKey={}",
                        deviceInfo.getDeviceKey(), deviceInfo.getDeviceNo(), deviceInfo.getProductKey());
                return null;
            }

            // 通过 link 模块接口获取脚本
            log.debug("通过link模块接口获取转换脚本: deviceKey={}, convertKey={}",
                    deviceInfo.getDeviceKey(), deviceInfo.getConvertKey());

            R<String> scriptResult = remoteProtocolService.getScriptByConvertKey(deviceInfo.getConvertKey());

            if (scriptResult == null) {
                log.error("从link模块获取转换脚本失败，远程调用返回null: deviceKey={}, convertKey={}",
                        deviceInfo.getDeviceKey(), deviceInfo.getConvertKey());
                return null;
            }

            if (scriptResult.getData() == null) {
                log.error(
                        "设备无数据转换脚本: deviceKey={}, deviceNo={}, productKey={}, convertKey={}, resultCode={}, resultMsg={}",
                        deviceInfo.getDeviceKey(), deviceInfo.getDeviceNo(), deviceInfo.getProductKey(),
                        deviceInfo.getConvertKey(), scriptResult.getCode(), scriptResult.getMsg());
                return null;
            }

            String convertJsScript = scriptResult.getData();

            if (StringUtils.isEmpty(convertJsScript)) {
                log.error("从link模块获取的转换脚本为空: deviceKey={}, convertKey={}",
                        deviceInfo.getDeviceKey(), deviceInfo.getConvertKey());
                return null;
            }

            log.debug("成功获取转换脚本: deviceKey={}, convertKey={}, 脚本长度={}",
                    deviceInfo.getDeviceKey(), deviceInfo.getConvertKey(), convertJsScript.length());

            return convertJsScript;

        } catch (Exception e) {
            log.error("获取转换脚本异常: deviceKey={}, convertKey={}, 错误: {}",
                    deviceInfo.getDeviceKey(), deviceInfo.getConvertKey(), e.getMessage(), e);
            return null;
        }
    }

    @Override
    public void reportDeviceData(String productKey, String deviceKey, String message) {
        // 参数验证
        if (StringUtils.isEmpty(productKey) || StringUtils.isEmpty(deviceKey)) {
            log.error("参数错误: productKey={}, deviceKey={}", productKey, deviceKey);
            return;
        }

        log.debug("开始处理设备消息: productKey={}, deviceKey={}, message={}", productKey, deviceKey, message);

        Map<String, Object> dataMap = new HashMap<>();

        // 1. 判断消息是否为 JSON 格式，如果是则解析为 Map，否则直接存入 dataMap
        if (JsonUtil.isJsonFormat(message)) {
            dataMap = JsonUtil.parseObject(message);
        } else {
            dataMap.put(DATA, message);
        }

        // 2. 通过link模块获取设备信息
        DeviceInfo deviceInfo = getDeviceInfoWithValidation(deviceKey);
        if (deviceInfo == null) {
            return; // 错误日志已在方法内部打印
        }

        // 如果是网关设备，添加 gatewayNo 到 dataMap
        if (IotConstant.DEVICE_TYPE_GATEWAY.equals(deviceInfo.getDeviceType())) {
            dataMap.put("gatewayNo", deviceInfo.getDeviceNo());
        }

        // 3. 构建物模型消息
        ThingModelMessage thingModelMessage = createThingModelMessage(productKey, deviceInfo,
                ThingModelMessage.ID_PROPERTY_POST, ThingModelMessage.TYPE_PROPERTY, System.currentTimeMillis());

        // 4. 获取数据转换脚本并进行数据转换
        String convertJsScript = getConvertScriptWithValidation(deviceInfo);
        if (StringUtils.isEmpty(convertJsScript)) {
            return; // 错误日志已在方法内部打印
        }

        iConverter.setScript(convertJsScript);
        List<Map<String, Object>> dataList = iConverter.decode(dataMap);
        if (dataList == null || dataList.isEmpty()) {
            log.error("解析数据为空, deviceNo：{}, deviceKey：{}", deviceInfo.getDeviceNo(), deviceInfo.getDeviceKey());
            return;
        } else {
            log.info("deviceNo：{}, deviceKey：{} ,解析数据: {}", deviceInfo.getDeviceNo(), deviceInfo.getDeviceKey(),
                    JSON.toJSONString(dataList));
        }

        // 5. 处理消息回调
        handleMessageCallback(productKey, deviceKey, deviceInfo, dataMap, thingModelMessage.getOccurred());

        // 6. 根据设备类型处理不同的设备数据
        switch (deviceInfo.getDeviceType()) {
            case IotConstant.DEVICE_TYPE_GATEWAY:
                processGatewayDeviceData(dataList, deviceInfo);
                break;
            case IotConstant.OLD_DEVICE_TYPE_GATEWAY:
                processOldGatewayDeviceData(dataList, thingModelMessage, deviceInfo);
                break;
            default:
                processDirectDeviceData(dataList, thingModelMessage, deviceInfo);
                break;
        }
    }

    /**
     * 创建一个 ThingModelMessage 对象，并初始化基本信息。
     *
     * @param productKey 产品密钥
     * @param deviceInfo 设备信息
     * @param identifier 消息标识符
     * @param type       消息类型
     * @param timestamp  时间戳
     * @return 初始化后的 ThingModelMessage 对象
     */
    private ThingModelMessage createThingModelMessage(String productKey, DeviceInfo deviceInfo, String identifier,
            String type, long timestamp) {
        ThingModelMessage message = new ThingModelMessage();
        message.setProductKey(productKey);
        message.setDeviceKey(deviceInfo.getDeviceKey());
        message.setDeviceNo(deviceInfo.getDeviceNo());
        message.setProjectId(deviceInfo.getProjectId());
        message.setType(type);
        message.setIdentifier(identifier);
        message.setOccurred(timestamp);
        message.setTime(timestamp);
        message.setDeviceName(deviceInfo.getDeviceName());
        message.setUid(deviceInfo.getUid());
        return message;
    }

    /**
     * 处理消息回调逻辑。
     *
     * @param productKey   产品密钥
     * @param deviceKey    设备密钥
     * @param deviceInfo   设备信息
     * @param dataMap      数据映射
     * @param occurredTime 发生时间
     */
    private void handleMessageCallback(String productKey, String deviceKey, DeviceInfo deviceInfo,
            Map<String, Object> dataMap, long occurredTime) {
        Map<String, Object> reMap = iConverter.encode(dataMap);
        if (reMap != null && !reMap.isEmpty()) {
            log.info("deviceKey:{}, 消息回调: {}", deviceKey, JSON.toJSONString(reMap));
            ThingModelMessage replyMessage = createThingModelMessage(productKey, deviceInfo,
                    ThingModelMessage.ID_PROPERTY_POST_REPLY, ThingModelMessage.TYPE_PROPERTY, occurredTime);
            replyMessage.setData(reMap.get(DATA));
            replyMessage.setMid(UniqueIdUtil.newRequestId());
            rocketMQTemplate.convertAndSend(ConsumerTopicConstant.FX_LINKS_DEVICE_POST_REPLY, replyMessage);
        }
    }

    /**
     * 处理网关设备的数据。
     *
     * @param dataList   数据列表
     * @param deviceInfo 设备信息
     */
    private void processGatewayDeviceData(List<Map<String, Object>> dataList, DeviceInfo deviceInfo) {
        for (Map<String, Object> deviceDataMap : dataList) {
            String subDeviceKey = String.valueOf(deviceDataMap.get(DEVICE_KEY));
            R<DeviceInfo> subDeviceInfoResult = remoteDeviceService.getDeviceInfoByKey(subDeviceKey);
            if (subDeviceInfoResult == null || subDeviceInfoResult.getData() == null) {
                log.error("无此网关子设备, deviceKey:{}", subDeviceKey);
                continue;
            }
            DeviceInfo subDeviceInfo = subDeviceInfoResult.getData();

            Object dataObj = deviceDataMap.get(DATA);
            if (!(dataObj instanceof Map)) {
                log.error("网关子设备数据为空或格式错误, deviceKey:{}", subDeviceKey);
                continue;
            }

            @SuppressWarnings("unchecked")
            Map<String, Object> data = (Map<String, Object>) dataObj;
            data.computeIfAbsent(DEVICE_KEY, k -> subDeviceInfo.getDeviceKey());
            data.computeIfAbsent(DEVICE_NO, k -> subDeviceInfo.getDeviceNo());

            long currentTime = System.currentTimeMillis();
            ThingModelMessage subThingModelMessage = createThingModelMessage(subDeviceInfo.getProductKey(),
                    subDeviceInfo, ThingModelMessage.ID_PROPERTY_POST, ThingModelMessage.TYPE_PROPERTY, currentTime);
            subThingModelMessage.setData(data);
            subThingModelMessage.setMid(UniqueIdUtil.newRequestId());

            // 如果数据中包含时间，则设置报告时间
            if (deviceDataMap.containsKey(TIME) && deviceDataMap.get(TIME) != null) {
                subThingModelMessage.setTime(getReportingTime(deviceDataMap));
            }

            log.info("网关：{}, 网关子设备数据: {}", deviceInfo.getDeviceKey(), JSON.toJSONString(deviceDataMap));
            sendThingModelMessage(subThingModelMessage);
            sendDeviceStateMessage(subThingModelMessage);
        }
        // 更新网关状态
        long currentTime = System.currentTimeMillis();
        ThingModelMessage gatewayThingModelMessage = createThingModelMessage(deviceInfo.getProductKey(), deviceInfo,
                ThingModelMessage.ID_PROPERTY_POST, ThingModelMessage.TYPE_PROPERTY, currentTime);
        gatewayThingModelMessage.setMid(UniqueIdUtil.newRequestId());
        sendDeviceStateMessage(gatewayThingModelMessage);
    }

    /**
     * 处理传统网关设备的数据。
     *
     * @param dataList      数据列表
     * @param thingModelMsg 物模型消息
     * @param deviceInfo    设备信息
     */
    private void processOldGatewayDeviceData(List<Map<String, Object>> dataList, ThingModelMessage thingModelMsg,
            DeviceInfo deviceInfo) {
        for (Map<String, Object> dataOldDeviceMap : dataList) {
            thingModelMsg.setDeviceKey(deviceInfo.getDeviceKey());
            thingModelMsg.setMid(UniqueIdUtil.newRequestId());
            thingModelMsg.setDeviceName(deviceInfo.getDeviceName());
            thingModelMsg.setUid(deviceInfo.getUid());
            thingModelMsg.setData(dataOldDeviceMap);

            if (dataOldDeviceMap.containsKey(TIME) && dataOldDeviceMap.get(TIME) != null) {
                thingModelMsg.setTime(getReportingTime(dataOldDeviceMap));
            }

            sendThingModelMessage(thingModelMsg);
            sendDeviceStateMessage(thingModelMsg);
        }
    }

    /**
     * 处理直连设备的数据。
     *
     * @param dataList      数据列表
     * @param thingModelMsg 物模型消息
     * @param deviceInfo    设备信息
     */
    private void processDirectDeviceData(List<Map<String, Object>> dataList, ThingModelMessage thingModelMsg,
            DeviceInfo deviceInfo) {
        if (dataList.isEmpty()) {
            log.error("直连设备数据列表为空, deviceKey:{}", deviceInfo.getDeviceKey());
            return;
        }

        Map<String, Object> firstDataMap = dataList.get(0);
        Object dataObj = firstDataMap.get(DATA);
        if (!(dataObj instanceof Map)) {
            log.error("直连设备数据格式错误, deviceKey:{}", deviceInfo.getDeviceKey());
            return;
        }

        @SuppressWarnings("unchecked")
        Map<String, Object> data = (Map<String, Object>) dataObj;
        data.computeIfAbsent(DEVICE_KEY, k -> deviceInfo.getDeviceKey());
        data.computeIfAbsent(DEVICE_NO, k -> deviceInfo.getDeviceNo());

        thingModelMsg.setData(data);
        if (firstDataMap.containsKey(TIME) && firstDataMap.get(TIME) != null) {
            thingModelMsg.setTime(getReportingTime(firstDataMap));
        }
        thingModelMsg.setMid(UniqueIdUtil.newRequestId());

        sendThingModelMessage(thingModelMsg);
        sendDeviceStateMessage(thingModelMsg);
    }

    /**
     * 发送物模型消息
     *
     * @param thingModelMessage 物模型消息
     */
    private void sendThingModelMessage(ThingModelMessage thingModelMessage) {
        rocketMQTemplate.convertAndSend(ConsumerTopicConstant.FX_THING_MODEL_MSG, thingModelMessage);
    }

    /**
     * 设备状态消息
     *
     * @param thingModelMessage 设备数据
     */
    private void sendDeviceStateMessage(ThingModelMessage thingModelMessage) {
        ThingModelMessage thingModelMessageState = new ThingModelMessage();
        BeanUtil.copyProperties(thingModelMessage, thingModelMessageState);
        thingModelMessageState.setType(ThingModelMessage.TYPE_STATE);
        thingModelMessageState.setIdentifier(ThingModelMessage.ID_TYPE_STATE_ONLINE);
        rocketMQTemplate.convertAndSend(ConsumerTopicConstant.FX_LINKS_DEVICE_STATE, thingModelMessageState);
    }

    @Override
    public void reportDeviceData(String message) {
        Map<String, Object> dataMap = JsonUtil.parseObject(message);
        JSONArray devices = JSONArray.parseArray(String.valueOf(dataMap.get(DEVICES)));
        JSONObject jsonObject = devices.getJSONObject(0);
        JSONObject identity = jsonObject.getJSONObject(IDENTITY);
        // String productKey = identity.getString(TEMPLATE);
        String deviceKey = identity.getString(DEVICE);

        // 上报物模型消息以及设备数据
        R<DeviceInfo> deviceInfoResult = remoteDeviceService.getDeviceInfoByKey(deviceKey);
        if (deviceInfoResult == null || deviceInfoResult.getData() == null) {
            log.error("无此设备,deviceKey:{}", deviceKey);
            return;
        }
        DeviceInfo deviceInfo = deviceInfoResult.getData();

        // 消息转换 - 通过 link 模块接口获取脚本
        R<String> scriptResult = remoteProtocolService.getScriptByConvertKey(deviceInfo.getConvertKey());
        if (scriptResult == null || scriptResult.getData() == null) {
            log.error("设备无数据转换脚本,deviceKey:{}, convertKey:{}", deviceInfo.getDeviceKey(), deviceInfo.getConvertKey());
            return;
        }
        String convertJsScript = scriptResult.getData();
        iConverter.setScript(convertJsScript);
        List<Map<String, Object>> dataList = iConverter.decode(dataMap);
        if (dataList == null || dataList.isEmpty()) {
            log.error("解析数据为空,deviceKey:{}", deviceInfo.getDeviceKey());
            return;
        }

        // 直连设备
        ThingModelMessage thingModelMessage = new ThingModelMessage();
        thingModelMessage.setProductKey(deviceInfo.getProductKey());
        thingModelMessage.setDeviceKey(deviceInfo.getDeviceKey());
        thingModelMessage.setDeviceNo(deviceInfo.getDeviceNo());
        thingModelMessage.setProjectId(deviceInfo.getProjectId());
        thingModelMessage.setType(ThingModelMessage.TYPE_PROPERTY);
        thingModelMessage.setIdentifier(ThingModelMessage.ID_PROPERTY_POST);
        long time = System.currentTimeMillis();
        thingModelMessage.setOccurred(time);
        thingModelMessage.setTime(time);
        thingModelMessage.setData(dataMap);
        thingModelMessage.setMid(UniqueIdUtil.newRequestId());
        thingModelMessage.setDeviceName(deviceInfo.getDeviceName());
        thingModelMessage.setUid(deviceInfo.getUid());

        for (Map<String, Object> stringObjectMap : dataList) {
            stringObjectMap.computeIfAbsent(DEVICE_KEY, k -> deviceInfo.getDeviceKey());
            stringObjectMap.computeIfAbsent(DEVICE_NO, k -> deviceInfo.getDeviceNo());
            thingModelMessage.setData(stringObjectMap);
            if (stringObjectMap.containsKey(TIME) && stringObjectMap.get(TIME) != null) {
                thingModelMessage.setTime(getReportingTime(stringObjectMap));
            }
            log.info("thingModelMessage: {}", JSON.toJSONString(thingModelMessage));
            sendThingModelMessage(thingModelMessage);
            sendDeviceStateMessage(thingModelMessage);
        }
    }

    @Override
    public void reportShadowData(String productKey, String deviceKey, String message) {
        Map<String, Object> dataMap = new HashMap<>();
        // 1. 判断是否是 JSON 格式, 不是则直接存入 dataMap, 是则解析
        if (JsonUtil.isJsonFormat(message)) {
            dataMap = JsonUtil.parseObject(message);
        } else {
            dataMap.put(DATA, message);
        }
        // 2. 通过link模块获取设备信息
        R<DeviceInfo> deviceInfoResult = remoteDeviceService.getDeviceInfoByKey(deviceKey);
        if (deviceInfoResult == null || deviceInfoResult.getData() == null) {
            log.error("无此设备,deviceKey:{}", deviceKey);
            return;
        }
        DeviceInfo deviceInfo = deviceInfoResult.getData();

        ThingModelMessage thingModelMessage = new ThingModelMessage();
        thingModelMessage.setProductKey(productKey);
        thingModelMessage.setDeviceKey(deviceInfo.getDeviceKey());
        thingModelMessage.setDeviceNo(deviceInfo.getDeviceNo());
        thingModelMessage.setProjectId(deviceInfo.getProjectId());
        long timeMillis = System.currentTimeMillis();
        thingModelMessage.setOccurred(timeMillis);
        thingModelMessage.setTime(timeMillis);
        thingModelMessage.setData(dataMap);
        thingModelMessage.setMid(UniqueIdUtil.newRequestId());
        thingModelMessage.setDeviceName(deviceInfo.getDeviceName());
        thingModelMessage.setUid(deviceInfo.getUid());

        String method = dataMap.get("method") == null ? "" : String.valueOf(dataMap.get("method"));
        if (StringUtils.isNotEmpty(method) && ThingModelMessage.ID_SHADOW_GET.equals(method)) {
            // 3. 设备获取影子
            thingModelMessage.setType(ThingModelMessage.TYPE_SHADOW);
            thingModelMessage.setIdentifier(ThingModelMessage.ID_SHADOW_GET);
        } else {
            // 4. 设备上报设备影子
            thingModelMessage.setType(ThingModelMessage.TYPE_SHADOW);
            thingModelMessage.setIdentifier(ThingModelMessage.ID_SHADOW_UPDATE);
        }
        rocketMQTemplate.convertAndSend(ConsumerTopicConstant.FX_LINKS_DEVICE_SHADOW, thingModelMessage);
    }
}
