package com.fx.broker.emq.service.impl;

import com.fx.broker.emq.service.IConverter;
import com.fx.common.core.domain.ThingModelMessage;
import com.fx.common.core.utils.JsonUtil;
import com.fx.common.iot.domain.Device;
import com.fx.common.iot.domain.ThingService;
import com.fx.link.api.domain.device.message.DeviceMessage;
import jdk.nashorn.api.scripting.NashornScriptEngine;
import jdk.nashorn.api.scripting.ScriptObjectMirror;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.springframework.stereotype.Service;

import javax.script.ScriptEngineManager;
import javax.script.ScriptException;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 脚本转换器
 *
 * <AUTHOR>
 */
@Data
@Slf4j
@Service
public class ScriptConverter implements IConverter {
    private final NashornScriptEngine engine = (NashornScriptEngine) (new ScriptEngineManager())
            .getEngineByName("nashorn");

    private Object scriptObj;

    private static String escape(String script) {
        // 如果存在&gt; &lt;等特殊字符，需要转义
        if (script.contains("&gt;")) {
            script = script.replace("&gt;", ">");
        }
        if (script.contains("&lt;")) {
            script = script.replace("&lt;", "<");
        }
        return script;
    }

    @Override
    public void setScript(String script) {
        try {
            scriptObj = engine.eval(String.format("new (function () {\n%s})()", escape(script)));
        } catch (ScriptException e) {
            log.error("eval converter script error", e);
        }
    }

    @Override
    public ThingModelMessage decode(DeviceMessage msg) {
        try {
            Object rst = engine.invokeMethod(scriptObj, "decode", msg);
            if (rst instanceof ThingModelMessage) {
                return (ThingModelMessage) rst;
            }

            ScriptObjectMirror result = (ScriptObjectMirror) rst;
            ThingModelMessage modelMessage = new ThingModelMessage();
            BeanUtils.populate(modelMessage, result);
            return modelMessage;
        } catch (Throwable e) {
            log.error("execute decode script error", e);
        }
        return null;
    }

    @Override
    public List<Map<String, Object>> decode(Map<String, Object> msgMap) {
        try {
            Object rst = engine.invokeMethod(scriptObj, "decode", msgMap);
            ScriptObjectMirror result = (ScriptObjectMirror) rst;
            if (result == null || result.isEmpty()) {
                return Collections.emptyList();
            }
            return (List<Map<String, Object>>) JsonUtil.toObject(result);
        } catch (Throwable e) {
            log.error("execute decode script error", e);
        }
        return null;
    }

    @Override
    public DeviceMessage encode(ThingService<?> service, Device device) {
        try {
            ScriptObjectMirror result = (ScriptObjectMirror) engine.invokeMethod(scriptObj, "encode", service, device);
            Map map = (Map) JsonUtil.toObject(result);
            DeviceMessage message = new DeviceMessage();
            BeanUtils.populate(message, map);
            return message;
        } catch (Throwable e) {
            log.error("execute encode script error", e);
        }
        return null;
    }

    @Override
    public Map<String, Object> encode(Map<String, Object> msgMap) {
        try {
            Object rst = engine.invokeMethod(scriptObj, "encode", msgMap);
            ScriptObjectMirror result = (ScriptObjectMirror) rst;
            if (result == null) {
                return Collections.emptyMap();
            }
            return (Map<String, Object>) JsonUtil.toObject(result);
        } catch (Throwable e) {
            log.error("execute encode script error", e);
        }
        return Collections.emptyMap();
    }

    @Override
    public void putScriptEnv(String key, Object value) {
        engine.put(key, value);
    }
}
