package com.fx.broker.emq.service;

import com.fx.common.core.domain.ThingModelMessage;
import com.fx.common.iot.domain.Device;
import com.fx.common.iot.domain.ThingService;
import com.fx.link.api.domain.device.message.DeviceMessage;

import java.util.List;
import java.util.Map;

/**
 * 转换器接口
 * 
 * <AUTHOR>
 */
public interface IConverter {

    /**
     * 置要执行的JavaScript脚本
     * 
     * @param script 脚本
     */
    void setScript(String script);

    /**
     * 解码设备消息
     * 
     * @param msg 消息
     * @return 解码后的消息
     */
    ThingModelMessage decode(DeviceMessage msg);

    /**
     * 解码设备消息
     * 
     * @param msgMap 消息
     * @return 解码后的消息
     */
    List<Map<String, Object>> decode(Map<String, Object> msgMap);

    /**
     * 编码设备消息
     * 
     * @param service 服务
     * @param device  设备
     * @return 编码后的消息
     */
    DeviceMessage encode(ThingService<?> service, Device device);

    /**
     * 编码设备消息
     * 
     * @param msgMap 消息
     * @return 编码后的消息
     */
    Map<String, Object> encode(Map<String, Object> msgMap);

    /**
     * 向JavaScript环境中添加变量
     * 
     * @param key   变量名
     * @param value 变量值
     */
    void putScriptEnv(String key, Object value);
}
