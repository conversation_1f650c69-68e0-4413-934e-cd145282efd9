package com.fx.broker.emq.config;

import org.eclipse.paho.client.mqttv3.IMqttDeliveryToken;
import org.eclipse.paho.client.mqttv3.MqttCallbackExtended;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 * @Classname MqttCallback
 * @Description 消费监听类
 */
@Component
public class EmqxCallback implements MqttCallbackExtended {
    private static final Logger logger = LoggerFactory.getLogger(EmqxCallback.class);

    @Lazy
    @Autowired
    private EmqxService emqxService;

    @Override
    public void connectionLost(Throwable cause) {
        logger.error("MQTT 连接丢失，原因：{}", cause.getMessage(), cause);
    }

    @Override
    public void messageArrived(String topic, MqttMessage message) {
        try {
            emqxService.subscribeCallback(topic, message);
        } catch (IllegalArgumentException e) {
            logger.error("处理消息时发生非法参数异常，主题：{}，消息内容：{}，错误：{}", topic, new String(message.getPayload()), e.getMessage(), e);
            // 处理非法参数异常，例如记录日志或采取其他措施
        } catch (Exception e) {
            logger.error("处理消息时发生异常，主题：{}，消息内容：{}，错误：{}", topic, new String(message.getPayload()), e.getMessage(), e);
        }
    }

    /**
     * 发布消息后，到达MQTT服务器，服务器回调消息接收
     */
    @Override
    public void deliveryComplete(IMqttDeliveryToken iMqttDeliveryToken) {
        // 消息到达 MQTT 代理时触发的事件
    }

    /**
     * 监听mqtt连接消息
     */
    @Override
    public void connectComplete(boolean reconnect, String serverURI) {
        logger.info("MQTT 已连接到服务器：{}，是否重新连接：{}", serverURI, reconnect);
        try {
            emqxService.subscribe(EmqxClient.client);
        } catch (MqttException e) {
            logger.error("订阅主题失败，错误：{}", e.getMessage(), e);
        }
    }
}