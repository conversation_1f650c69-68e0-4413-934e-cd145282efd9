package com.fx.broker.emq.service;

/**
 * 设备服务 Service 接口
 * 
 * <AUTHOR>
 */
public interface DeviceMessageService {

    /**
     * 上报设备数据
     *
     * @param productKey 产品key
     * @param deviceKey  设备key
     * @param message    设备数据
     */
    void reportDeviceData(String productKey, String deviceKey, String message);

    /**
     * 上报设备数据-直连安科瑞
     *
     * @param message 设备数据
     */
    void reportDeviceData(String message);

    /**
     * 设备影子上报或获取数据
     *
     * @param productKey 产品key
     * @param deviceKey  设备key
     * @param message    设备数据
     */
    void reportShadowData(String productKey, String deviceKey, String message);
}
