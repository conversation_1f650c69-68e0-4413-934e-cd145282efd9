package com.fx.broker.emq.consumer;

import com.fx.broker.emq.config.EmqxClient;
import com.fx.common.core.domain.ThingModelMessage;
import com.fx.common.core.utils.JsonUtil;
import com.fx.common.rocketmq.constant.ConsumerGroupConstant;
import com.fx.common.rocketmq.constant.ConsumerTopicConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 监听设备影子上报主题响应
 *
 * <AUTHOR>
 * @since 2024-05-15
 */
@Slf4j
@Component
@RocketMQMessageListener(consumerGroup = ConsumerGroupConstant.FX_EMQ_DEVICE_SHADOW_REPLY_CONSUMER, topic = ConsumerTopicConstant.FX_LINKS_DEVICE_SHADOW_REPLY)
public class DeviceShadowReplyConsumer implements RocketMQListener<ThingModelMessage> {

    @Resource
    private EmqxClient emqxClient;

    @Override
    public void onMessage(ThingModelMessage thingModelMessage) {
        ///sys/YcpRRnAkz8pKx7Qi/66778899/thing/shadow/get
        if (thingModelMessage != null) {
            log.info("监听获取和上报设备影子主题响应，消息内容：{}", thingModelMessage);
            String shadowPropertyTopic = "/sys/" + thingModelMessage.getProductKey() + "/" + thingModelMessage.getDeviceNo() + "/thing/shadow/get";
            emqxClient.publish(1, false, shadowPropertyTopic, JsonUtil.toJsonString(thingModelMessage.getData()));
        }
    }
}