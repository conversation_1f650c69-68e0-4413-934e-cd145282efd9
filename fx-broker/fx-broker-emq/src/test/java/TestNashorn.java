import javax.script.Invocable;
import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;

public class TestNashorn {
    public static void main(String[] args) throws Exception {
        // 模拟你的 JSON
        String jsonString = 
            "{\"f\":\"rp\",\"d\":[" +
            "{\"sid\":\"ModbusRTU\",\"pid\":\"mKkd7DbGhSr72ADW_heating_set_temp\",\"v\":\"430\",\"s\":1735196761,\"ms\":686,\"e\":0}," +
            "{\"sid\":\"ModbusRTU\",\"pid\":\"mKkd7DbGhSr72ADW_cooling_pool_temp\",\"v\":\"221\",\"s\":1735196761,\"ms\":545,\"e\":0}," +
            "{\"sid\":\"ModbusRTU\",\"pid\":\"mKkd7DbGhSr72ADW_return_pool_temp\",\"v\":\"171\",\"s\":1735196761,\"ms\":545,\"e\":0}" +
            // 省略其他，为了示例
            "]}";

        // 你的脚本（合并 parse 逻辑 + decode 逻辑）
        String script = 
        "function decode(jsonString) {\n" +
        "    var msg = JSON.parse(jsonString); // 先转成对象\n" +
        "    var datas = msg.d;\n" +
        "    if (!datas || !Array.isArray(datas)) {\n" +
        "        return null;\n" +
        "    }\n" +
        "    var dataMap = {};\n" +
        "    datas.forEach(function(item){\n" +
        "        if (!item.pid || typeof item.pid !== 'string') {\n" +
        "            return;\n" +
        "        }\n" +
        "        if (item.pid.indexOf('_')===-1){\n" +
        "            return;\n" +
        "        }\n" +
        "        var pidSplit = item.pid.split('_');\n" +
        "        var deviceKey = pidSplit.shift();\n" +
        "        var paramName = pidSplit.join('_');\n" +
        "        var valueNum = parseFloat(item.v);\n" +
        "        if (isNaN(valueNum)) {\n" +
        "            return;\n" +
        "        }\n" +
        "        // 按需 /10\n" +
        "        switch (paramName) {\n" +
        "            case 'cooling_pool_temp':\n" +
        "            case 'return_pool_temp':\n" +
        "            case 'heating_set_temp':\n" +
        "            case 'water_tank_in_temp':\n" +
        "            case 'unit_heating_temp':\n" +
        "            case 'compressor_current':\n" +
        "                valueNum = valueNum / 10;\n" +
        "                break;\n" +
        "        }\n" +
        "        if (!dataMap[deviceKey]) {\n" +
        "            dataMap[deviceKey] = { deviceKey: deviceKey, data: {} };\n" +
        "        }\n" +
        "        dataMap[deviceKey].data[paramName] = valueNum;\n" +
        "        dataMap[deviceKey].data['time'] = item.s;\n" +
        "    });\n" +
        "    var resultDatas = [];\n" +
        "    for (var k in dataMap) {\n" +
        "        if (dataMap.hasOwnProperty(k)) {\n" +
        "            resultDatas.push(dataMap[k]);\n" +
        "        }\n" +
        "    }\n" +
        "    return resultDatas.length>0 ? resultDatas : null;\n" +
        "}";

        // 初始化 Nashorn
        ScriptEngine engine = new ScriptEngineManager().getEngineByName("nashorn");
        // 加载脚本
        engine.eval(script);

        // 调用 decode
        Invocable invocable = (Invocable) engine;
        Object result = invocable.invokeFunction("decode", jsonString);

        // 打印看下
        System.out.println("==== decode result ====");
        System.out.println(result);
    }
}
