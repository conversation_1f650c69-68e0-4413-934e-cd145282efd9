import com.fx.common.core.utils.JsonUtil;
import jdk.nashorn.api.scripting.NashornScriptEngine;
import jdk.nashorn.api.scripting.ScriptObjectMirror;
import org.junit.Test;

import javax.script.ScriptEngineManager;
import javax.script.ScriptException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/6/6 下午7:12
 */
public class ScriptConverterTest {
    private final NashornScriptEngine engine = (NashornScriptEngine) (new ScriptEngineManager()).getEngineByName("nashorn");

    @Test
    public void testMainMethod() throws ScriptException, NoSuchMethodException {
        NashornScriptEngine engine = (NashornScriptEngine) (new ScriptEngineManager()).getEngineByName("nashorn");
        String script = "// 16进制减去 33H,两个字节顺序从后往前\n" +
                "function parseData(datas, start, size) {\n" +
                "    var data = '';\n" +
                "    for (var i = start + size - 2; i >= start; i -= 2) {\n" +
                "        data += padZero((parseInt(datas.slice(i, i + 2), 16) - 0x33).toString(16), 2);\n" +
                "    }\n" +
                "    return data;\n" +
                "}\n" +
                "\n" +
                "function hexToAscii(hexString) {\n" +
                "    var asciiString = '';\n" +
                "    for (var i = 0; i < hexString.length; i += 2) {\n" +
                "        asciiString += String.fromCharCode(parseInt(hexString.substr(i, 2), 16));\n" +
                "    }\n" +
                "    return asciiString;\n" +
                "}\n" +
                "\n" +
                "// 补零\n" +
                "function padZero(num, size) {\n" +
                "    var s = num + \"\";\n" +
                "    while (s.length < size) s = \"0\" + s;\n" +
                "    return s;\n" +
                "}\n" +
                "\n" +
                "// 解析电能数据  datas:数据  start:开始位置  size:数据长度  point:小数点位置\n" +
                "function parseEnergyDataBySlice(datas, start, size, point) {\n" +
                "    var data = datas.slice(start, start + size);\n" +
                "    if (size === 8) {\n" +
                "        data = parseFloat((parseInt(datas.slice(start + 6, start + 8), 16) - 0x33).toString(16) +\n" +
                "            (parseInt(datas.slice(start + 4, start + 6), 16) - 0x33).toString(16) +\n" +
                "            (parseInt(datas.slice(start + 2, start + 4), 16) - 0x33).toString(16) + '.' +\n" +
                "            (parseInt(datas.slice(start, start + 2), 16) - 0x33).toString(16)).toString();\n" +
                "    } else if (size === 4) {\n" +
                "        var data1 = (parseInt(datas.slice(start + 2, start + 4), 16) - 0x33).toString(16) +\n" +
                "            (parseInt(datas.slice(start, start + 2), 16) - 0x33).toString(16);\n" +
                "        //在第三位后加小数点\n" +
                "        data = parseFloat(data1.slice(0, point) + '.' + data1.slice(point)).toString();\n" +
                "    } else if (size === 6) {\n" +
                "        var data1 = (parseInt(datas.slice(start + 4, start + 6), 16) - 0x33).toString(16) +\n" +
                "            (parseInt(datas.slice(start + 2, start + 4), 16) - 0x33).toString(16) +\n" +
                "            (parseInt(datas.slice(start, start + 2), 16) - 0x33).toString(16);\n" +
                "        //在第三位后加小数点\n" +
                "        data = parseFloat(data1.slice(0, point) + '.' + data1.slice(point)).toString();\n" +
                "    }\n" +
                "    return data;\n" +
                "}\n" +
                "\n" +
                "//数据解析\n" +
                "this.decode = function (msg) {\n" +
                "    var resultDatas = [];\n" +
                "    var datas = msg.data;\n" +
                "    // 帧总长度 00 EA\n" +
                "    var frameLength = datas.slice(0, 4);\n" +
                "    // 帧起始符 68\n" +
                "    var frameStart = datas.slice(4, 6);\n" +
                "    // 模块地址 135792468417  网关地址 135792468417\n" +
                "    var moduleAddress = datas.slice(16, 18) + datas.slice(14, 16) + datas.slice(12, 14) + datas.slice(10, 12) + datas.slice(8, 10) + datas.slice(6, 8);\n" +
                "    // 帧起始符 68\n" +
                "    var frameStart1 = datas.slice(18, 20);\n" +
                "    // 控制码 22\n" +
                "    var controlCode = datas.slice(20, 22);\n" +
                "    // 数据域长度 00 DD\n" +
                "    var dataLength = datas.slice(22, 26);\n" +
                "    // 周期上报标识 16进制减去 33H  原数据 37 33 B7 33   转换为 00840004\n" +
                "    var periodReport = parseData(datas,26,8);\n" +
                "    if (periodReport === '00840004') {\n" +
                "        // 15分钟上报\n" +
                "        // 第一块表数据总长度 33 0A\n" +
                "        var firstDataLength = datas.slice(34, 38);\n" +
                "        //第一块表地址 8C B5 79 C5 8A 46  135792468259\n" +
                "        var firstDataAddress = parseData(datas.slice(38, 12));\n" +
                "        // 数据项长度\n" +
                "        var dataItemLength = datas.slice(50, 52);\n" +
                "        // 数据标识 8个字节 55 33 B6 33 将十六进制的字符串转换为十进制的数字，然后减去33，再将结果转换为十六进制的字符串\n" +
                "        var dataFlag = parseData(datas, 52, 8);\n" +
                "        // 时间 10 个字节\n" +
                "        var time = '20' + padZero((parseInt(datas.slice(68, 70), 16) - 0x33).toString(16), 2) + '-' +\n" +
                "            padZero((parseInt(datas.slice(66, 68), 16) - 0x33).toString(16), 2) + '-' +\n" +
                "            padZero((parseInt(datas.slice(64, 66), 16) - 0x33).toString(16), 2) + ' ' +\n" +
                "            padZero((parseInt(datas.slice(62, 64), 16) - 0x33).toString(16), 2) + ':' +\n" +
                "            padZero((parseInt(datas.slice(60, 62), 16) - 0x33).toString(16), 2) + ':00';\n" +
                "        //转为时间戳\n" +
                "        var timestamp = new Date(time).getTime().toString();\n" +
                "\n" +
                "        // 正向有功总电能 8个字节\n" +
                "        // var forwardActiveTotalEnergy = datas.slice(70, 78);\n" +
                "        var forwardActiveTotalEnergy = parseEnergyDataBySlice(datas, 70, 8, 6);\n" +
                "        // 正向有功费率1电能 8个字节\n" +
                "        // var forwardActiveRate1Energy = datas.slice(78, 86);\n" +
                "        var forwardActiveRate1Energy = parseEnergyDataBySlice(datas, 78, 8, 6);\n" +
                "        // var forwardActiveRate2Energy = datas.slice(86, 94);\n" +
                "        var forwardActiveRate2Energy = parseEnergyDataBySlice(datas, 86, 8, 6);\n" +
                "        // var forwardActiveRate3Energy = datas.slice(94, 102);\n" +
                "        var forwardActiveRate3Energy = parseEnergyDataBySlice(datas, 94, 8, 6);\n" +
                "        // var forwardActiveRate4Energy = datas.slice(102, 110);\n" +
                "        var forwardActiveRate4Energy = parseEnergyDataBySlice(datas, 102, 8, 6);\n" +
                "        // var forwardActiveRate5Energy = datas.slice(110, 118,6);\n" +
                "        var forwardActiveRate5Energy = parseEnergyDataBySlice(datas, 110, 8, 6);\n" +
                "        // var forwardActiveRate6Energy = datas.slice(118, 126);\n" +
                "        var forwardActiveRate6Energy = parseEnergyDataBySlice(datas, 118, 8, 6);\n" +
                "        // var forwardActiveRate7Energy = datas.slice(126, 134);\n" +
                "        var forwardActiveRate7Energy = parseEnergyDataBySlice(datas, 126, 8, 6);\n" +
                "        // 正向有功费率8电能 8个字节\n" +
                "        var forwardActiveRate8Energy = parseEnergyDataBySlice(datas, 134, 8, 6);\n" +
                "        // 反向有功总电能 8个字节\n" +
                "        var reverseActiveTotalEnergy = parseEnergyDataBySlice(datas, 142, 8, 6);\n" +
                "        // 反向有功费率1电能 8个字节\n" +
                "        var reverseActiveRate1Energy = parseEnergyDataBySlice(datas, 150, 8, 6);\n" +
                "        var reverseActiveRate2Energy = parseEnergyDataBySlice(datas, 158, 8, 6);\n" +
                "        var reverseActiveRate3Energy = parseEnergyDataBySlice(datas, 166, 8, 6);\n" +
                "        var reverseActiveRate4Energy = parseEnergyDataBySlice(datas, 174, 8, 6);\n" +
                "        var reverseActiveRate5Energy = parseEnergyDataBySlice(datas, 182, 8, 6);\n" +
                "        var reverseActiveRate6Energy = parseEnergyDataBySlice(datas, 190, 8, 6);\n" +
                "        var reverseActiveRate7Energy = parseEnergyDataBySlice(datas, 198, 8, 6);\n" +
                "        // 反向有功费率8电能 8个字节\n" +
                "        var reverseActiveRate8Energy = parseEnergyDataBySlice(datas, 206, 8, 6);\n" +
                "        // 组合无功1总电能 8个字节\n" +
                "        var combinedReactive1TotalEnergy = parseEnergyDataBySlice(datas, 214, 8, 6);\n" +
                "        // 组合无功1费率1电能 8个字节\n" +
                "        var combinedReactive1Rate1Energy = parseEnergyDataBySlice(datas, 222, 8, 6);\n" +
                "        var combinedReactive1Rate2Energy = parseEnergyDataBySlice(datas, 230, 8, 6);\n" +
                "        var combinedReactive1Rate3Energy = parseEnergyDataBySlice(datas, 238, 8, 6);\n" +
                "        var combinedReactive1Rate4Energy = parseEnergyDataBySlice(datas, 246, 8, 6);\n" +
                "        var combinedReactive1Rate5Energy = parseEnergyDataBySlice(datas, 254, 8, 6);\n" +
                "        var combinedReactive1Rate6Energy = parseEnergyDataBySlice(datas, 262, 8, 6);\n" +
                "        var combinedReactive1Rate7Energy = parseEnergyDataBySlice(datas, 270, 8, 6);\n" +
                "        // 组合无功1费率8电能 8个字节\n" +
                "        var combinedReactive1Rate8Energy = parseEnergyDataBySlice(datas, 278, 8, 6);\n" +
                "        // 组合无功2总电能 8个字节\n" +
                "        var combinedReactive2TotalEnergy = parseEnergyDataBySlice(datas, 286, 8, 6);\n" +
                "        // 组合无功2费率1电能 8个字节\n" +
                "        var combinedReactive2Rate1Energy = parseEnergyDataBySlice(datas, 294, 8, 6);\n" +
                "        var combinedReactive2Rate2Energy = parseEnergyDataBySlice(datas, 302, 8, 6);\n" +
                "        var combinedReactive2Rate3Energy = parseEnergyDataBySlice(datas, 310, 8, 6);\n" +
                "        var combinedReactive2Rate4Energy = parseEnergyDataBySlice(datas, 318, 8, 6);\n" +
                "        var combinedReactive2Rate5Energy = parseEnergyDataBySlice(datas, 326, 8, 6);\n" +
                "        var combinedReactive2Rate6Energy = parseEnergyDataBySlice(datas, 334, 8, 6);\n" +
                "        var combinedReactive2Rate7Energy = parseEnergyDataBySlice(datas, 342, 8, 6);\n" +
                "        // 组合无功2费率8电能 8个字节\n" +
                "        var combinedReactive2Rate8Energy = parseEnergyDataBySlice(datas, 350, 8, 6);\n" +
                "        // 电压数据块1 4个字节 A 相电压\n" +
                "        var voltageData1 = parseEnergyDataBySlice(datas, 358, 4, 3);\n" +
                "        // B 相电压\n" +
                "        var voltageData2 = parseEnergyDataBySlice(datas, 362, 4, 3);\n" +
                "        // C 相电压\n" +
                "        var voltageData3 = parseEnergyDataBySlice(datas, 366, 4, 3);\n" +
                "        // 电流数据块 6个字节 A 相电流\n" +
                "        var currentData1 = parseEnergyDataBySlice(datas, 370, 6, 3);\n" +
                "        // B 相电流\n" +
                "        var currentData2 = parseEnergyDataBySlice(datas, 376, 6, 3);\n" +
                "        // C 相电流\n" +
                "        var currentData3 = parseEnergyDataBySlice(datas, 382, 6, 3);\n" +
                "        // 电网频率 4个字节\n" +
                "        var gridFrequency = parseInt((parseInt(datas.slice(388, 390), 16) - 0x33).toString(16) +\n" +
                "            (parseInt(datas.slice(390, 392), 16) - 0x33).toString(16)).toString();\n" +
                "        // 当前有功需量 6个字节\n" +
                "        var currentActiveDemand = parseEnergyDataBySlice(datas, 392, 6, 3);\n" +
                "        // 当前无功需量 6个字节\n" +
                "        var currentReactiveDemand = parseEnergyDataBySlice(datas, 398, 6, 3);\n" +
                "        // 有功功率数据块 6个字节 总有功功率\n" +
                "        var activePowerData1 = parseEnergyDataBySlice(datas, 404, 6, 2);\n" +
                "        // A 相有功功率\n" +
                "        var activePowerData2 = parseEnergyDataBySlice(datas, 410, 6, 2);\n" +
                "        // B 相有功功率\n" +
                "        var activePowerData3 = parseEnergyDataBySlice(datas, 416, 6, 2);\n" +
                "        // C 相有功功率\n" +
                "        var activePowerData4 = parseEnergyDataBySlice(datas, 422, 6, 2);\n" +
                "        // 无功功率数据块 6个字节 总无功功率\n" +
                "        var reactivePowerData1 = parseEnergyDataBySlice(datas, 428, 6, 2);\n" +
                "        // A 相无功功率\n" +
                "        var reactivePowerData2 = parseEnergyDataBySlice(datas, 434, 6, 2);\n" +
                "        // B 相无功功率\n" +
                "        var reactivePowerData3 = parseEnergyDataBySlice(datas, 440, 6, 2);\n" +
                "        // C 相无功功率\n" +
                "        var reactivePowerData4 = parseEnergyDataBySlice(datas, 446, 6, 2);\n" +
                "        // 功率因数数据块 4个字节 总功率因数\n" +
                "        var powerFactorData1 = parseEnergyDataBySlice(datas, 452, 4, 1);\n" +
                "        // A 相功率因数\n" +
                "        var powerFactorData2 = parseEnergyDataBySlice(datas, 456, 4, 1);\n" +
                "        // B 相功率因数\n" +
                "        var powerFactorData3 = parseEnergyDataBySlice(datas, 460, 4, 1);\n" +
                "        // C 相功率因数\n" +
                "        var powerFactorData4 = parseEnergyDataBySlice(datas, 464, 4, 1);\n" +
                "        // 校验码\n" +
                "        var checkCode = datas.slice(468, 470);\n" +
                "        // 帧结束符\n" +
                "        var frameEnd = datas.slice(470, 472);\n" +
                "\n" +
                "        var obj = {};\n" +
                "        obj.deviceKey = firstDataAddress;\n" +
                "        obj.data = {\n" +
                "            // \"frameLength\": frameLength,\n" +
                "            // \"frameStart\": frameStart,\n" +
                "            // \"gatewayDeviceNo\": moduleAddress,\n" +
                "            // \"frameStart1\": frameStart1,\n" +
                "            // \"controlCode\": controlCode,\n" +
                "            // \"dataLength\": dataLength,\n" +
                "            // \"periodReport\": periodReport,\n" +
                "            // \"firstDataLength\": firstDataLength,\n" +
                "            \"deviceKey\": firstDataAddress,\n" +
                "            // \"dataItemLength\": dataItemLength,\n" +
                "            // \"dataFlag\": dataFlag,\n" +
                "            \"time\": timestamp,\n" +
                "            \"kwhp\": forwardActiveTotalEnergy,\n" +
                "            // \"forwardActiveRate1Energy\": forwardActiveRate1Energy,\n" +
                "            // \"forwardActiveRate2Energy\": forwardActiveRate2Energy,\n" +
                "            // \"forwardActiveRate3Energy\": forwardActiveRate3Energy,\n" +
                "            // \"forwardActiveRate4Energy\": forwardActiveRate4Energy,\n" +
                "            // \"forwardActiveRate5Energy\": forwardActiveRate5Energy,\n" +
                "            // \"forwardActiveRate6Energy\": forwardActiveRate6Energy,\n" +
                "            // \"forwardActiveRate7Energy\": forwardActiveRate7Energy,\n" +
                "            // \"forwardActiveRate8Energy\": forwardActiveRate8Energy,\n" +
                "            \"kwhn\": reverseActiveTotalEnergy,\n" +
                "            // \"reverseActiveRate1Energy\": reverseActiveRate1Energy,\n" +
                "            // \"reverseActiveRate2Energy\": reverseActiveRate2Energy,\n" +
                "            // \"reverseActiveRate3Energy\": reverseActiveRate3Energy,\n" +
                "            // \"reverseActiveRate4Energy\": reverseActiveRate4Energy,\n" +
                "            // \"reverseActiveRate5Energy\": reverseActiveRate5Energy,\n" +
                "            // \"reverseActiveRate6Energy\": reverseActiveRate6Energy,\n" +
                "            // \"reverseActiveRate7Energy\": reverseActiveRate7Energy,\n" +
                "            // \"reverseActiveRate8Energy\": reverseActiveRate8Energy,\n" +
                "            // \"combinedReactive1TotalEnergy\": combinedReactive1TotalEnergy,\n" +
                "            // \"combinedReactive1Rate1Energy\": combinedReactive1Rate1Energy,\n" +
                "            // \"combinedReactive1Rate2Energy\": combinedReactive1Rate2Energy,\n" +
                "            // \"combinedReactive1Rate3Energy\": combinedReactive1Rate3Energy,\n" +
                "            // \"combinedReactive1Rate4Energy\": combinedReactive1Rate4Energy,\n" +
                "            // \"combinedReactive1Rate5Energy\": combinedReactive1Rate5Energy,\n" +
                "            // \"combinedReactive1Rate6Energy\": combinedReactive1Rate6Energy,\n" +
                "            // \"combinedReactive1Rate7Energy\": combinedReactive1Rate7Energy,\n" +
                "            // \"combinedReactive1Rate8Energy\": combinedReactive1Rate8Energy,\n" +
                "            // \"combinedReactive2TotalEnergy\": combinedReactive2TotalEnergy,\n" +
                "            // \"combinedReactive2Rate1Energy\": combinedReactive2Rate1Energy,\n" +
                "            // \"combinedReactive2Rate2Energy\": combinedReactive2Rate2Energy,\n" +
                "            // \"combinedReactive2Rate3Energy\": combinedReactive2Rate3Energy,\n" +
                "            // \"combinedReactive2Rate4Energy\": combinedReactive2Rate4Energy,\n" +
                "            // \"combinedReactive2Rate5Energy\": combinedReactive2Rate5Energy,\n" +
                "            // \"combinedReactive2Rate6Energy\": combinedReactive2Rate6Energy,\n" +
                "            // \"combinedReactive2Rate7Energy\": combinedReactive2Rate7Energy,\n" +
                "            // \"combinedReactive2Rate8Energy\": combinedReactive2Rate8Energy,\n" +
                "            \"ua\": voltageData1,\n" +
                "            \"ub\": voltageData2,\n" +
                "            \"uc\": voltageData3,\n" +
                "            \"ia\": currentData1,\n" +
                "            \"ib\": currentData2,\n" +
                "            \"ic\": currentData3,\n" +
                "            // \"gridFrequency\": gridFrequency,\n" +
                "            // \"currentActiveDemand\": currentActiveDemand,\n" +
                "            // \"currentReactiveDemand\": currentReactiveDemand,\n" +
                "            \"pt\": activePowerData1,\n" +
                "            \"pa\": activePowerData2,\n" +
                "            \"pb\": activePowerData3,\n" +
                "            \"pc\": activePowerData4,\n" +
                "            \"qt\": reactivePowerData1,\n" +
                "            \"q1\": reactivePowerData2,\n" +
                "            \"q2\": reactivePowerData3,\n" +
                "            \"q3\": reactivePowerData4,\n" +
                "            \"pft\": powerFactorData1,\n" +
                "            \"pf1\": powerFactorData2,\n" +
                "            \"pf2\": powerFactorData3,\n" +
                "            \"pf3\": powerFactorData4,\n" +
                "            // \"checkCode\": checkCode,\n" +
                "            // \"frameEnd\": frameEnd\n" +
                "        };\n" +
                "        obj.time = timestamp;\n" +
                "        resultDatas.push(obj)\n" +
                "    }\n" +
                "    else if (periodReport === '00840000'){\n" +
                "        // 登录 采集器\n" +
                "        // IMEI(ASCII) 30 个字节\n" +
                "        var imei = hexToAscii(parseData(datas, 34, 30));\n" +
                "        // IMSI(ASCII) 40 个字节\n" +
                "        var imsi = hexToAscii(parseData(datas, 64, 40));\n" +
                "        // ICCID(ASCII) 40 个字节\n" +
                "        var iccid = hexToAscii(parseData(datas, 104, 40));\n" +
                "        // 生产商(ASCII) 16 个字节\n" +
                "        var manufacturer = hexToAscii(parseData(datas, 144, 16));\n" +
                "        // 型号(ASCII) 16 个字节\n" +
                "        var model = hexToAscii(parseData(datas, 160, 16));\n" +
                "        // 硬件版本号(ASCII) 16 个字节\n" +
                "        var hardwareVersion = hexToAscii(parseData(datas, 176, 16));\n" +
                "        // 硬件版本日期(ASCII) 16 个字节\n" +
                "        var hardwareVersionDate = hexToAscii(parseData(datas, 192, 16));\n" +
                "        // 保留 16 个字节\n" +
                "        var reserve = parseData(datas, 208, 16)\n" +
                "        // 固件版本日期(ASCII) 16 个字节\n" +
                "        var firmwareVersionDate = hexToAscii(parseData(datas, 224, 16));\n" +
                "        // 电表数量 2 个字节\n" +
                "        var meterCount = parseData(datas, 240, 2)\n" +
                "        // 电表1唯一标识(电表地址) 12个字节 根据数量循环\n" +
                "        var meterAddress1 = '';\n" +
                "        var meterAddress2 = '';\n" +
                "        var meterAddress3 = '';\n" +
                "        if (parseInt(meterCount) > 0) {\n" +
                "            for (var i = 0; i < parseInt(meterCount, 16); i++) {\n" +
                "                // 电表地址\n" +
                "                if (i === 0) {\n" +
                "                    meterAddress1 = parseData(datas, 242, 12)\n" +
                "                }\n" +
                "                if (i === 1) {\n" +
                "                    meterAddress2 = parseData(datas, 254, 12)\n" +
                "                }\n" +
                "                if (i === 2) {\n" +
                "                    meterAddress3 = parseData(datas, 266, 12)\n" +
                "                }\n" +
                "            }\n" +
                "        }\n" +
                "        // 校验码 2个字节\n" +
                "        // 帧结束符  2个字节\n" +
                "        var obj = {};\n" +
                "        obj.deviceKey = moduleAddress;\n" +
                "        obj.data = {\n" +
                "            \"imei\": imei,\n" +
                "            \"imsi\": imsi,\n" +
                "            \"iccid\": iccid,\n" +
                "            \"manufacturer\": manufacturer,\n" +
                "            \"model\": model,\n" +
                "            \"hardware_version\": hardwareVersion,\n" +
                "            \"hardware_version_date\": hardwareVersionDate,\n" +
                "            \"firmware_version_date\": firmwareVersionDate,\n" +
                "            \"meter_count\": meterCount,\n" +
                "            \"meter_address_one\": meterAddress1,\n" +
                "            \"meter_address_two\": meterAddress2,\n" +
                "            \"meter_address_three\": meterAddress3\n" +
                "        };\n" +
                "        resultDatas.push(obj)\n" +
                "    }\n" +
                "    return resultDatas;\n" +
                "}\n" +
                "// 数据编码\n" +
                "this.encode = function (service, device) {\n" +
                "    return null;\n" +
                "}";
        //15 分钟数据上报
//        String msg = "{\"data\":\"00EA68790200790859682200DD3733B733330A36373398A98B035533B633484B3939575936333333333333443433339534333385333333333333333333333333333333333333333B33333333333333383333333533333333333333333333333333333333333333333333333B3333333333333338333333363333333333333333333333333333333333333333333333393333333333333336333333363333333333333333333333333333333333333333333333CC3C33333343333333333333333333CC7C33333333333333333333333333333333333333333333333333333333333333433343333333437516\"}";
        String msg = "{\"data\":\"00EA68790200790859682200DD3733B733330A36373398A98B035533B63353553939575936333333333333443433339534333385333333333333333333333333333333333333333B33333333333333383333333533333333333333333333333333333333333333333333333B3333333333333338333333363333333333333333333333333333333333333333333333393333333333333336333333363333333333333333333333333333333333333333333333CC3C3333334333333333333333333333833333333333333333333333333333333333333333333333333333333333333343334333333343F816\"}";
        // 登录
//        String msg = "{\"data\":\"007F68790200790859682200723333B7336366696368676668636A656C67696B333333333365636A6C63686A6569696B6363696765636A6C686A647766656C6469786763696B6C6B33333333877D868A3333866364647F805363616589647576646063606564757633333333333333336C6364656A6364653436373398A98B4516\"}";
        Object scriptObj = engine.eval(String.format("new (function () {\n%s})()", script));
        Map<String, Object> msgMap = JsonUtil.parseObject(msg);
        Object rst = engine.invokeMethod(scriptObj, "decode", msgMap);
        ScriptObjectMirror result = (ScriptObjectMirror) rst;
        List<Map<String, Object>> msgList = (List<Map<String, Object>>) JsonUtil.toObject(result);
        System.out.println(JsonUtil.toJsonString(msgList));
        for (Map<String, Object> map : msgList) {
            for (String s : map.keySet()) {
                System.out.println(s + ":" + map.get(s).toString().trim());
            }
        }
    }
}
