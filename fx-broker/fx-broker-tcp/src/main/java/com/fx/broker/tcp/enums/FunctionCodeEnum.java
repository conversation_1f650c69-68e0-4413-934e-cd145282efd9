package com.fx.broker.tcp.enums;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 协议功能码枚举
 * 定义系统支持的所有功能码及其描述信息
 * 
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum FunctionCodeEnum {

    HEARTBEAT(0, "心跳"),
    LOGIN(1, "登录"),
    DATA_REPORT(2, "上报数据"),
    TIME_SYNC(3, "请求时钟同步"),
    RESTART_GATEWAY(4, "重启网关"),
    COMMAND_ISSUE(5, "下发指令"),
    COMMAND_RESULT(6, "上报指令结果"),
    BINARY_DATA(7, "下发二进制数据"),
    COMMAND_COUNT(8, "读指令条数"),
    COMMAND_CONTENT(9, "读指令内容"),
    RUNTIME_INFO(10, "读运行信息"),
    GATEWAY_UPGRADE(11, "网关升级");

    /**
     * 功能码值
     */
    private final Integer code;

    /**
     * 功能码描述
     */
    private final String description;

    /**
     * 根据功能码值获取枚举实例
     * 
     * @param code 功能码值
     * @return 对应的枚举实例，如果不存在则返回null
     */
    public static FunctionCodeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }

        for (FunctionCodeEnum functionCodeEnum : values()) {
            if (functionCodeEnum.getCode().equals(code)) {
                return functionCodeEnum;
            }
        }
        return null;
    }

    /**
     * 根据功能码值获取描述
     * 
     * @param code 功能码值
     * @return 功能码描述，如果不存在则返回未知功能码信息
     */
    public static String getDescriptionByCode(Integer code) {
        FunctionCodeEnum functionCodeEnum = getByCode(code);
        if (functionCodeEnum != null) {
            return functionCodeEnum.getDescription();
        }
        return StrUtil.format("未知功能码({})", code);
    }

    /**
     * 检查功能码是否无效
     * 
     * @param code 功能码值
     * @return 是否为无效的功能码
     */
    public static boolean isInvalidCode(Integer code) {
        return getByCode(code) == null;
    }

    /**
     * 检查功能码是否有效
     * 
     * @param code 功能码值
     * @return 是否为有效的功能码
     */
    public static boolean isValidCode(Integer code) {
        return getByCode(code) != null;
    }

}