package com.fx.broker.tcp.service;

/**
 * 二进制数据缓存服务接口
 * 负责管理分包数据的缓存、组装和文件上传
 *
 * <AUTHOR>
 */
public interface BinaryDataCacheService {

    /**
     * 缓存二进制数据包
     *
     * @param gatewayId  网关ID
     * @param deviceKey  设备Key
     * @param deviceName 设备名称
     * @param total      总包数
     * @param num        当前包序号
     * @param binaryData 二进制数据
     * @param time       时间戳
     * @return 是否缓存成功
     */
    boolean cacheBinaryPacket(String gatewayId, String deviceKey, String deviceName,
            Integer total, Integer num, byte[] binaryData, String time);

    /**
     * 检查是否所有包都已接收完成
     *
     * @param gatewayId 网关ID
     * @param deviceKey 设备Key
     * @param total     总包数
     * @return 是否接收完成
     */
    boolean isAllPacketsReceived(String gatewayId, String deviceKey, Integer total);

    /**
     * 组装完整的二进制数据并上传到文件服务
     *
     * @param gatewayId  网关ID
     * @param deviceKey  设备Key
     * @param deviceName 设备名称
     * @param total      总包数
     * @param time       时间戳
     * @return 文件URL，如果失败返回null
     */
    String assembleAndUploadFile(String gatewayId, String deviceKey, String deviceName,
            Integer total, String time);

    /**
     * 清理缓存的分包数据
     *
     * @param gatewayId 网关ID
     * @param deviceKey 设备Key
     */
    void clearCachedPackets(String gatewayId, String deviceKey);
}