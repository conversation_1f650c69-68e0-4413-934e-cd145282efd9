package com.fx.broker.tcp.protocol.processor;

import com.fx.broker.tcp.domain.ProtocolMessage;
import com.fx.broker.tcp.enums.FunctionCodeEnum;
import com.fx.broker.tcp.enums.ReturnCodeEnum;
import com.fx.broker.tcp.mq.publisher.RocketMQPublisher;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 功能码 4：重启网关处理器
 * 处理网关的重启回调响应
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class RestartProcessor extends BaseCommandProcessor {

    @Resource
    private RocketMQPublisher rocketMQPublisher;

    @Override
    public Integer getFunctionCode() {
        return FunctionCodeEnum.RESTART_GATEWAY.getCode();
    }

    @Override
    public String getProcessorName() {
        return "重启网关处理器";
    }

    @Override
    protected ProtocolMessage doProcess(ProtocolMessage message) {
        try {
            String gatewayId = getGatewayId(message);
            Integer retCode = message.getRetCode();

            // 1. 验证结果码
            if (retCode == null) {
                logProcessing(message, "[doProcess][重启响应缺少结果码]");
                return createErrorResponse(message, ReturnCodeEnum.MISSING_PARAMETER);
            }

            // 2. 处理重启结果
            if (retCode == 0) {
                logProcessing(message, String.format("[doProcess][网关确认重启 - 网关ID: %s]", gatewayId));
            } else {
                logProcessing(message, String.format("[doProcess][网关拒绝重启 - 网关ID: %s, 结果码: %d]", gatewayId, retCode));
            }
            return NO_RESPONSE;

        } catch (Exception e) {
            logProcessing(message, "[doProcess][处理重启回调时发生异常]");
            log.error("[doProcess][处理重启回调时发生异常 - 连接ID: {}]", message.getConnectionId(), e);
            return createErrorResponse(message, ReturnCodeEnum.MEMORY_INSUFFICIENT);
        }
    }

}