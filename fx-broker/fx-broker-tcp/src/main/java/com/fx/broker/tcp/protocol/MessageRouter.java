package com.fx.broker.tcp.protocol;

import cn.hutool.core.util.StrUtil;
import com.fx.broker.tcp.domain.ProtocolMessage;
import com.fx.broker.tcp.enums.FunctionCodeEnum;
import com.fx.broker.tcp.enums.ReturnCodeEnum;
import com.fx.broker.tcp.protocol.processor.BaseCommandProcessor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 消息路由器
 * 负责根据功能码将协议消息路由到对应的处理器进行处理
 * 支持动态处理器注册、统计监控和健康检查
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class MessageRouter {

    @Resource
    private ApplicationContext applicationContext;

    /**
     * 功能码处理器映射表
     */
    private final ConcurrentHashMap<Integer, BaseCommandProcessor> processors = new ConcurrentHashMap<>();

    /**
     * 每个功能码的处理统计
     */
    private final ConcurrentHashMap<Integer, ProcessorStatistics> processorStats = new ConcurrentHashMap<>();

    /**
     * 初始化路由器，注册所有处理器
     */
    @PostConstruct
    public void initialize() {
        log.info("[initialize][初始化消息路由器]");

        try {
            // 从Spring容器获取所有处理器
            applicationContext.getBeansOfType(BaseCommandProcessor.class)
                    .values()
                    .forEach(this::registerProcessor);

            log.info("[initialize][消息路由器初始化完成 - 已注册处理器数量: {}]", processors.size());

        } catch (Exception e) {
            log.error("[initialize][消息路由器初始化失败]", e);
            throw new RuntimeException("消息路由器初始化失败", e);
        }
    }

    /**
     * 注册处理器
     * 
     * @param processor 处理器实例
     */
    public void registerProcessor(BaseCommandProcessor processor) {
        if (processor == null) {
            log.warn("[registerProcessor][尝试注册空的处理器]");
            return;
        }

        Integer functionCode = processor.getFunctionCode();
        if (FunctionCodeEnum.isInvalidCode(functionCode)) {
            log.warn("[registerProcessor][处理器功能码无效 - 处理器: {}, 功能码: {}]",
                    processor.getClass().getSimpleName(), functionCode);
            return;
        }

        BaseCommandProcessor existingProcessor = processors.put(functionCode, processor);
        if (existingProcessor != null) {
            log.warn("[registerProcessor][功能码 {} 的处理器被替换 - 原处理器: {}, 新处理器: {}]",
                    functionCode, existingProcessor.getClass().getSimpleName(),
                    processor.getClass().getSimpleName());
        }

        // 初始化处理器统计信息
        processorStats.put(functionCode, new ProcessorStatistics(functionCode,
                processor.getClass().getSimpleName()));

        log.info("[registerProcessor][注册处理器成功 - 功能码: {}, 处理器: {}]",
                functionCode, processor.getClass().getSimpleName());
    }

    /**
     * 路由消息到对应处理器
     * 
     * @param message 协议消息
     */
    public void routeMessage(ProtocolMessage message) {
        try {
            // 验证消息
            if (!validateMessage(message)) {
                log.warn("[routeMessage][消息验证失败 - 连接ID: {}, 功能码: {}]",
                        message.getConnectionId(), message.getCode());
                return;
            }

            Integer functionCode = message.getCode();

            // 获取处理器
            BaseCommandProcessor processor = processors.get(functionCode);
            if (processor == null) {
                log.warn("[routeMessage][未找到功能码 {} 的处理器 - 连接ID: {}]", functionCode, message.getConnectionId());
                handleUnsupportedFunction(message);
                updateProcessorStatistics(functionCode, false, 0);
                return;
            }

            // 更新处理器活跃时间
            ProcessorStatistics stats = processorStats.get(functionCode);
            if (stats != null) {
                stats.setLastProcessTime(System.currentTimeMillis());
            }

            // 执行处理
            long processorStartTime = System.currentTimeMillis();
            processor.process(message);
            long processorDuration = System.currentTimeMillis() - processorStartTime;

            // 更新统计信息
            updateProcessorStatistics(functionCode, true, processorDuration);

        } catch (Exception e) {
            log.error("[routeMessage][消息路由处理异常 - 连接ID: {}, 功能码: {}]",
                    message.getConnectionId(), message.getCode(), e);
            updateProcessorStatistics(message.getCode(), false, 0);

            // 发送异常响应
            handleProcessingException(message, e);
        }
    }

    /**
     * 验证消息有效性
     * 
     * @param message 消息
     * @return 是否有效
     */
    private boolean validateMessage(ProtocolMessage message) {
        if (message == null) {
            return false;
        }

        if (FunctionCodeEnum.isInvalidCode(message.getCode())) {
            return false;
        }

        if (message.getIsUplink() == null || message.getIsResponse() == null) {
            return false;
        }

        if (StrUtil.isBlank(message.getConnectionId())) {
            return false;
        }

        return true;
    }

    /**
     * 处理不支持的功能码
     * 
     * @param message 消息
     */
    private void handleUnsupportedFunction(ProtocolMessage message) {
        try {
            // 创建错误响应
            ProtocolMessage errorResponse = message.createErrorResponse(ReturnCodeEnum.NOT_SUPPORTED.getCode());

            // 这里应该通过ProtocolHandler发送响应，但为了避免循环依赖，我们记录日志
            log.warn("[handleUnsupportedFunction][功能码不支持 - 连接ID: {}, 功能码: {}]",
                    message.getConnectionId(), message.getCode());

        } catch (Exception e) {
            log.error("[handleUnsupportedFunction][处理不支持功能码时发生异常]", e);
        }
    }

    /**
     * 处理处理异常
     * 
     * @param message   消息
     * @param exception 异常
     */
    private void handleProcessingException(ProtocolMessage message, Exception exception) {
        try {
            // 创建异常响应
            ProtocolMessage errorResponse = message.createErrorResponse(ReturnCodeEnum.MEMORY_INSUFFICIENT.getCode()); // 内存不足，无法处理消息

            log.error("[handleProcessingException][消息处理异常响应 - 连接ID: {}, 功能码: {}]",
                    message.getConnectionId(), message.getCode());

        } catch (Exception e) {
            log.error("[handleProcessingException][处理异常响应时发生异常]", e);
        }
    }

    /**
     * 更新处理器统计信息
     * 
     * @param functionCode   功能码
     * @param success        是否成功
     * @param processingTime 处理时间
     */
    private void updateProcessorStatistics(Integer functionCode, boolean success, long processingTime) {
        ProcessorStatistics stats = processorStats.get(functionCode);
        if (stats != null) {
            stats.incrementTotalMessages();
            if (success) {
                stats.incrementSuccessMessages();
            } else {
                stats.incrementErrorMessages();
            }
            stats.addProcessingTime(processingTime);
        }
    }

    /**
     * 处理器统计信息
     */
    @Data
    public static class ProcessorStatistics {
        /**
         * 功能码
         */
        private final Integer functionCode;
        /**
         * 处理器名称
         */
        private final String processorName;
        /**
         * 总消息数
         */
        private final AtomicLong totalMessages = new AtomicLong(0);
        /**
         * 成功消息数
         */
        private final AtomicLong successMessages = new AtomicLong(0);
        /**
         * 错误消息数
         */
        private final AtomicLong errorMessages = new AtomicLong(0);
        /**
         * 总处理时间
         */
        private final AtomicLong totalProcessingTime = new AtomicLong(0);
        /**
         * 最后处理时间
         */
        private volatile long lastProcessTime;

        public ProcessorStatistics(Integer functionCode, String processorName) {
            this.functionCode = functionCode;
            this.processorName = processorName;
            this.lastProcessTime = System.currentTimeMillis();
        }

        public void incrementTotalMessages() {
            totalMessages.incrementAndGet();
        }

        public void incrementSuccessMessages() {
            successMessages.incrementAndGet();
        }

        public void incrementErrorMessages() {
            errorMessages.incrementAndGet();
        }

        public void addProcessingTime(long time) {
            totalProcessingTime.addAndGet(time);
        }

    }
}