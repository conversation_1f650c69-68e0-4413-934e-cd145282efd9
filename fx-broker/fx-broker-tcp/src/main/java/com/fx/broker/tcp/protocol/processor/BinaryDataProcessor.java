package com.fx.broker.tcp.protocol.processor;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.fx.broker.tcp.domain.BinaryTransfer;
import com.fx.broker.tcp.domain.ProtocolMessage;
import com.fx.broker.tcp.enums.FunctionCodeEnum;
import com.fx.broker.tcp.enums.ReturnCodeEnum;
import com.fx.broker.tcp.mq.publisher.RocketMQPublisher;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 功能码 7：下发二进制数据处理器
 * 处理配置包或固件包的分包下发和网关的接收确认
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class BinaryDataProcessor extends BaseCommandProcessor {

    @Resource
    private RocketMQPublisher rocketMQPublisher;

    @Value("${tcp-server.binary.max-package-size:8192}")
    private int maxPackageSize; // 单包最大大小（字节）

    @Value("${tcp-server.binary.max-total-size:10485760}")
    private long maxTotalSize; // 总文件最大大小（字节，默认10MB）

    /**
     * 二进制传输会话管理
     */
    private final ConcurrentHashMap<String, BinaryTransfer> transferSessions = new ConcurrentHashMap<>();

    @Override
    public Integer getFunctionCode() {
        return FunctionCodeEnum.BINARY_DATA.getCode();
    }

    @Override
    public String getProcessorName() {
        return "下发二进制数据处理器";
    }

    @Override
    protected ProtocolMessage doProcess(ProtocolMessage message) {
        JSONObject data = message.getData();
        if (data == null) {
            log.warn("二进制数据消息缺少数据 - 连接ID: {}", message.getConnectionId());
            return createErrorResponse(message, ReturnCodeEnum.MISSING_PARAMETER);
        }

        // 验证必要字段
        if (hasMissingRequiredFields(data, "data_type", "offect", "bin")) {
            log.warn("二进制数据消息缺少必要参数 - 连接ID: {}", message.getConnectionId());
            return createErrorResponse(message, ReturnCodeEnum.MISSING_PARAMETER);
        }

        Integer dataType = data.getInt("data_type");
        Long offset = data.getLong("offect"); // 注意：协议文档中是"offect"，不是"offset"
        String binData = data.getStr("bin");

        // 验证数据类型
        if (!isValidDataType(dataType)) {
            log.warn("无效的数据类型 - 连接ID: {}, 数据类型: {}", message.getConnectionId(), dataType);
            return createErrorResponse(message, ReturnCodeEnum.MISSING_PARAMETER);
        }

        // 验证偏移量
        if (offset == null || offset < 0) {
            log.warn("无效的偏移量 - 连接ID: {}, 偏移量: {}", message.getConnectionId(), offset);
            return createErrorResponse(message, ReturnCodeEnum.MISSING_PARAMETER);
        }

        // 验证 Base64 数据
        if (StrUtil.isBlank(binData)) {
            log.warn("二进制数据为空 - 连接ID: {}", message.getConnectionId());
            return createErrorResponse(message, ReturnCodeEnum.MISSING_PARAMETER);
        }

        try {
            // 解码Base64数据
            byte[] binaryData = Base64.decode(binData);
            if (binaryData == null || binaryData.length == 0) {
                log.warn("Base64解码失败 - 连接ID: {}", message.getConnectionId());
                return createErrorResponse(message, ReturnCodeEnum.FORMAT_ERROR);
            }

            // 验证包大小
            if (binaryData.length > maxPackageSize) {
                log.warn("当前包超出范围 - 连接ID: {}, 包大小: {}, 最大: {}",
                        message.getConnectionId(), binaryData.length, maxPackageSize);
                return createErrorResponse(message, ReturnCodeEnum.PACKAGE_SIZE_OVERFLOW);
            }

            String gatewayId = getGatewayId(message);

            // 处理二进制数据包
            boolean processed = processBinaryPackage(gatewayId, dataType, offset, binaryData);
            if (!processed) {
                log.warn("处理二进制数据包失败 - 网关ID: {}, 数据类型: {}, 偏移量: {}",
                        gatewayId, dataType, offset);
                return createErrorResponse(message, ReturnCodeEnum.MEMORY_INSUFFICIENT);
            }

            // 记录二进制数据下发事件
            recordBinaryDataEvent(gatewayId, dataType, offset, binaryData.length, "BINARY_SENT");

            logProcessing(message, "二进制数据下发成功 - 网关ID: " + gatewayId +
                    ", 数据类型: " + getDataTypeName(dataType) +
                    ", 偏移量: " + offset + ", 大小: " + binaryData.length + "字节");

            // 下行数据不需要返回响应，网关会发送上行响应
            return NO_RESPONSE;

        } catch (Exception e) {
            log.error("处理下行二进制数据时发生异常 - 连接ID: {}, 数据类型: {}, 偏移量: {}",
                    message.getConnectionId(), dataType, offset, e);
            return createErrorResponse(message, ReturnCodeEnum.MEMORY_INSUFFICIENT);
        }
    }

    /**
     * 处理上行二进制响应
     * 网关对二进制数据包的接收确认
     */
    private ProtocolMessage processUplinkBinaryResponse(ProtocolMessage message) {
        Integer retCode = message.getRetCode();
        if (retCode == null) {
            log.warn("二进制响应缺少结果码 - 连接ID: {}", message.getConnectionId());
            return createErrorResponse(message, ReturnCodeEnum.MISSING_PARAMETER);
        }

        JSONObject data = message.getData();
        if (data == null) {
            log.warn("二进制响应缺少数据 - 连接ID: {}", message.getConnectionId());
            return createErrorResponse(message, ReturnCodeEnum.MISSING_PARAMETER);
        }

        // 验证必要字段
        if (hasMissingRequiredFields(data, "data_type", "offect")) {
            log.warn("二进制响应缺少必要参数 - 连接ID: {}", message.getConnectionId());
            return createErrorResponse(message, ReturnCodeEnum.MISSING_PARAMETER);
        }

        Integer dataType = data.getInt("data_type");
        Long offset = data.getLong("offect");

        try {
            String gatewayId = getGatewayId(message);

            if (retCode == 0) {
                log.info("网关确认接收二进制数据 - 网关ID: {}, 数据类型: {}, 偏移量: {}, 连接ID: {}",
                        gatewayId, dataType, offset, message.getConnectionId());

                // 更新传输状态
                updateTransferStatus(gatewayId, dataType, offset, true);

                // 记录接收确认事件
                recordBinaryDataEvent(gatewayId, dataType, offset, 0, "BINARY_RECEIVED");

                // 发布接收确认事件到MQ
                // publishBinaryDataEventToMQ(gatewayId, dataType, offset, 0,
                // "BINARY_RECEIVED");

                logProcessing(message, "网关确认接收二进制数据 - 网关ID: " + gatewayId +
                        ", 数据类型: " + getDataTypeName(dataType) + ", 偏移量: " + offset);

            } else {
                log.warn("网关拒绝接收二进制数据 - 网关ID: {}, 数据类型: {}, 偏移量: {}, 结果码: {}",
                        gatewayId, dataType, offset, retCode);

                // 更新传输状态为失败
                updateTransferStatus(gatewayId, dataType, offset, false);

                // 记录拒绝事件
                recordBinaryDataEvent(gatewayId, dataType, offset, 0, "BINARY_REJECTED");

                // 发布拒绝事件到MQ
                // publishBinaryDataEventToMQ(gatewayId, dataType, offset, 0,
                // "BINARY_REJECTED");
            }

            // 上行响应不需要再回复
            return NO_RESPONSE;

        } catch (Exception e) {
            log.error("处理上行二进制响应时发生异常 - 连接ID: {}, 数据类型: {}, 偏移量: {}",
                    message.getConnectionId(), dataType, offset, e);
            return createErrorResponse(message, ReturnCodeEnum.MEMORY_INSUFFICIENT);
        }
    }

    /**
     * 处理二进制数据包
     */
    private boolean processBinaryPackage(String gatewayId, Integer dataType, Long offset, byte[] data) {
        try {
            String transferKey = generateTransferKey(gatewayId, dataType);

            // 获取或创建传输会话
            BinaryTransfer transfer = transferSessions.computeIfAbsent(transferKey, k -> {
                BinaryTransfer newTransfer = new BinaryTransfer();
                newTransfer.setGatewayId(gatewayId);
                newTransfer.setDataType(BinaryTransfer.DataType.fromCode(dataType));
                newTransfer.setStartTime(LocalDateTime.now());
                newTransfer.setStatus(BinaryTransfer.TransferStatus.TRANSFERRING);
                return newTransfer;
            });

            // 验证总大小限制
            long expectedTotalSize = offset + data.length;
            if (expectedTotalSize > maxTotalSize) {
                log.warn("总体积超出范围 - 网关ID: {}, 数据类型: {}, 预期大小: {}, 最大: {}",
                        gatewayId, dataType, expectedTotalSize, maxTotalSize);
                transferSessions.remove(transferKey);
                return false;
            }

            // 更新传输信息
            transfer.markPacketSuccess(offset.intValue(), data.length);

            log.debug("处理二进制数据包 - 网关ID: {}, 数据类型: {}, 偏移量: {}, 大小: {}, 进度: {:.2f}%",
                    gatewayId, dataType, offset, data.length, transfer.getProgress());

            // 检查是否传输完成
            if (transfer.getProgress() == 100) {
                log.info("二进制数据传输完成 - 网关ID: {}, 数据类型: {}, 总大小: {}, 耗时: {}ms",
                        gatewayId, dataType, transfer.getTotalSize(),
                        System.currentTimeMillis() - transfer.getStartTime().toEpochSecond(ZoneOffset.of("+8")));

                // 传输完成，移除会话
                transferSessions.remove(transferKey);

                // 记录传输完成事件
                recordBinaryDataEvent(gatewayId, dataType, 0L, transfer.getTotalSize(), "TRANSFER_COMPLETED");
            }

            return true;

        } catch (Exception e) {
            log.error("处理二进制数据包异常 - 网关ID: {}, 数据类型: {}, 偏移量: {}",
                    gatewayId, dataType, offset, e);
            return false;
        }
    }

    /**
     * 更新传输状态
     */
    private void updateTransferStatus(String gatewayId, Integer dataType, Long offset, boolean success) {
        String transferKey = generateTransferKey(gatewayId, dataType);
        BinaryTransfer transfer = transferSessions.get(transferKey);

        if (transfer != null) {
            if (success) {
                transfer.markPacketSuccess(offset.intValue(), 0);
            } else {
                transfer.markPacketFailed(offset.intValue());
                transferSessions.remove(transferKey);
            }
        }
    }

    /**
     * 验证数据类型是否有效
     */
    private boolean isValidDataType(Integer dataType) {
        // 1:网关固件、2：摄像模块固件、3：RS485配置、4：图片采集配置
        return dataType != null && dataType >= 1 && dataType <= 4;
    }

    /**
     * 获取数据类型名称
     */
    private String getDataTypeName(Integer dataType) {
        switch (dataType) {
            case 1:
                return "网关固件";
            case 2:
                return "摄像模块固件";
            case 3:
                return "RS485配置";
            case 4:
                return "图片采集配置";
            default:
                return "未知类型";
        }
    }

    /**
     * 生成传输会话Key
     */
    private String generateTransferKey(String gatewayId, Integer dataType) {
        return gatewayId + "_" + dataType;
    }

    /**
     * 记录二进制数据事件
     */
    private void recordBinaryDataEvent(String gatewayId, Integer dataType, Long offset,
            long dataSize, String eventType) {
        try {
            JSONObject binaryEvent = new JSONObject();
            binaryEvent.set("gateway_id", gatewayId);
            binaryEvent.set("data_type", dataType);
            binaryEvent.set("data_type_name", getDataTypeName(dataType));
            binaryEvent.set("offset", offset);
            binaryEvent.set("data_size", dataSize);
            binaryEvent.set("event_type", eventType);
            binaryEvent.set("function_code", 7);
            binaryEvent.set("event_time", System.currentTimeMillis() / 1000);
            binaryEvent.set("description", getEventDescription(eventType));

            // TODO: 保存到数据库或缓存
            log.info("记录二进制数据事件 - 网关ID: {}, 数据类型: {}, 事件类型: {}",
                    gatewayId, getDataTypeName(dataType), eventType);

        } catch (Exception e) {
            log.error("记录二进制数据事件失败 - 网关ID: {}, 数据类型: {}, 事件类型: {}",
                    gatewayId, dataType, eventType, e);
        }
    }

    /**
     * 获取事件描述
     */
    private String getEventDescription(String eventType) {
        switch (eventType) {
            case "BINARY_SENT":
                return "服务器下发二进制数据包";
            case "BINARY_RECEIVED":
                return "网关确认接收二进制数据包";
            case "BINARY_REJECTED":
                return "网关拒绝接收二进制数据包";
            case "TRANSFER_COMPLETED":
                return "二进制数据传输完成";
            case "TRANSFER_FAILED":
                return "二进制数据传输失败";
            default:
                return "未知二进制数据事件";
        }
    }





}