package com.fx.broker.tcp.protocol.processor;

import cn.hutool.json.JSONObject;
import com.fx.broker.tcp.domain.ProtocolMessage;
import com.fx.broker.tcp.enums.FunctionCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 功能码 9：读取指定任务 ID 的指令内容
 * 指令内容处理器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class CommandContentProcessor extends BaseCommandProcessor {

    @Override
    public Integer getFunctionCode() {
        return FunctionCodeEnum.COMMAND_CONTENT.getCode();
    }

    @Override
    public String getProcessorName() {
        return "指令内容处理器";
    }

    @Override
    protected ProtocolMessage doProcess(ProtocolMessage message) {
        // 处理网关回复的指令内容
        handleCommandContentResponse(message);

        // 上行响应不需要再回复
        return NO_RESPONSE;
    }

    /**
     * 处理网关的指令内容回复
     */
    private void handleCommandContentResponse(ProtocolMessage message) {
        log.info("收到指令内容回复 - 网关ID: {}, 连接ID: {}", message.getGatewayId(), message.getConnectionId());

        Integer retCode = message.getRetCode();
        if (retCode == null) {
            log.warn("指令内容回复缺少结果码 - 网关ID: {}", message.getGatewayId());
            return;
        }

        if (retCode != 0) {
            log.warn("网关查询指令内容失败 - 网关ID: {}, 错误码: {}", message.getGatewayId(), retCode);
            return;
        }

        // 验证必要参数
        if (hasMissingRequiredFields(message.getData(), "task_id", "content")) {
            log.warn("指令内容回复缺少必要参数 - 网关ID: {}", message.getGatewayId());
            return;
        }

        Integer taskId = message.getDataInt("task_id");
        Object content = message.getData("content");

        if (taskId == null) {
            log.warn("任务ID无效 - 网关ID: {}", message.getGatewayId());
            return;
        }

        log.info("收到指令内容回复 - 网关ID: {}, 任务ID: {}, 内容类型: {}",
                message.getGatewayId(), taskId, content != null ? content.getClass().getSimpleName() : "null");

        // 处理指令内容信息
        handleCommandContentInfo(message.getGatewayId(), taskId, content);
    }

    /**
     * 主动查询指定指令的内容
     *
     * @param gatewayId 网关ID
     * @param taskId    任务ID
     * @return 是否发送成功
     */
    public boolean queryCommandContent(String gatewayId, Integer taskId) {
        try {
            log.info("查询指令内容 - 网关ID: {}, 任务ID: {}", gatewayId, taskId);

            // 检查网关是否在线
            if (!connectionManager.isGatewayOnline(gatewayId)) {
                log.warn("网关不在线，无法查询指令内容 - 网关ID: {}", gatewayId);
                return false;
            }

            if (taskId == null) {
                log.warn("任务ID不能为空 - 网关ID: {}", gatewayId);
                return false;
            }

            // 构建查询指令内容消息
            ProtocolMessage queryMessage = new ProtocolMessage();
            queryMessage.setIsUplink(false); // 下行消息
            queryMessage.setIsResponse(false); // 发送消息
            queryMessage.setPackId(0); // 使用默认包序号
            queryMessage.setCode(9);

            JSONObject queryData = new JSONObject();
            queryData.set("task_id", taskId);
            queryMessage.setData(queryData);

            // 设置网关信息
            queryMessage.setGatewayId(gatewayId);

            // 编码消息
            String messageContent = messageCodec.encode(queryMessage);
            if (messageContent == null) {
                log.error("查询指令内容消息编码失败 - 网关ID: {}, 任务ID: {}", gatewayId, taskId);
                return false;
            }

            // 发送查询请求
            boolean sent = connectionManager.sendMessageToGateway(gatewayId, messageContent);
            if (sent) {
                log.info("查询指令内容请求发送成功 - 网关ID: {}, 任务ID: {}", gatewayId, taskId);
                // 记录查询事件
                recordCommandContentQueryEvent(gatewayId, taskId);
            } else {
                log.error("查询指令内容请求发送失败 - 网关ID: {}, 任务ID: {}", gatewayId, taskId);
            }

            return sent;

        } catch (Exception e) {
            log.error("查询指令内容异常 - 网关ID: {}, 任务ID: {}", gatewayId, taskId, e);
            return false;
        }
    }

    /**
     * 处理指令内容信息
     */
    private void handleCommandContentInfo(String gatewayId, Integer taskId, Object content) {
        try {
            // 构建指令内容信息对象
            JSONObject contentInfo = new JSONObject();
            contentInfo.set("gateway_id", gatewayId);
            contentInfo.set("task_id", taskId);
            contentInfo.set("command_content", content);
            contentInfo.set("content_type", determineContentType(content));
            contentInfo.set("query_time", System.currentTimeMillis() / 1000);

            log.info("处理指令内容信息 - 网关ID: {}, 任务ID: {}", gatewayId, taskId);

            // 发送信息到MQ进行业务处理
            publishCommandContentInfoToMQ(contentInfo);

            // 记录指令内容信息事件
            recordCommandContentInfoEvent(gatewayId, taskId, content);

        } catch (Exception e) {
            log.error("处理指令内容信息失败 - 网关ID: {}, 任务ID: {}", gatewayId, taskId, e);
        }
    }

    /**
     * 确定内容类型
     */
    private String determineContentType(Object content) {
        if (content == null) {
            return "null";
        } else if (content instanceof String) {
            return "string";
        } else if (content instanceof Number) {
            return "number";
        } else if (content instanceof JSONObject) {
            return "object";
        } else if (content instanceof java.util.List) {
            return "array";
        } else {
            return "unknown";
        }
    }

    /**
     * 发布指令内容信息到MQ
     */
    private void publishCommandContentInfoToMQ(JSONObject contentInfo) {
        try {
            // TODO: 集成RocketMQ发布器
            log.debug("发布指令内容信息到MQ - 网关ID: {}, 任务ID: {}",
                    contentInfo.getStr("gateway_id"), contentInfo.getInt("task_id"));

            // 模拟MQ发布
            // rocketMQPublisher.publish("gateway_command_content", contentInfo.toString());

        } catch (Exception e) {
            log.error("发布指令内容信息到MQ失败", e);
        }
    }

    /**
     * 记录查询事件
     */
    private void recordCommandContentQueryEvent(String gatewayId, Integer taskId) {
        try {
            JSONObject queryEvent = new JSONObject();
            queryEvent.set("gateway_id", gatewayId);
            queryEvent.set("task_id", taskId);
            queryEvent.set("query_type", "command_content");
            queryEvent.set("query_time", System.currentTimeMillis() / 1000);
            queryEvent.set("event_type", "command_content_query");

            // TODO: 发送查询事件到MQ
            log.debug("记录指令内容查询事件 - 事件: {}", queryEvent);

        } catch (Exception e) {
            log.error("记录查询事件失败 - 网关ID: {}, 任务ID: {}", gatewayId, taskId, e);
        }
    }

    /**
     * 记录指令内容信息事件
     */
    private void recordCommandContentInfoEvent(String gatewayId, Integer taskId, Object content) {
        try {
            JSONObject infoEvent = new JSONObject();
            infoEvent.set("gateway_id", gatewayId);
            infoEvent.set("task_id", taskId);
            infoEvent.set("content_available", content != null);
            infoEvent.set("content_type", determineContentType(content));
            infoEvent.set("info_time", System.currentTimeMillis() / 1000);
            infoEvent.set("event_type", "command_content_info");

            // TODO: 发送信息事件到MQ或记录到数据库
            log.debug("记录指令内容信息事件 - 事件: {}", infoEvent);

        } catch (Exception e) {
            log.error("记录指令内容信息事件失败 - 网关ID: {}, 任务ID: {}", gatewayId, taskId, e);
        }
    }

    /**
     * 批量查询多个指令的内容
     *
     * @param gatewayId 网关ID
     * @param taskIds   任务ID列表
     * @return 查询结果统计
     */
    public BatchContentQueryResult batchQueryCommandContent(String gatewayId, java.util.List<Integer> taskIds) {
        int successCount = 0;
        int failCount = 0;
        java.util.List<Integer> failedTasks = new java.util.ArrayList<>();

        for (Integer taskId : taskIds) {
            boolean success = queryCommandContent(gatewayId, taskId);
            if (success) {
                successCount++;
            } else {
                failCount++;
                failedTasks.add(taskId);
            }

            // 批量查询间隔
            try {
                Thread.sleep(200); // 200ms间隔，避免频繁查询
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }

        BatchContentQueryResult result = new BatchContentQueryResult();
        result.setGatewayId(gatewayId);
        result.setTotalCount(taskIds.size());
        result.setSuccessCount(successCount);
        result.setFailCount(failCount);
        result.setFailedTasks(failedTasks);

        log.info("批量查询指令内容完成 - 网关ID: {}, 总数: {}, 成功: {}, 失败: {}",
                gatewayId, taskIds.size(), successCount, failCount);

        return result;
    }

    /**
     * 批量内容查询结果
     */
    public static class BatchContentQueryResult {
        private String gatewayId;
        private int totalCount;
        private int successCount;
        private int failCount;
        private java.util.List<Integer> failedTasks;

        // Getters and Setters
        public String getGatewayId() {
            return gatewayId;
        }

        public void setGatewayId(String gatewayId) {
            this.gatewayId = gatewayId;
        }

        public int getTotalCount() {
            return totalCount;
        }

        public void setTotalCount(int totalCount) {
            this.totalCount = totalCount;
        }

        public int getSuccessCount() {
            return successCount;
        }

        public void setSuccessCount(int successCount) {
            this.successCount = successCount;
        }

        public int getFailCount() {
            return failCount;
        }

        public void setFailCount(int failCount) {
            this.failCount = failCount;
        }

        public java.util.List<Integer> getFailedTasks() {
            return failedTasks;
        }

        public void setFailedTasks(java.util.List<Integer> failedTasks) {
            this.failedTasks = failedTasks;
        }
    }

    /**
     * 获取指令内容查询统计信息
     */
    public JSONObject getCommandContentStatistics(String gatewayId) {
        JSONObject stats = new JSONObject();
        stats.set("gateway_id", gatewayId);
        stats.set("total_content_queries", 0); // TODO: 从统计服务获取
        stats.set("successful_queries", 0);
        stats.set("failed_queries", 0);
        stats.set("last_query_time", 0);
        return stats;
    }

    /**
     * 获取任务详细信息
     */
    public JSONObject getTaskInfo(String gatewayId, Integer taskId) {
        // TODO: 实际环境中应该从数据库或缓存中查询
        JSONObject taskInfo = new JSONObject();
        taskInfo.set("gateway_id", gatewayId);
        taskInfo.set("task_id", taskId);
        taskInfo.set("status", "unknown");
        taskInfo.set("content", null);
        taskInfo.set("create_time", 0);
        taskInfo.set("last_query_time", 0);
        return taskInfo;
    }
}