package com.fx.broker.tcp;

import com.fx.common.swagger.annotation.EnableCustomSwagger2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.web.bind.annotation.CrossOrigin;

/**
 * Broker TCP 模块启动类
 * 负责启动TCP协议网关服务
 *
 * <AUTHOR>
 */
@Slf4j
@EnableCustomSwagger2
@CrossOrigin(origins = "*", maxAge = 3600)
@SpringBootApplication(exclude = { DataSourceAutoConfiguration.class, HibernateJpaAutoConfiguration.class })
@EnableDiscoveryClient
@EnableFeignClients(basePackages = { "com.fx.link.api", "com.fx.system.api" })
@ComponentScan(basePackages = { "com.fx" })
public class FxBrokerTcpApplication {

    public static void main(String[] args) {
        try {
            SpringApplication.run(FxBrokerTcpApplication.class, args);
            log.info("(♥◠‿◠)ﾉﾞ  Broker-Tcp 模块启动成功   ლ(´ڡ`ლ)ﾞ  ");
            System.out.println("(♥◠‿◠)ﾉﾞ  Broker-Tcp 模块启动成功   ლ(´ڡ`ლ)ﾞ  ");
        } catch (Exception e) {
            log.error("Broker-Tcp 模块启动失败", e);
            System.err.println("Broker-Tcp 模块启动失败: " + e.getMessage());
            System.exit(1);
        }
    }
}
