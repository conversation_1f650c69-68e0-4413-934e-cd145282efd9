package com.fx.broker.tcp.mq.publisher;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.fx.common.core.domain.ThingModelMessage;
import com.fx.common.iot.util.UniqueIdUtil;
import com.fx.common.rocketmq.constant.ConsumerTopicConstant;
import com.fx.link.api.domain.device.model.DeviceInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * RocketMQ发布器
 * 负责将 TCP 网关消息发布到 RocketMQ 各个主题
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class RocketMQPublisher {

    @Resource
    private RocketMQTemplate rocketMQTemplate;

    /**
     * 发布上行数据
     *
     * @param deviceInfo  设备信息
     * @param messageData 消息数据
     * @return 是否发布成功
     */
    public boolean publishUplinkData(DeviceInfo deviceInfo, JSONObject messageData) {
        return publishMessage(deviceInfo, messageData,
                ThingModelMessage.TYPE_PROPERTY, ThingModelMessage.ID_PROPERTY_POST,
                ConsumerTopicConstant.FX_THING_MODEL_MSG);
    }

    /**
     * 发布上行状态
     *
     * @param deviceInfo  设备信息
     * @param messageData 消息数据
     * @param identifier  标识符
     * @return 是否发布成功
     */
    public boolean publishUplinkState(DeviceInfo deviceInfo, JSONObject messageData, String identifier) {
        return publishMessage(deviceInfo, messageData,
                ThingModelMessage.TYPE_STATE, identifier,
                ConsumerTopicConstant.FX_LINKS_DEVICE_STATE);
    }

    /**
     * 发布上行指令响应
     *
     * @param deviceInfo  设备信息
     * @param messageData 消息数据
     * @param identifier  标识符
     * @return 是否发布成功
     */
    public boolean publishUplinkCommandResponse(DeviceInfo deviceInfo, JSONObject messageData, String identifier) {
        return publishMessage(deviceInfo, messageData,
                ThingModelMessage.TYPE_COMMAND, identifier,
                ConsumerTopicConstant.FX_TCP_COMMAND_REPLY);
    }

    /**
     * 统一消息发布方法
     *
     * @param deviceInfo  设备信息
     * @param messageData 消息数据
     * @param thingType   事物类型
     * @param identifier  标识符
     * @param topic       主题
     * @return 是否发布成功
     */
    private boolean publishMessage(DeviceInfo deviceInfo, JSONObject messageData,
                                   String thingType, String identifier, String topic) {

        // 验证设备信息
        if (ObjectUtil.isNull(deviceInfo)) {
            log.warn("[publishMessage][设备信息为空，跳过发布消息]");
            return false;
        }

        if (StrUtil.isBlank(deviceInfo.getDeviceKey())) {
            log.warn("[publishMessage][设备Key为空，跳过发布消息 - 设备信息: {}]", deviceInfo);
            return false;
        }

        // 验证必要参数
        if (StrUtil.isBlank(thingType) || StrUtil.isBlank(identifier) || StrUtil.isBlank(topic)) {
            log.error("[publishMessage][发布消息参数无效 - thingType: {}, identifier: {}, topic: {}]",
                    thingType, identifier, topic);
            return false;
        }

        try {
            // 构建消息对象
            ThingModelMessage message = ThingModelMessage.builder()
                    .mid(UniqueIdUtil.newRequestId())
                    .deviceKey(deviceInfo.getDeviceKey())
                    .deviceNo(deviceInfo.getDeviceNo())
                    .deviceName(deviceInfo.getDeviceName())
                    .productKey(deviceInfo.getProductKey())
                    .projectId(deviceInfo.getProjectId())
                    .uid(deviceInfo.getUid())
                    .type(thingType)
                    .identifier(identifier)
                    .data(messageData)
                    .code(200)
                    .time(System.currentTimeMillis())
                    .occurred(System.currentTimeMillis())
                    .build();

            return sendMessage(topic, message);

        } catch (Exception e) {
            log.error("[publishMessage][构建消息对象异常 - 设备Key: {}]", deviceInfo.getDeviceKey(), e);
            return false;
        }
    }

    /**
     * 发送消息
     *
     * @param topic   主题
     * @param message 消息对象
     * @return 是否发送成功
     */
    private boolean sendMessage(String topic, ThingModelMessage message) {
        try {
            if (ObjectUtil.isNull(message) || StrUtil.isBlank(topic)) {
                return false;
            }

            rocketMQTemplate.convertAndSend(topic, message);
            log.info("[sendMessage][发送消息成功 - 主题: {} - 消息: {}]", topic, message);
            return true;

        } catch (Exception e) {
            log.error("[sendMessage][发送消息失败 - 主题: {} - 消息: {}]", topic, message, e);
            return false;
        }
    }
}