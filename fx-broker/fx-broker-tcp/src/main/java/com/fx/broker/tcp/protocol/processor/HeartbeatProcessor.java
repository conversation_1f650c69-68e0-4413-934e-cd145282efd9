package com.fx.broker.tcp.protocol.processor;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.fx.broker.tcp.domain.ProtocolMessage;
import com.fx.broker.tcp.enums.FunctionCodeEnum;
import com.fx.broker.tcp.enums.ReturnCodeEnum;
import com.fx.broker.tcp.manager.ConnectionManager;
import com.fx.broker.tcp.mq.publisher.RocketMQPublisher;
import com.fx.broker.tcp.service.GatewayManageService;
import com.fx.common.core.domain.R;
import com.fx.common.core.domain.ThingModelMessage;
import com.fx.link.api.RemoteDeviceService;
import com.fx.link.api.domain.device.model.DeviceInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 功能码 0：心跳处理器
 * 处理客户端发送的心跳请求，更新连接活跃时间
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class HeartbeatProcessor extends BaseCommandProcessor {

    @Resource
    private ConnectionManager connectionManager;

    @Resource
    private GatewayManageService gatewayManageService;

    @Resource
    private RemoteDeviceService remoteDeviceService;

    @Resource
    private RocketMQPublisher rocketMQPublisher;

    @Override
    public Integer getFunctionCode() {
        return FunctionCodeEnum.HEARTBEAT.getCode();
    }

    @Override
    public String getProcessorName() {
        return "心跳处理器";
    }

    @Override
    protected ProtocolMessage doProcess(ProtocolMessage message) {
        try {
            // 1. 更新连接活跃时间
            updateConnectionActiveTime(message);

            // 2. 处理心跳消息
            if (isClientHeartbeatRequest(message)) {
                return processClientHeartbeatRequest(message);
            } else {
                return processClientHeartbeatResponse(message);
            }

        } catch (Exception e) {
            logProcessing(message, "[doProcess][处理心跳消息时发生异常]");
            log.error("[doProcess][处理心跳消息异常 - 连接ID: {}]", message.getConnectionId(), e);
            return createErrorResponse(message, ReturnCodeEnum.MEMORY_INSUFFICIENT);
        }
    }

    /**
     * 更新连接活跃时间
     */
    private void updateConnectionActiveTime(ProtocolMessage message) {
        try {
            String connectionId = message.getConnectionId();
            if (connectionId != null) {
                connectionManager.updateLastActiveTime(connectionId);
                log.debug("[updateConnectionActiveTime][更新连接活跃时间 - 连接ID: {}]", connectionId);
            }
        } catch (Exception e) {
            log.warn("[updateConnectionActiveTime][更新连接活跃时间失败 - 连接ID: {}]", message.getConnectionId(), e);
        }
    }

    /**
     * 检查是否为客户端心跳请求
     */
    private boolean isClientHeartbeatRequest(ProtocolMessage message) {
        return message.isUplink() && !message.isResponse();
    }

    /**
     * 处理客户端心跳请求
     */
    private ProtocolMessage processClientHeartbeatRequest(ProtocolMessage message) {
        logProcessing(message, "[processClientHeartbeatRequest][处理客户端心跳请求]");

        // 1. 处理网关心跳
        handleGatewayHeartbeat(message);

        // 2. 返回成功响应
        return createSuccessResponse(message, new JSONObject());
    }

    /**
     * 处理客户端心跳响应
     */
    private ProtocolMessage processClientHeartbeatResponse(ProtocolMessage message) {
        // 1. 检查返回码
        if (isHeartbeatResponseSuccess(message)) {
            logProcessing(message, "[processClientHeartbeatResponse][客户端心跳响应正常]");
        } else {
            logProcessing(message, String.format("[processClientHeartbeatResponse][客户端心跳响应异常 - 返回码: %d]",
                    message.getRetCode()));
        }

        // 2. 处理网关心跳
        handleGatewayHeartbeat(message);

        // 3. 心跳响应不需要回复
        return NO_RESPONSE;
    }

    /**
     * 检查心跳响应是否成功
     */
    private boolean isHeartbeatResponseSuccess(ProtocolMessage message) {
        return message.getRetCode() != null && message.getRetCode() == 0;
    }

    /**
     * 处理网关心跳相关操作
     */
    private void handleGatewayHeartbeat(ProtocolMessage message) {
        try {
            String gatewayId = getGatewayIdFromMessage(message);
            if (gatewayId == null) {
                logProcessing(message, "[handleGatewayHeartbeat][无法获取网关ID，跳过心跳处理]");
                return;
            }

            // 1. 更新网关心跳时间
            gatewayManageService.updateHeartbeat(gatewayId);

            // 2. 发送设备上线消息
            sendDeviceOnlineMessageIfNeeded(gatewayId);

            logProcessing(message, String.format("[handleGatewayHeartbeat][处理网关心跳 - 网关ID: %s]", gatewayId));

        } catch (Exception e) {
            logProcessing(message, "[handleGatewayHeartbeat][处理网关心跳失败]");
            log.warn("[handleGatewayHeartbeat][处理网关心跳失败 - 连接ID: {}]", message.getConnectionId(), e);
        }
    }

    /**
     * 从消息中获取网关ID
     */
    private String getGatewayIdFromMessage(ProtocolMessage message) {
        // 1. 优先从消息中获取网关ID
        String gatewayId = message.getGatewayId();
        if (gatewayId != null) {
            return gatewayId;
        }

        // 2. 通过连接ID获取网关ID
        String connectionId = message.getConnectionId();
        if (connectionId != null) {
            ConnectionManager.ConnectionInfo connectionInfo = connectionManager.getConnection(connectionId);
            if (connectionInfo != null && connectionInfo.getGatewayId() != null) {
                gatewayId = connectionInfo.getGatewayId();
                // 设置到消息中，便于后续使用
                message.setGatewayId(gatewayId);
                return gatewayId;
            }
        }

        return null;
    }

    /**
     * 发送设备上线消息
     */
    private void sendDeviceOnlineMessageIfNeeded(String gatewayId) {
        try {
            // 1. 获取设备信息
            DeviceInfo deviceInfo = getDeviceInfo(gatewayId);
            if (deviceInfo == null) {
                log.warn("[sendDeviceOnlineMessageIfNeeded][无法获取设备信息，跳过发送设备上线消息 - 网关ID: {}]", gatewayId);
                return;
            }

            // 2. 发送设备上线消息
            boolean success = rocketMQPublisher.publishUplinkState(deviceInfo, null,
                    ThingModelMessage.ID_TYPE_STATE_ONLINE);
            if (success) {
                log.debug("[sendDeviceOnlineMessageIfNeeded][设备上线消息发送成功 - 网关ID: {}, 设备Key: {}]",
                        gatewayId, deviceInfo.getDeviceKey());
            } else {
                log.warn("[sendDeviceOnlineMessageIfNeeded][设备上线消息发送失败 - 网关ID: {}, 设备Key: {}]",
                        gatewayId, deviceInfo.getDeviceKey());
            }

        } catch (Exception e) {
            log.error("[sendDeviceOnlineMessageIfNeeded][发送设备上线消息异常 - 网关ID: {}]", gatewayId, e);
        }
    }

    /**
     * 获取设备信息
     */
    private DeviceInfo getDeviceInfo(String gatewayId) {
        try {
            // 1. 调用远程服务获取设备信息
            R<DeviceInfo> result = remoteDeviceService.getDeviceInfoByKey(gatewayId);

            // 2. 验证返回结果
            if (result == null || result.getCode() != 200 || result.getData() == null) {
                log.warn("[getDeviceInfo][远程设备服务调用失败 - 网关ID: {}, 返回码: {}]", gatewayId,
                        result != null ? result.getCode() : "null");
                return null;
            }

            DeviceInfo deviceInfo = result.getData();
            if (StrUtil.isBlank(deviceInfo.getDeviceKey())) {
                log.warn("[getDeviceInfo][设备信息中设备Key为空 - 网关ID: {}]", gatewayId);
                return null;
            }

            log.debug("[getDeviceInfo][成功获取设备信息 - 网关ID: {}, 设备Key: {}]", gatewayId, deviceInfo.getDeviceKey());
            return deviceInfo;

        } catch (Exception e) {
            log.error("[getDeviceInfo][调用远程设备服务异常 - 网关ID: {}]", gatewayId, e);
            return null;
        }
    }

}