package com.fx.broker.tcp.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONObject;
import com.fx.broker.tcp.domain.CommandTask;
import com.fx.broker.tcp.domain.ProtocolMessage;
import com.fx.broker.tcp.enums.ReturnCodeEnum;
import com.fx.broker.tcp.mq.publisher.RocketMQPublisher;
import com.fx.broker.tcp.protocol.ProtocolHandler;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 指令服务
 * 负责指令的创建、管理、下发、执行跟踪和结果处理
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class CommandService {

    @Autowired
    private ProtocolHandler protocolHandler;

    @Autowired
    private RocketMQPublisher rocketMQPublisher;

    @Autowired
    @Qualifier("stringObjectRedisTemplate")
    private RedisTemplate<String, Object> redisTemplate;

    @Value("${command.service.max-retry:3}")
    private int maxRetryCount; // 最大重试次数

    @Value("${command.service.timeout:300000}")
    private long commandTimeout; // 指令超时时间（毫秒），默认5分钟

    @Value("${command.service.max-pending:100}")
    private int maxPendingCommands; // 单个网关最大待处理指令数

    @Value("${command.service.cleanup-interval:3600000}")
    private long cleanupInterval; // 清理间隔（毫秒），默认1小时

    /**
     * 内存中的指令缓存（网关ID -> 指令列表）
     */
    private final ConcurrentHashMap<String, List<CommandTask>> gatewayCommands = new ConcurrentHashMap<>();

    /**
     * 指令执行统计
     */
    private final AtomicLong totalCommands = new AtomicLong(0);
    private final AtomicLong successCommands = new AtomicLong(0);
    private final AtomicLong failedCommands = new AtomicLong(0);
    private final AtomicLong timeoutCommands = new AtomicLong(0);

    /**
     * 指令任务ID生成器
     */
    private final AtomicLong taskIdGenerator = new AtomicLong(0);

    /**
     * Redis Key前缀
     */
    private static final String COMMAND_REDIS_PREFIX = "fx:broker:command:";
    private static final String GATEWAY_COMMANDS_KEY = COMMAND_REDIS_PREFIX + "gateway:";

    /**
     * 创建并下发指令
     * 
     * @param gatewayId   网关ID
     * @param commandType 指令类型
     * @param content     指令内容
     * @return 指令任务
     */
    public CommandTask createCommand(String gatewayId, String commandType, JSONObject content) {
        try {
            log.debug("创建指令 - 网关ID: {}, 指令类型: {}", gatewayId, commandType);

            // 检查待处理指令数量限制
            if (getPendingCommandCount(gatewayId) >= maxPendingCommands) {
                log.warn("网关待处理指令数量超过限制 - 网关ID: {}, 限制: {}", gatewayId, maxPendingCommands);
                return null;
            }

            // 创建指令任务
            CommandTask commandTask = new CommandTask();
            commandTask.setTaskId(generateTaskId());
            commandTask.setGatewayId(gatewayId);
            commandTask.setCommandType(commandType);
            commandTask.setContent(content);
            commandTask.setStatus(CommandTask.CommandStatus.PENDING);
            commandTask.setCreatedTime(LocalDateTime.now());
            commandTask.setRetryCount(0);
            commandTask.setMaxRetryCount(maxRetryCount);
            commandTask.setTimeout(commandTimeout);

            // 保存到内存缓存
            addCommandToGateway(gatewayId, commandTask);

            // 持久化到Redis
            saveCommandToRedis(commandTask);

            // 下发指令
            boolean sent = sendCommandToGateway(commandTask);
            if (sent) {
                commandTask.setStatus(CommandTask.CommandStatus.SENT);
                commandTask.setSentTime(LocalDateTime.now());
                updateCommandInRedis(commandTask);

                totalCommands.incrementAndGet();
                log.info("指令下发成功 - 任务ID: {}, 网关ID: {}", commandTask.getTaskId(), gatewayId);
            } else {
                commandTask.setStatus(CommandTask.CommandStatus.FAILED);
                commandTask.setErrorMessage("网关不在线或发送失败");
                updateCommandInRedis(commandTask);

                failedCommands.incrementAndGet();
                log.warn("指令下发失败 - 任务ID: {}, 网关ID: {}", commandTask.getTaskId(), gatewayId);
            }

            return commandTask;

        } catch (Exception e) {
            log.error("创建指令异常 - 网关ID: {}, 指令类型: {}", gatewayId, commandType, e);
            return null;
        }
    }

    /**
     * 处理指令执行结果
     * 
     * @param message 结果消息
     * @return 处理结果
     */
    public ProtocolMessage processCommandResult(ProtocolMessage message) {
        try {
            JSONObject data = message.getData();
            Long taskId = data.getLong("task_id");
            JSONObject result = data.getJSONObject("result");

            if (taskId == null) {
                log.warn("指令结果缺少任务ID - 网关ID: {}", message.getGatewayId());
                return message.createErrorResponse(ReturnCodeEnum.MISSING_PARAMETER.getCode());
            }

            log.debug("处理指令执行结果 - 任务ID: {}, 网关ID: {}", taskId, message.getGatewayId());

            // 获取指令任务
            CommandTask commandTask = findCommandTask(message.getGatewayId(), taskId);
            if (commandTask == null) {
                log.warn("未找到指令任务 - 任务ID: {}, 网关ID: {}", taskId, message.getGatewayId());
                return message.createErrorResponse(ReturnCodeEnum.MISSING_PARAMETER.getCode());
            }

            // 更新指令执行结果
            commandTask.setResult(result);
            commandTask.setCompletedTime(LocalDateTime.now());
            commandTask.setStatus(CommandTask.CommandStatus.COMPLETED);

            // 更新缓存和Redis
            updateCommandInRedis(commandTask);

            // 发布执行结果到MQ
            publishCommandResult(commandTask);

            successCommands.incrementAndGet();
            log.info("指令执行完成 - 任务ID: {}, 网关ID: {}", taskId, message.getGatewayId());

            return message.createSuccessResponse();

        } catch (Exception e) {
            log.error("处理指令执行结果异常 - 网关ID: {}", message.getGatewayId(), e);
            return message.createErrorResponse(ReturnCodeEnum.MEMORY_INSUFFICIENT.getCode());
        }
    }

    /**
     * 获取网关待处理指令数量
     * 
     * @param gatewayId 网关ID
     * @return 指令数量
     */
    public int getPendingCommandCount(String gatewayId) {
        List<CommandTask> commands = gatewayCommands.get(gatewayId);
        if (commands == null) {
            return 0;
        }

        return (int) commands.stream()
                .filter(cmd -> cmd.getStatus() == CommandTask.CommandStatus.PENDING ||
                        cmd.getStatus() == CommandTask.CommandStatus.SENT)
                .count();
    }

    /**
     * 获取网关所有待处理指令ID列表
     * 
     * @param gatewayId 网关ID
     * @return 指令ID列表
     */
    public List<Long> getPendingCommandIds(String gatewayId) {
        List<CommandTask> commands = gatewayCommands.get(gatewayId);
        if (commands == null) {
            return new ArrayList<>();
        }

        return commands.stream()
                .filter(cmd -> cmd.getStatus() == CommandTask.CommandStatus.PENDING ||
                        cmd.getStatus() == CommandTask.CommandStatus.SENT)
                .map(CommandTask::getTaskId)
                .sorted()
                .collect(ArrayList::new, List::add, List::addAll);
    }

    /**
     * 根据任务ID获取指令
     * 
     * @param gatewayId 网关ID
     * @param taskId    任务ID
     * @return 指令
     */
    public CommandTask getCommandTask(String gatewayId, Long taskId) {
        return findCommandTask(gatewayId, taskId);
    }

    /**
     * 获取正在执行的指令ID列表
     * 
     * @param gatewayId 网关ID
     * @return 指令ID列表
     */
    public List<Long> getExecutingCommandIds(String gatewayId) {
        List<CommandTask> commands = gatewayCommands.get(gatewayId);
        if (commands == null) {
            return new ArrayList<>();
        }

        return commands.stream()
                .filter(cmd -> cmd.getStatus() == CommandTask.CommandStatus.PENDING ||
                        cmd.getStatus() == CommandTask.CommandStatus.SENT)
                .map(CommandTask::getTaskId)
                .sorted()
                .collect(ArrayList::new, List::add, List::addAll);
    }

    /**
     * 获取指令执行结果
     * 
     * @param gatewayId 网关ID
     * @param taskId    任务ID
     * @return 指令执行结果
     */
    public List<Long> getCompletedCommandIds(String gatewayId) {
        List<CommandTask> commands = gatewayCommands.get(gatewayId);
        if (commands == null) {
            return new ArrayList<>();
        }

        return commands.stream()
                .filter(cmd -> cmd.getStatus() == CommandTask.CommandStatus.COMPLETED)
                .map(CommandTask::getTaskId)
                .sorted()
                .collect(ArrayList::new, List::add, List::addAll);
    }

    /**
     * 获取指令执行结果
     * 
     * @param gatewayId 网关ID
     * @param taskId    任务ID
     * @return 指令执行结果
     */
    public JSONObject getCommandResult(String gatewayId, Long taskId) {
        CommandTask commandTask = findCommandTask(gatewayId, taskId);
        return commandTask != null ? commandTask.getResult() : null;
    }

    /**
     * 清理已完成的指令
     * 
     * @param gatewayId 网关ID
     */
    public int cleanupCompletedCommands(String gatewayId, long beforeTimestamp) {
        List<CommandTask> commands = gatewayCommands.get(gatewayId);
        if (commands != null) {
            int count = 0;
            for (CommandTask commandTask : commands) {
                if (commandTask.getStatus() == CommandTask.CommandStatus.COMPLETED
                        && commandTask.getCreatedTime().toEpochSecond(ZoneOffset.UTC) < beforeTimestamp) {
                    count++;
                }
            }
            return count;
        }
        return 0;
    }

    /**
     * 根据任务ID获取指令内容
     * 
     * @param gatewayId 网关ID
     * @param taskId    任务ID
     * @return 指令内容
     */
    public JSONObject getCommandContent(String gatewayId, Long taskId) {
        CommandTask commandTask = findCommandTask(gatewayId, taskId);
        return commandTask != null ? commandTask.getContent() : null;
    }

    /**
     * 重试失败的指令
     * 
     * @param gatewayId 网关ID
     * @param taskId    任务ID
     * @return 是否成功
     */
    public boolean retryCommand(String gatewayId, Long taskId) {
        try {
            CommandTask commandTask = findCommandTask(gatewayId, taskId);
            if (commandTask == null) {
                log.warn("未找到需要重试的指令任务 - 任务ID: {}, 网关ID: {}", taskId, gatewayId);
                return false;
            }

            if (commandTask.getRetryCount() >= commandTask.getMaxRetryCount()) {
                log.warn("指令重试次数已达上限 - 任务ID: {}, 网关ID: {}", taskId, gatewayId);
                return false;
            }

            // 增加重试计数
            commandTask.setRetryCount(commandTask.getRetryCount() + 1);
            commandTask.setStatus(CommandTask.CommandStatus.PENDING);

            // 重新下发指令
            boolean sent = sendCommandToGateway(commandTask);
            if (sent) {
                commandTask.setStatus(CommandTask.CommandStatus.SENT);
                commandTask.setSentTime(LocalDateTime.now());
                updateCommandInRedis(commandTask);

                log.info("指令重试成功 - 任务ID: {}, 网关ID: {}, 重试次数: {}",
                        taskId, gatewayId, commandTask.getRetryCount());
                return true;
            } else {
                commandTask.setStatus(CommandTask.CommandStatus.FAILED);
                commandTask.setErrorMessage("重试失败：网关不在线或发送失败");
                updateCommandInRedis(commandTask);

                log.warn("指令重试失败 - 任务ID: {}, 网关ID: {}", taskId, gatewayId);
                return false;
            }

        } catch (Exception e) {
            log.error("重试指令异常 - 任务ID: {}, 网关ID: {}", taskId, gatewayId, e);
            return false;
        }
    }

    /**
     * 取消指令
     * 
     * @param gatewayId 网关ID
     * @param taskId    任务ID
     * @return 是否成功
     */
    public boolean cancelCommand(String gatewayId, Long taskId) {
        try {
            CommandTask commandTask = findCommandTask(gatewayId, taskId);
            if (commandTask == null) {
                log.warn("未找到需要取消的指令任务 - 任务ID: {}, 网关ID: {}", taskId, gatewayId);
                return false;
            }

            if (commandTask.getStatus() == CommandTask.CommandStatus.COMPLETED ||
                    commandTask.getStatus() == CommandTask.CommandStatus.CANCELLED) {
                log.warn("指令已完成或已取消，无法取消 - 任务ID: {}, 网关ID: {}, 状态: {}",
                        taskId, gatewayId, commandTask.getStatus());
                return false;
            }

            // 更新状态为已取消
            commandTask.setStatus(CommandTask.CommandStatus.CANCELLED);
            commandTask.setCompletedTime(LocalDateTime.now());
            commandTask.setErrorMessage("指令已被取消");

            // 更新Redis
            updateCommandInRedis(commandTask);

            // 从内存缓存中移除
            removeCommandFromGateway(gatewayId, taskId);

            log.info("指令已取消 - 任务ID: {}, 网关ID: {}", taskId, gatewayId);
            return true;

        } catch (Exception e) {
            log.error("取消指令异常 - 任务ID: {}, 网关ID: {}", taskId, gatewayId, e);
            return false;
        }
    }

    /**
     * 清理超时和完成的指令
     */
    public void cleanupCommands() {
        log.debug("开始清理超时和完成的指令");

        try {
            LocalDateTime now = LocalDateTime.now();
            int totalCleaned = 0;
            int timeoutCleaned = 0;

            for (Map.Entry<String, List<CommandTask>> entry : gatewayCommands.entrySet()) {
                String gatewayId = entry.getKey();
                List<CommandTask> commands = entry.getValue();

                Iterator<CommandTask> iterator = commands.iterator();
                while (iterator.hasNext()) {
                    CommandTask commandTask = iterator.next();

                    boolean shouldClean = false;

                    // 检查是否超时
                    if (commandTask.getStatus() == CommandTask.CommandStatus.SENT) {
                        LocalDateTime sentTime = commandTask.getSentTime();
                        if (sentTime != null &&
                                sentTime.plusNanos(commandTask.getTimeout() * 1_000_000).isBefore(now)) {
                            // 指令超时
                            commandTask.setStatus(CommandTask.CommandStatus.TIMEOUT);
                            commandTask.setCompletedTime(now);
                            commandTask.setErrorMessage("指令执行超时");
                            updateCommandInRedis(commandTask);

                            timeoutCommands.incrementAndGet();
                            timeoutCleaned++;
                            shouldClean = true;
                        }
                    }

                    // 检查是否为已完成的旧指令（超过清理间隔）
                    if (commandTask.getStatus() == CommandTask.CommandStatus.COMPLETED ||
                            commandTask.getStatus() == CommandTask.CommandStatus.CANCELLED ||
                            commandTask.getStatus() == CommandTask.CommandStatus.TIMEOUT) {

                        LocalDateTime completedTime = commandTask.getCompletedTime();
                        if (completedTime != null &&
                                completedTime.plusNanos(cleanupInterval * 1_000_000).isBefore(now)) {
                            // 从Redis中删除
                            deleteCommandFromRedis(commandTask.getTaskId());
                            shouldClean = true;
                        }
                    }

                    if (shouldClean) {
                        iterator.remove();
                        totalCleaned++;
                    }
                }
            }

            if (totalCleaned > 0) {
                log.info("指令清理完成 - 总清理数: {}, 超时清理数: {}", totalCleaned, timeoutCleaned);
            }

        } catch (Exception e) {
            log.error("清理指令异常", e);
        }
    }

    /**
     * 下发指令到网关
     * 
     * @param commandTask 指令任务
     * @return 是否成功
     */
    private boolean sendCommandToGateway(CommandTask commandTask) {
        try {
            // 创建协议消息
            ProtocolMessage message = new ProtocolMessage();
            message.setIsUplink(false);
            message.setIsResponse(false);
            message.setPackId(0); // 使用默认包序号
            message.setCode(5); // 下发指令功能码

            JSONObject data = new JSONObject();
            data.set("task_id", commandTask.getTaskId());
            data.set("content", commandTask.getContent());
            message.setData(data);

            // 发送消息
            return protocolHandler.sendDownlinkMessage(commandTask.getGatewayId(), message);

        } catch (Exception e) {
            log.error("下发指令异常 - 任务ID: {}, 网关ID: {}",
                    commandTask.getTaskId(), commandTask.getGatewayId(), e);
            return false;
        }
    }

    /**
     * 发布指令执行结果到MQ
     * 
     * @param commandTask 指令任务
     */
    private void publishCommandResult(CommandTask commandTask) {
        try {
            JSONObject resultData = new JSONObject();
            resultData.set("taskId", commandTask.getTaskId());
            resultData.set("gatewayId", commandTask.getGatewayId());
            resultData.set("commandType", commandTask.getCommandType());
            resultData.set("result", commandTask.getResult());
            resultData.set("status", commandTask.getStatus().name());
            resultData.set("completedTime", DateUtil.formatDateTime(DateUtil.date()));

            // rocketMQPublisher.publishDownlinkCommand(commandTask.getGatewayId(),
            // resultData);
        } catch (Exception e) {
            log.error("发布指令执行结果到MQ失败 - 任务ID: {}", commandTask.getTaskId(), e);
        }
    }

    /**
     * 添加指令到网关缓存
     * 
     * @param gatewayId   网关ID
     * @param commandTask 指令任务
     */
    private void addCommandToGateway(String gatewayId, CommandTask commandTask) {
        gatewayCommands.computeIfAbsent(gatewayId, k -> new ArrayList<>()).add(commandTask);
    }

    /**
     * 从网关缓存中移除指令
     * 
     * @param gatewayId 网关ID
     * @param taskId    任务ID
     */
    private void removeCommandFromGateway(String gatewayId, Long taskId) {
        List<CommandTask> commands = gatewayCommands.get(gatewayId);
        if (commands != null) {
            commands.removeIf(cmd -> Objects.equals(cmd.getTaskId(), taskId));
        }
    }

    /**
     * 查找指令任务
     * 
     * @param gatewayId 网关ID
     * @param taskId    任务ID
     * @return 指令任务
     */
    private CommandTask findCommandTask(String gatewayId, Long taskId) {
        List<CommandTask> commands = gatewayCommands.get(gatewayId);
        if (commands == null) {
            return null;
        }

        return commands.stream()
                .filter(cmd -> Objects.equals(cmd.getTaskId(), taskId))
                .findFirst()
                .orElse(null);
    }

    /**
     * 保存指令到Redis
     * 
     * @param commandTask 指令任务
     */
    private void saveCommandToRedis(CommandTask commandTask) {
        try {
            String key = COMMAND_REDIS_PREFIX + commandTask.getTaskId();
            redisTemplate.opsForValue().set(key, commandTask, commandTimeout + cleanupInterval, TimeUnit.MILLISECONDS);

            // 添加到网关指令集合
            String gatewayKey = GATEWAY_COMMANDS_KEY + commandTask.getGatewayId();
            redisTemplate.opsForSet().add(gatewayKey, commandTask.getTaskId());
            redisTemplate.expire(gatewayKey, commandTimeout + cleanupInterval, TimeUnit.MILLISECONDS);

        } catch (Exception e) {
            log.error("保存指令到Redis失败 - 任务ID: {}", commandTask.getTaskId(), e);
        }
    }

    /**
     * 更新Redis中的指令
     * 
     * @param commandTask 指令任务
     */
    private void updateCommandInRedis(CommandTask commandTask) {
        try {
            String key = COMMAND_REDIS_PREFIX + commandTask.getTaskId();
            redisTemplate.opsForValue().set(key, commandTask, commandTimeout + cleanupInterval, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            log.error("更新Redis中的指令失败 - 任务ID: {}", commandTask.getTaskId(), e);
        }
    }

    /**
     * 从Redis删除指令
     * 
     * @param taskId 任务ID
     */
    private void deleteCommandFromRedis(Long taskId) {
        try {
            String key = COMMAND_REDIS_PREFIX + taskId;
            redisTemplate.delete(key);
        } catch (Exception e) {
            log.error("从Redis删除指令失败 - 任务ID: {}", taskId, e);
        }
    }

    /**
     * 生成任务ID
     * 
     * @return 任务ID
     */
    private Long generateTaskId() {
        return taskIdGenerator.incrementAndGet();
    }

    /**
     * 获取指令服务统计信息
     * 
     * @return 统计信息
     */
    public CommandServiceStatistics getStatistics() {
        CommandServiceStatistics stats = new CommandServiceStatistics();
        stats.setTotalCommands(totalCommands.get());
        stats.setSuccessCommands(successCommands.get());
        stats.setFailedCommands(failedCommands.get());
        stats.setTimeoutCommands(timeoutCommands.get());

        // 计算当前待处理指令总数
        int totalPending = gatewayCommands.values().stream()
                .mapToInt(commands -> (int) commands.stream()
                        .filter(cmd -> cmd.getStatus() == CommandTask.CommandStatus.PENDING ||
                                cmd.getStatus() == CommandTask.CommandStatus.SENT)
                        .count())
                .sum();
        stats.setPendingCommands(totalPending);

        // 计算成功率
        long total = totalCommands.get();
        stats.setSuccessRate(total > 0 ? (double) successCommands.get() / total * 100 : 0);

        return stats;
    }

    /**
     * 指令服务统计信息
     */
    @Data
    public static class CommandServiceStatistics {
        private long totalCommands;
        private long successCommands;
        private long failedCommands;
        private long timeoutCommands;
        private int pendingCommands;
        private double successRate;
    }
}