package com.fx.broker.tcp.config;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * TCP模块异步线程配置
 * 为TCP协议处理提供专用的线程池配置
 * 
 * <AUTHOR>
 */
@Slf4j
@Configuration
@EnableAsync
public class TcpAsyncConfig {

    /**
     * 核心线程数（默认线程数）
     */
    @Value("${threadBus.pool.core-pool-size:8}")
    private int corePoolSize;

    /**
     * 最大线程数
     */
    @Value("${threadBus.pool.max-pool-size:16}")
    private int maxPoolSize;

    /**
     * 允许线程空闲时间（单位：默认为秒）
     */
    @Value("${threadBus.pool.keep-alive-time:60}")
    private int keepAliveTime;

    /**
     * 缓冲队列大小
     */
    @Value("${threadBus.pool.queue-capacity:1000}")
    private int queueCapacity;

    /**
     * 线程池名前缀
     */
    @Value("${threadBus.pool.thread-name-prefix:fxlinksAsync-}")
    private String threadNamePrefix;

    /**
     * TCP协议处理专用异步线程池
     * 用于处理TCP连接、消息路由、指令下发等异步任务
     * 
     * @return TCP专用线程池执行器
     */
    @Bean("tcpAsync")
    public Executor tcpAsync() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(corePoolSize);
        executor.setMaxPoolSize(maxPoolSize);
        executor.setQueueCapacity(queueCapacity);
        executor.setKeepAliveSeconds(keepAliveTime);

        // 使用Hutool工具类进行字符串处理
        String tcpThreadNamePrefix = StrUtil.isBlank(threadNamePrefix) ? "fxlinksAsync-" : threadNamePrefix;
        executor.setThreadNamePrefix(tcpThreadNamePrefix + "tcp-");

        // 自定义拒绝策略
        executor.setRejectedExecutionHandler(new TcpRejectedExecutionHandler());

        // 设置线程池等待所有任务都完成再关闭
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(keepAliveTime);

        executor.initialize();

        log.info("[tcpAsync][TCP模块异步线程池配置完成 - 核心线程数: {}, 最大线程数: {}, 队列容量: {}]",
                corePoolSize, maxPoolSize, queueCapacity);

        return executor;
    }

    /**
     * TCP模块专用的拒绝策略
     * 当线程池满时的处理策略
     */
    private static class TcpRejectedExecutionHandler implements RejectedExecutionHandler {

        @Override
        public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
            log.warn("[TcpRejectedExecutionHandler][TCP异步任务被拒绝执行 - 活跃线程数: {}, 队列大小: {}, 任务类型: {}]",
                    executor.getActiveCount(),
                    executor.getQueue().size(),
                    r.getClass().getSimpleName());

            // 使用调用者线程执行任务，避免任务丢失
            if (!executor.isShutdown()) {
                try {
                    r.run();
                    log.info("[TcpRejectedExecutionHandler][TCP异步任务已由调用者线程执行完成]");
                } catch (Exception e) {
                    log.error("[TcpRejectedExecutionHandler][TCP异步任务执行失败]", e);
                }
            }
        }
    }
}