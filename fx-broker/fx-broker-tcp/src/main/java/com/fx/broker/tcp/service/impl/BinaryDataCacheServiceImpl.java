package com.fx.broker.tcp.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.fx.broker.tcp.service.BinaryDataCacheService;
import com.fx.common.core.domain.R;
import com.fx.common.redis.service.RedisService;
import com.fx.system.api.RemoteFileService;
import com.fx.system.api.domain.SysFile;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.concurrent.TimeUnit;

/**
 * 二进制数据缓存服务实现类
 * 负责管理分包数据的缓存、组装和文件上传
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class BinaryDataCacheServiceImpl implements BinaryDataCacheService {

    @Resource
    private RedisService redisService;

    @Resource
    private RemoteFileService remoteFileService;

    @Value("${binary.cache.ttl:3600}")
    private long cacheTtl; // 缓存过期时间（秒），默认1小时

    @Value("${binary.cache.key-prefix:fx:broker:binary:}")
    private String cacheKeyPrefix;

    @Value("${binary.file.extension:jpg}")
    private String fileExtension; // 文件扩展名

    @Override
    public boolean cacheBinaryPacket(String gatewayId, String deviceKey, String deviceName,
            Integer total, Integer num, byte[] binaryData, String time) {
        try {
            // 1. 生成缓存key
            String packetKey = generatePacketKey(gatewayId, deviceKey, num);
            String metadataKey = generateMetadataKey(gatewayId, deviceKey);

            // 2. 缓存二进制数据包
            redisService.setCacheObject(packetKey, binaryData, cacheTtl, TimeUnit.SECONDS);

            // 3. 缓存元数据信息
            JSONObject metadata = getOrCreateMetadata(metadataKey);
            metadata.set("gatewayId", gatewayId);
            metadata.set("deviceKey", deviceKey);
            metadata.set("deviceName", deviceName);
            metadata.set("total", total);
            metadata.set("time", time);
            metadata.set("receivedPackets", metadata.getInt("receivedPackets", 0) + 1);
            metadata.set("lastUpdateTime", System.currentTimeMillis());

            redisService.setCacheObject(metadataKey, metadata, cacheTtl, TimeUnit.SECONDS);

            log.debug("缓存二进制数据包成功 - 网关ID: {}, 设备Key: {}, 包序号: {}/{}",
                    gatewayId, deviceKey, num, total);

            return true;

        } catch (Exception e) {
            log.error("缓存二进制数据包失败 - 网关ID: {}, 设备Key: {}, 包序号: {}/{}",
                    gatewayId, deviceKey, num, total, e);
            return false;
        }
    }

    @Override
    public boolean isAllPacketsReceived(String gatewayId, String deviceKey, Integer total) {
        try {
            String metadataKey = generateMetadataKey(gatewayId, deviceKey);
            JSONObject metadata = redisService.getCacheObject(metadataKey);

            if (metadata == null) {
                return false;
            }

            int receivedPackets = metadata.getInt("receivedPackets", 0);
            return receivedPackets >= total;

        } catch (Exception e) {
            log.error("检查包接收状态失败 - 网关ID: {}, 设备Key: {}", gatewayId, deviceKey, e);
            return false;
        }
    }

    @Override
    public String assembleAndUploadFile(String gatewayId, String deviceKey, String deviceName,
            Integer total, String time) {
        try {
            // 1. 获取元数据
            String metadataKey = generateMetadataKey(gatewayId, deviceKey);
            JSONObject metadata = redisService.getCacheObject(metadataKey);

            if (metadata == null) {
                log.warn("未找到元数据信息 - 网关ID: {}, 设备Key: {}", gatewayId, deviceKey);
                return null;
            }

            // 2. 组装完整的二进制数据
            byte[] completeData = assembleCompleteData(gatewayId, deviceKey, total);
            if (completeData == null) {
                log.error("组装完整数据失败 - 网关ID: {}, 设备Key: {}", gatewayId, deviceKey);
                return null;
            }

            // 3. 生成文件名
            String fileName = generateFileName(gatewayId, deviceKey, time);

            // 4. 上传到文件服务
            String fileUrl = uploadToFileService(completeData, fileName);
            if (StrUtil.isBlank(fileUrl)) {
                log.error("上传文件失败 - 网关ID: {}, 设备Key: {}", gatewayId, deviceKey);
                return null;
            }

            // 5. 清理缓存的分包数据
            clearCachedPackets(gatewayId, deviceKey);

            log.info("文件上传成功 - 网关ID: {}, 设备Key: {}, 文件URL: {}", gatewayId, deviceKey, fileUrl);
            return fileUrl;

        } catch (Exception e) {
            log.error("组装并上传文件失败 - 网关ID: {}, 设备Key: {}", gatewayId, deviceKey, e);
            return null;
        }
    }

    @Override
    public void clearCachedPackets(String gatewayId, String deviceKey) {
        try {
            // 1. 删除所有分包数据
            for (int i = 1; i <= 1000; i++) { // 假设最大1000个包
                String packetKey = generatePacketKey(gatewayId, deviceKey, i);
                if (!redisService.hasKey(packetKey)) {
                    break; // 没有更多包了
                }
                redisService.deleteObject(packetKey);
            }

            // 2. 删除元数据
            String metadataKey = generateMetadataKey(gatewayId, deviceKey);
            redisService.deleteObject(metadataKey);

            log.debug("清理缓存数据完成 - 网关ID: {}, 设备Key: {}", gatewayId, deviceKey);

        } catch (Exception e) {
            log.error("清理缓存数据失败 - 网关ID: {}, 设备Key: {}", gatewayId, deviceKey, e);
        }
    }

    /**
     * 生成分包缓存key
     *
     * @param gatewayId 网关ID
     * @param deviceKey 设备Key
     * @param num       包序号
     * @return 缓存key
     */
    private String generatePacketKey(String gatewayId, String deviceKey, Integer num) {
        return cacheKeyPrefix + "packet:" + gatewayId + ":" + deviceKey + ":" + num;
    }

    /**
     * 生成元数据缓存key
     *
     * @param gatewayId 网关ID
     * @param deviceKey 设备Key
     * @return 缓存key
     */
    private String generateMetadataKey(String gatewayId, String deviceKey) {
        return cacheKeyPrefix + "metadata:" + gatewayId + ":" + deviceKey;
    }

    /**
     * 获取或创建元数据对象
     *
     * @param metadataKey 元数据key
     * @return 元数据对象
     */
    private JSONObject getOrCreateMetadata(String metadataKey) {
        JSONObject metadata = redisService.getCacheObject(metadataKey);
        if (metadata == null) {
            metadata = new JSONObject();
            metadata.set("receivedPackets", 0);
        }
        return metadata;
    }

    /**
     * 组装完整的二进制数据
     *
     * @param gatewayId 网关ID
     * @param deviceKey 设备Key
     * @param total     总包数
     * @return 完整的二进制数据
     */
    private byte[] assembleCompleteData(String gatewayId, String deviceKey, Integer total) {
        try {
            // 1. 计算总大小
            int totalSize = 0;
            for (int i = 1; i <= total; i++) {
                String packetKey = generatePacketKey(gatewayId, deviceKey, i);
                byte[] packetData = redisService.getCacheObject(packetKey);
                if (packetData == null) {
                    log.error("缺少数据包 - 网关ID: {}, 设备Key: {}, 包序号: {}", gatewayId, deviceKey, i);
                    return null;
                }
                totalSize += packetData.length;
            }

            // 2. 组装数据
            byte[] completeData = new byte[totalSize];
            int offset = 0;
            for (int i = 1; i <= total; i++) {
                String packetKey = generatePacketKey(gatewayId, deviceKey, i);
                byte[] packetData = redisService.getCacheObject(packetKey);
                System.arraycopy(packetData, 0, completeData, offset, packetData.length);
                offset += packetData.length;
            }

            return completeData;

        } catch (Exception e) {
            log.error("组装完整数据失败 - 网关ID: {}, 设备Key: {}", gatewayId, deviceKey, e);
            return null;
        }
    }

    /**
     * 生成文件名
     *
     * @param gatewayId 网关ID
     * @param deviceKey 设备Key
     * @param time      时间戳
     * @return 文件名
     */
    private String generateFileName(String gatewayId, String deviceKey, String time) {
        String timestamp = StrUtil.isNotBlank(time) ? time : String.valueOf(System.currentTimeMillis());
        String dateStr = DateUtil.format(DateUtil.date(Long.parseLong(timestamp)), "yyyyMMdd_HHmmss");
        String uuid = IdUtil.fastSimpleUUID().substring(0, 8);

        return String.format("%s_%s_%s_%s.%s",
                gatewayId, deviceKey, dateStr, uuid, fileExtension);
    }

    /**
     * 上传文件到文件服务
     *
     * @param data     文件数据
     * @param fileName 文件名
     * @return 文件URL
     */
    private String uploadToFileService(byte[] data, String fileName) {
        try {
            // 1. 使用 Hutool 工具类创建 MultipartFile 对象
            MultipartFile multipartFile = createMultipartFile(data, fileName);

            // 2. 调用文件服务上传
            R<SysFile> result = remoteFileService.upload(multipartFile);

            if (result != null && result.getCode() == 200 && result.getData() != null) {
                return result.getData().getUrl();
            } else {
                log.error("文件服务返回错误 - 文件名: {}, 错误信息: {}",
                        fileName, result != null ? result.getMsg() : "null");
                return null;
            }

        } catch (Exception e) {
            log.error("上传文件到文件服务失败 - 文件名: {}", fileName, e);
            return null;
        }
    }

    /**
     * 使用 Hutool 工具类创建 MultipartFile 对象
     *
     * @param data     文件数据
     * @param fileName 文件名
     * @return MultipartFile 对象
     */
    private MultipartFile createMultipartFile(byte[] data, String fileName) {
        return new MultipartFile() {
            @Override
            public String getName() {
                return "file";
            }

            @Override
            public String getOriginalFilename() {
                return fileName;
            }

            @Override
            public String getContentType() {
                // 使用 Hutool 工具类获取文件类型
                return StrUtil.isNotBlank(fileName) && fileName.contains(".")
                        ? "application/" + StrUtil.subAfter(fileName, ".", true)
                        : "application/octet-stream";
            }

            @Override
            public boolean isEmpty() {
                // 判断数据是否为空
                return data == null || data.length == 0;
            }

            @Override
            public long getSize() {
                // 获取数据大小
                return data != null ? data.length : 0;
            }

            @Override
            public byte[] getBytes() {
                return data;
            }

            @Override
            public InputStream getInputStream() {
                // 创建字节数组输入流
                return new ByteArrayInputStream(data);
            }

            @Override
            public void transferTo(java.io.File dest) throws IllegalStateException {
                // 使用 Hutool 工具类实现文件传输逻辑
                if (data != null && data.length > 0) {
                    FileUtil.writeBytes(data, dest);
                }
            }
        };
    }
}