package com.fx.broker.tcp.protocol.processor;

import cn.hutool.json.JSONObject;
import com.fx.broker.tcp.domain.ProtocolMessage;
import com.fx.broker.tcp.enums.FunctionCodeEnum;
import com.fx.broker.tcp.enums.ReturnCodeEnum;
import com.fx.broker.tcp.mq.publisher.RocketMQPublisher;
import com.fx.broker.tcp.service.CommandService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 功能码 6：上报指令结果处理器
 * 处理网关上报的摄像模块指令执行结果
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class CommandResultProcessor extends BaseCommandProcessor {

    @Resource
    private CommandService commandService;

    @Resource
    private RocketMQPublisher rocketMQPublisher;

    @Override
    public Integer getFunctionCode() {
        return FunctionCodeEnum.COMMAND_RESULT.getCode();
    }

    @Override
    public String getProcessorName() {
        return "上报指令结果处理器";
    }

    @Override
    protected ProtocolMessage doProcess(ProtocolMessage message) {
        JSONObject data = message.getData();
        if (data == null) {
            log.warn("指令结果消息缺少数据 - 连接ID: {}", message.getConnectionId());
            return createErrorResponse(message, ReturnCodeEnum.MISSING_PARAMETER);
        }

        // 验证必要字段
        if (hasMissingRequiredFields(data, "task_id", "result")) {
            log.warn("指令结果消息缺少必要参数 - 连接ID: {}", message.getConnectionId());
            return createErrorResponse(message, ReturnCodeEnum.MISSING_PARAMETER);
        }

        Long taskId = data.getLong("task_id");
        JSONObject result = data.getJSONObject("result");

        if (taskId == null || taskId <= 0) {
            log.warn("指令ID无效 - 连接ID: {}, 指令ID: {}", message.getConnectionId(), taskId);
            return createErrorResponse(message, ReturnCodeEnum.MISSING_PARAMETER);
        }

        if (result == null) {
            log.warn("指令结果为空 - 连接ID: {}, 指令ID: {}", message.getConnectionId(), taskId);
            return createErrorResponse(message, ReturnCodeEnum.MISSING_PARAMETER);
        }

        try {
            String gatewayId = getGatewayId(message);

            log.info("收到指令执行结果 - 网关ID: {}, 指令ID: {}, 连接ID: {}",
                    gatewayId, taskId, message.getConnectionId());

            // 处理指令执行结果
            boolean processed = processCommandResult(gatewayId, taskId, result);
            if (!processed) {
                log.warn("处理指令结果失败 - 网关ID: {}, 指令ID: {}", gatewayId, taskId);
                return createErrorResponse(message, ReturnCodeEnum.MEMORY_INSUFFICIENT);
            }

            logProcessing(message, "指令结果上报成功 - 网关ID: " + gatewayId + ", 指令ID: " + taskId);

            return createSuccessResponse(message);

        } catch (Exception e) {
            log.error("处理指令结果时发生异常 - 连接ID: {}, 指令ID: {}",
                    message.getConnectionId(), taskId, e);
            return createErrorResponse(message, ReturnCodeEnum.MEMORY_INSUFFICIENT);
        }
    }

    /**
     * 处理指令执行结果
     */
    private boolean processCommandResult(String gatewayId, Long taskId, JSONObject result) {
        try {
            // 验证指令是否存在
            // TODO: 实现指令存在性检查逻辑
            // if (!commandService.isCommandExists(gatewayId, taskId)) {
            // log.warn("指令不存在 - 网关ID: {}, 指令ID: {}", gatewayId, taskId);
            // return false;
            // }

            // 根据结果状态处理
            String status = result.getStr("status");
            boolean processed = false;

            switch (status) {
                case "success":
                    processed = processSuccessResult(gatewayId, taskId, result);
                    break;
                case "failed":
                    processed = processFailedResult(gatewayId, taskId, result);
                    break;
                case "timeout":
                    processed = processTimeoutResult(gatewayId, taskId, result);
                    break;
                default:
                    log.warn("未知的指令执行状态 - 网关ID: {}, 指令ID: {}, 状态: {}",
                            gatewayId, taskId, status);
                    return false;
            }

            if (processed) {
                // 保存指令执行结果
                // TODO: 实现指令结果保存逻辑
                // commandService.updateCommandResult(gatewayId, taskId, result);

                // 记录指令结果事件
                recordCommandResultEvent(gatewayId, taskId, result);

                // 发布指令结果到MQ
                publishCommandResultToMQ(gatewayId, taskId, result);
            }

            return processed;

        } catch (Exception e) {
            log.error("处理指令结果异常 - 网关ID: {}, 指令ID: {}", gatewayId, taskId, e);
            return false;
        }
    }

    /**
     * 处理成功执行结果
     */
    private boolean processSuccessResult(String gatewayId, Long taskId, JSONObject result) {
        try {
            log.info("指令执行成功 - 网关ID: {}, 指令ID: {}", gatewayId, taskId);

            // 处理成功结果数据
            JSONObject data = result.getJSONObject("data");
            if (data != null && !data.isEmpty()) {
                // 可能包含摄像头拍照结果、设备状态等
                log.debug("指令执行成功数据 - 网关ID: {}, 指令ID: {}, 数据: {}",
                        gatewayId, taskId, data);
            }

            return true;

        } catch (Exception e) {
            log.error("处理成功结果异常 - 网关ID: {}, 指令ID: {}", gatewayId, taskId, e);
            return false;
        }
    }

    /**
     * 处理失败执行结果
     */
    private boolean processFailedResult(String gatewayId, Long taskId, JSONObject result) {
        try {
            String errorMessage = result.getStr("message");
            String errorCode = result.getStr("error_code");

            log.warn("指令执行失败 - 网关ID: {}, 指令ID: {}, 错误码: {}, 错误信息: {}",
                    gatewayId, taskId, errorCode, errorMessage);

            return true;

        } catch (Exception e) {
            log.error("处理失败结果异常 - 网关ID: {}, 指令ID: {}", gatewayId, taskId, e);
            return false;
        }
    }

    /**
     * 处理超时执行结果
     */
    private boolean processTimeoutResult(String gatewayId, Long taskId, JSONObject result) {
        try {
            log.warn("指令执行超时 - 网关ID: {}, 指令ID: {}", gatewayId, taskId);

            return true;

        } catch (Exception e) {
            log.error("处理超时结果异常 - 网关ID: {}, 指令ID: {}", gatewayId, taskId, e);
            return false;
        }
    }

    /**
     * 记录指令结果事件
     */
    private void recordCommandResultEvent(String gatewayId, Long taskId, JSONObject result) {
        try {
            JSONObject resultEvent = new JSONObject();
            resultEvent.set("gateway_id", gatewayId);
            resultEvent.set("task_id", taskId);
            resultEvent.set("event_type", "COMMAND_RESULT");
            resultEvent.set("function_code", 6);
            resultEvent.set("result", result);
            resultEvent.set("event_time", System.currentTimeMillis() / 1000);
            resultEvent.set("description", "网关上报指令执行结果");

            // TODO: 保存到数据库或缓存
            log.info("记录指令结果事件 - 网关ID: {}, 指令ID: {}, 状态: {}",
                    gatewayId, taskId, result.getStr("status"));

        } catch (Exception e) {
            log.error("记录指令结果事件失败 - 网关ID: {}, 指令ID: {}", gatewayId, taskId, e);
        }
    }

    /**
     * 发布指令结果到MQ
     */
    private void publishCommandResultToMQ(String gatewayId, Long taskId, JSONObject result) {
        try {
            JSONObject mqData = new JSONObject();
            mqData.set("gateway_id", gatewayId);
            mqData.set("task_id", taskId);
            mqData.set("result", result);
            mqData.set("function_code", 6);
            mqData.set("timestamp", System.currentTimeMillis());

            // TODO: 修复MQ发布方法调用
            // rocketMQPublisher.publishAsyncResponse("COMMAND_RESULT", mqData,
            // "COMMAND_RESULT");

            log.debug("指令结果已发布到MQ - 网关ID: {}, 指令ID: {}", gatewayId, taskId);

        } catch (Exception e) {
            log.error("发布指令结果到MQ失败 - 网关ID: {}, 指令ID: {}", gatewayId, taskId, e);
        }
    }
}