package com.fx.broker.tcp.mq.subscriber;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fx.broker.tcp.domain.ProtocolMessage;
import com.fx.broker.tcp.manager.ConnectionManager;
import com.fx.broker.tcp.protocol.ProtocolHandler;
import com.fx.common.core.domain.ThingModelMessage;
import com.fx.common.core.utils.JsonUtil;
import com.fx.common.rocketmq.constant.ConsumerGroupConstant;
import com.fx.common.rocketmq.constant.ConsumerTopicConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * RocketMQ 订阅器
 * 负责订阅业务层返回的响应消息和下发指令
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class RocketMQSubscriber {

    /**
     * TCP 指令下发监听器
     * 监听业务层下发的 TCP 指令
     */
    @Component
    @RocketMQMessageListener(topic = ConsumerTopicConstant.FX_TCP_COMMAND, consumerGroup = ConsumerGroupConstant.FX_BROKER_GROUP)
    public static class TcpCommandListener implements RocketMQListener<ThingModelMessage> {

        // 常量定义
        private static final String FIELD_GATEWAY_ID = "gatewayId";
        private static final String FIELD_CODE = "code";
        private static final String FIELD_IS_UPLINK = "isUplink";
        private static final String FIELD_IS_RESPONSE = "isResponse";
        private static final String FIELD_PACK_ID = "packId";
        private static final String FIELD_DATA = "data";

        private static final int MIN_FUNCTION_CODE = 1;
        private static final int MAX_FUNCTION_CODE = 10;

        @Resource
        private ProtocolHandler protocolHandler;

        @Resource
        private ConnectionManager connectionManager;

        @Override
        public void onMessage(ThingModelMessage message) {
            // 1. 基础参数验证
            if (message == null) {
                return;
            }

            try {
                // 2. 提取并验证协议消息
                ProtocolMessage protocolMessage = extractAndValidateProtocolMessage(message);
                if (protocolMessage == null) {
                    return;
                }

                // 3. 发送消息给网关
                sendMessageToGateway(protocolMessage);

                log.info("[onMessage][TCP 指令处理成功 - 网关: {}, 功能码: {}]",
                        protocolMessage.getGatewayId(), protocolMessage.getCode());

            } catch (Exception e) {
                log.error("[onMessage][处理 TCP 指令失败 - 消息: {}]", JsonUtil.toJsonString(message), e);
            }
        }

        /**
         * 提取并验证协议消息
         * 
         * @param thingModelMessage ThingModelMessage 对象
         * @return ProtocolMessage 对象，验证失败时返回 null
         */
        private ProtocolMessage extractAndValidateProtocolMessage(ThingModelMessage thingModelMessage) {
            try {
                // 1. 解析 JSON 数据
                JSONObject dataObj = parseMessageData(thingModelMessage);
                if (dataObj == null) {
                    return null;
                }

                // 2. 提取并验证网关 ID
                String gatewayId = extractAndValidateGatewayId(dataObj);
                if (StrUtil.isBlank(gatewayId)) {
                    return null;
                }

                // 3. 提取并验证功能码
                Integer functionCode = extractAndValidateFunctionCode(dataObj);
                if (functionCode == null) {
                    return null;
                }

                // 4. 获取连接 ID
                String connectionId = getConnectionId(gatewayId);
                if (StrUtil.isBlank(connectionId)) {
                    return null;
                }

                // 5. 构建协议消息
                ProtocolMessage protocolMessage = buildProtocolMessage(dataObj, gatewayId, connectionId, functionCode);

                // 6. 最终验证
                if (!isValidProtocolMessage(protocolMessage)) {
                    log.warn("[extractAndValidateProtocolMessage][协议消息验证失败 - 网关: {}, 功能码: {}]", gatewayId,
                            functionCode);
                    return null;
                }

                log.debug("[extractAndValidateProtocolMessage][协议消息提取成功 - 网关: {}, 功能码: {}]", gatewayId, functionCode);
                return protocolMessage;

            } catch (Exception e) {
                log.error("[extractAndValidateProtocolMessage][提取协议消息失败]", e);
                return null;
            }
        }

        /**
         * 解析消息数据为 JSON 对象
         */
        private JSONObject parseMessageData(ThingModelMessage thingModelMessage) {
            try {
                Object data = thingModelMessage.getData();
                if (data == null) {
                    return null;
                }

                String dataJson = JsonUtil.toJsonString(data);
                if (StrUtil.isBlank(dataJson)) {
                    return null;
                }
                return JSONUtil.parseObj(dataJson);
            } catch (Exception e) {
                log.error("[parseMessageData][解析消息数据失败]", e);
                return null;
            }
        }

        /**
         * 提取并验证网关 ID
         */
        private String extractAndValidateGatewayId(JSONObject dataObj) {
            String gatewayId = dataObj.getStr(FIELD_GATEWAY_ID);
            if (StrUtil.isBlank(gatewayId)) {
                log.warn("[extractAndValidateGatewayId][网关 ID 为空]");
                return null;
            }
            return gatewayId;
        }

        /**
         * 提取并验证功能码
         */
        private Integer extractAndValidateFunctionCode(JSONObject dataObj) {
            Integer code = dataObj.getInt(FIELD_CODE);
            if (code == null || code < MIN_FUNCTION_CODE || code > MAX_FUNCTION_CODE) {
                log.warn("[extractAndValidateFunctionCode][功能码无效: {}, 有效范围: {}-{}]", code, MIN_FUNCTION_CODE,
                        MAX_FUNCTION_CODE);
                return null;
            }
            return code;
        }

        /**
         * 获取连接 ID
         */
        private String getConnectionId(String gatewayId) {
            ConnectionManager.ConnectionInfo connectionInfo = connectionManager.getConnectionByGatewayId(gatewayId);
            if (connectionInfo == null) {
                log.warn("[getConnectionId][网关不在线: {}]", gatewayId);
                return null;
            }
            return connectionInfo.getConnectionId();
        }

        /**
         * 构建协议消息对象
         */
        private ProtocolMessage buildProtocolMessage(JSONObject dataObj, String gatewayId,
                                                     String connectionId, Integer functionCode) {
            ProtocolMessage protocolMessage = new ProtocolMessage();

            // 设置基本字段
            protocolMessage.setIsUplink(dataObj.getBool(FIELD_IS_UPLINK, false));
            protocolMessage.setIsResponse(dataObj.getBool(FIELD_IS_RESPONSE, false));
            protocolMessage.setPackId(dataObj.getInt(FIELD_PACK_ID, 0));
            protocolMessage.setCode(functionCode);
            protocolMessage.setConnectionId(connectionId);
            protocolMessage.setGatewayId(gatewayId);

            // 设置数据内容（确保不为 null）
            JSONObject messageData = dataObj.getJSONObject(FIELD_DATA);
            protocolMessage.setData(messageData != null ? messageData : new JSONObject());

            // 设置时间戳
            protocolMessage.setSendTime(System.currentTimeMillis());

            return protocolMessage;
        }

        /**
         * 验证协议消息是否有效
         * 
         * @param protocolMessage 协议消息
         * @return 是否有效
         */
        private boolean isValidProtocolMessage(ProtocolMessage protocolMessage) {
            // 1. 基础对象验证
            if (protocolMessage == null) {
                return false;
            }

            // 2. 必要字段验证
            if (StrUtil.isBlank(protocolMessage.getConnectionId())) {
                log.warn("[isValidProtocolMessage][连接 ID 为空]");
                return false;
            }

            if (StrUtil.isBlank(protocolMessage.getGatewayId())) {
                log.warn("[isValidProtocolMessage][网关 ID 为空]");
                return false;
            }

            if (protocolMessage.getCode() == null ||
                    protocolMessage.getCode() < MIN_FUNCTION_CODE ||
                    protocolMessage.getCode() > MAX_FUNCTION_CODE) {
                log.warn("[isValidProtocolMessage][功能码无效: {}]", protocolMessage.getCode());
                return false;
            }

            // 3. 业务逻辑验证（应该是下行消息）
            if (protocolMessage.isUplink()) {
                log.warn("[isValidProtocolMessage][收到上行消息，期望下行消息 - 网关: {}]", protocolMessage.getGatewayId());
                return false;
            }

            return true;
        }

        /**
         * 发送消息给网关
         *
         * @param protocolMessage 协议消息
         */
        private void sendMessageToGateway(ProtocolMessage protocolMessage) {
            try {
                protocolHandler.sendResponse(protocolMessage.getConnectionId(), protocolMessage);
                log.debug("[sendMessageToGateway][消息发送完成 - 网关: {}, 功能码: {}]",
                        protocolMessage.getGatewayId(), protocolMessage.getCode());
            } catch (Exception e) {
                log.error("[sendMessageToGateway][发送消息给网关失败 - 网关: {}, 功能码: {}]",
                        protocolMessage.getGatewayId(), protocolMessage.getCode(), e);
                throw e; // 重新抛出异常，让上层处理
            }
        }
    }

}