package com.fx.broker.tcp.protocol.processor;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.fx.broker.tcp.domain.ProtocolMessage;
import com.fx.broker.tcp.enums.FunctionCodeEnum;
import com.fx.broker.tcp.enums.ReturnCodeEnum;
import com.fx.broker.tcp.manager.ConnectionManager;
import com.fx.broker.tcp.mq.publisher.RocketMQPublisher;
import com.fx.broker.tcp.service.GatewayManageService;
import com.fx.common.core.domain.R;
import com.fx.common.core.domain.ThingModelMessage;
import com.fx.link.api.RemoteDeviceService;
import com.fx.link.api.domain.device.model.DeviceInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 功能码 1：登录处理器
 * 处理网关登录认证请求
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class LoginProcessor extends BaseCommandProcessor {

    // 常量定义
    private static final String FIELD_GATEWAY_NUM = "gateway_num";
    private static final String FIELD_PASSWORD = "password";

    @Resource
    private RemoteDeviceService remoteDeviceService;

    @Resource
    private RocketMQPublisher rocketMQPublisher;

    @Resource
    private ConnectionManager connectionManager;

    @Resource
    private GatewayManageService gatewayManageService;

    @Override
    public Integer getFunctionCode() {
        return FunctionCodeEnum.LOGIN.getCode();
    }

    @Override
    public String getProcessorName() {
        return "登录处理器";
    }

    @Override
    protected ProtocolMessage doProcess(ProtocolMessage message) {
        try {
            // 1. 验证必要参数
            if (hasMissingRequiredFields(message.getData(), FIELD_GATEWAY_NUM, FIELD_PASSWORD)) {
                logProcessing(message, "[doProcess][登录消息缺少必要字段]");
                return createErrorResponse(message, ReturnCodeEnum.MISSING_PARAMETER);
            }

            // 2. 提取登录参数
            LoginParams loginParams = JSON.parseObject(message.getData().toString(), LoginParams.class);

            // 3. 执行认证
            DeviceInfo deviceInfo = performAuthentication(loginParams);
            if (ObjectUtil.isNull(deviceInfo)) {
                logProcessing(message, "[doProcess][网关认证失败]");
                return createErrorResponse(message, ReturnCodeEnum.PASSWORD_ERROR);
            }

            // 4. 创建会话并绑定连接
            createGatewaySession(loginParams, message);

            // 5. 更新网关信息
            updateGatewayInfo(loginParams, message, deviceInfo);

            // 6. 设置消息网关ID并记录成功日志
            message.setGatewayId(loginParams.getGatewayNum());
            logProcessing(message, String.format("[doProcess][网关登录成功 - 网关编号: %s]", loginParams.getGatewayNum()));

            return createSuccessResponse(message);

        } catch (Exception e) {
            logProcessing(message, "[doProcess][处理登录请求时发生异常]");
            log.error("[doProcess][处理登录请求时发生异常 - 连接ID: {}]", message.getConnectionId(), e);
            return createErrorResponse(message, ReturnCodeEnum.MEMORY_INSUFFICIENT);
        }
    }

    /**
     * 执行认证
     */
    private DeviceInfo performAuthentication(LoginParams loginParams) {
        try {
            // 1. 从link模块获取设备信息
            DeviceInfo deviceInfo = getDeviceInfo(loginParams.getGatewayNum());
            if (deviceInfo == null) {
                log.warn("[performAuthentication][认证失败：网关编号不存在 - 网关编号: {}]", loginParams.getGatewayNum());
                return null;
            }

            // 2. 验证密码
            if (isPasswordInvalid(loginParams.getPassword(), deviceInfo.getPassword())) {
                log.warn("[performAuthentication][认证失败：密码错误 - 网关编号: {}]", loginParams.getGatewayNum());
                return null;
            }

            // 3. 发送设备上线消息
            rocketMQPublisher.publishUplinkState(deviceInfo, null, ThingModelMessage.ID_TYPE_STATE_ONLINE);
            return deviceInfo;

        } catch (Exception e) {
            log.error("[performAuthentication][网关认证异常 - 网关编号: {}]", loginParams.getGatewayNum(), e);
            return null;
        }
    }

    /**
     * 从link模块获取设备信息
     *
     * @param gatewayNum 网关编号
     * @return 设备信息
     */
    private DeviceInfo getDeviceInfo(String gatewayNum) {
        try {
            R<DeviceInfo> result = remoteDeviceService.getDeviceInfoByKey(gatewayNum);
            if (result != null && result.getCode() == 200 && result.getData() != null) {
                return result.getData();
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 验证密码是否无效
     *
     * @param inputPassword  输入的密码
     * @param storedPassword 存储的密码
     * @return 是否无效
     */
    private boolean isPasswordInvalid(String inputPassword, String storedPassword) {
        if (StrUtil.isBlank(storedPassword)) {
            log.warn("[isPasswordInvalid][设备密码为空，认证失败]");
            return true;
        }

        return !StrUtil.equals(inputPassword, storedPassword);
    }

    /**
     * 创建网关会话并绑定连接
     */
    private void createGatewaySession(LoginParams loginParams, ProtocolMessage message) {
        try {
            // 1. 绑定网关连接
            connectionManager.bindGatewayConnection(message.getConnectionId(), loginParams.getGatewayNum());

            log.debug("[createGatewaySession][创建网关会话成功 - 网关编号: {}]", loginParams.getGatewayNum());

        } catch (Exception e) {
            log.error("[createGatewaySession][创建网关会话异常 - 网关编号: {}]", loginParams.getGatewayNum(), e);
        }
    }

    /**
     * 更新网关信息
     */
    private void updateGatewayInfo(LoginParams loginParams, ProtocolMessage message, DeviceInfo deviceInfo) {
        try {
            gatewayManageService.updateGatewayInfo(
                    loginParams.getGatewayNum(),
                    loginParams.getVersion(),
                    message.getConnectionId(),
                    deviceInfo);
        } catch (Exception e) {
            log.error("[updateGatewayInfo][更新网关信息失败 - 网关编号: {}]", loginParams.getGatewayNum(), e);
        }
    }

    /**
     * 登录参数内部类
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    private static class LoginParams {

        @JSONField(name = "gateway_num")
        private String gatewayNum;

        @JSONField(name = "password")
        private String password;

        @JSONField(name = "version")
        private String version;
    }
}