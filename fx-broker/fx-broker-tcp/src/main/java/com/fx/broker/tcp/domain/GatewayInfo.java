package com.fx.broker.tcp.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 网关信息实体类
 * 存储网关设备的详细信息和状态
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GatewayInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 网关ID（唯一标识）
     */
    private String gatewayId;

    /**
     * 网关名称
     */
    private String gatewayName;

    /**
     * 网关版本
     */
    private String version;

    /**
     * 当前连接 ID
     */
    private String connectionId;

    /**
     * 客户端 IP 地址
     */
    private String clientIp;

    /**
     * 连接时间
     */
    private LocalDateTime connectTime;

    /**
     * 断开时间
     */
    private LocalDateTime disconnectTime;

    /**
     * 最后心跳时间
     */
    private LocalDateTime lastHeartbeatTime;

    /**
     * 下线原因
     */
    private String offlineReason;

    /**
     * 是否在线
     */
    private boolean online;

    /**
     * 获取在线时长（分钟）
     * 
     * @return 在线时长
     */
    public long getOnlineDurationMinutes() {
        if (!online || connectTime == null) {
            return 0;
        }
        return java.time.Duration.between(connectTime, LocalDateTime.now()).toMinutes();
    }

}