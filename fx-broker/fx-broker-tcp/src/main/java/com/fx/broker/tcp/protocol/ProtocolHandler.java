package com.fx.broker.tcp.protocol;

import com.fx.broker.tcp.domain.ProtocolMessage;
import com.fx.broker.tcp.manager.ConnectionManager;
import io.vertx.core.net.NetSocket;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 协议处理器主类
 * 作为协议解析和处理的统一入口，负责消息的解析、路由和响应
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class ProtocolHandler {

    @Resource
    private MessageCodec messageCodec;

    @Resource
    private MessageRouter messageRouter;

    @Resource
    private ConnectionManager connectionManager;

    /**
     * 处理接收到的 TCP 消息
     * 
     * @param connectionId   连接ID
     * @param socket         TCP套接字
     * @param messageContent 消息内容
     */
    public void handleMessage(String connectionId, NetSocket socket, String messageContent) {
        try {
            // 1. 解码消息
            ProtocolMessage protocolMessage = messageCodec.decode(messageContent);
            if (protocolMessage == null) {
                log.warn("[handleMessage][消息解码失败 - 连接ID: {}]", connectionId);
                sendErrorResponse(socket, 4, "消息格式错误");
                return;
            }

            // 2. 设置消息上下文信息
            protocolMessage.setConnectionId(connectionId);
            protocolMessage.setSocket(socket);
            protocolMessage.setReceiveTime(System.currentTimeMillis());

            // 3. 根据连接ID获取网关ID并设置到消息中
            setGatewayIdFromConnection(protocolMessage);

            // 4. 验证消息基本格式
            if (!validateMessage(protocolMessage)) {
                log.warn("[handleMessage][消息验证失败 - 连接ID: {}, 功能码: {}]", connectionId, protocolMessage.getCode());
                sendErrorResponse(socket, 6, "消息参数不完整");
                return;
            }

            // 5. 路由消息到对应处理器
            messageRouter.routeMessage(protocolMessage);

        } catch (Exception e) {
            log.error("[handleMessage][处理协议消息时发生异常 - 连接ID: {}]", connectionId, e);
            sendErrorResponse(socket, 9, "内存不足，无法处理消息");
        }
    }

    /**
     * 发送响应消息
     * 
     * @param connectionId    连接ID
     * @param responseMessage 响应消息
     */
    public void sendResponse(String connectionId, ProtocolMessage responseMessage) {
        try {
            // 编码响应消息
            String responseContent = messageCodec.encode(responseMessage);
            if (responseContent == null) {
                log.error("[sendResponse][响应消息编码失败 - 连接ID: {}]", connectionId);
                return;
            }

            log.info("[sendResponse][发送 TCP 响应 - 连接ID: {}, 消息内容: {}]", connectionId, responseContent);

            // 发送响应
            boolean sent = connectionManager.sendMessage(connectionId, responseContent);
            if (!sent) {
                log.error("[sendResponse][响应消息发送失败 - 连接ID: {}]", connectionId);
            }

        } catch (Exception e) {
            log.error("[sendResponse][发送响应消息时发生异常 - 连接ID: {}]", connectionId, e);
        }
    }

    /**
     * 向指定网关发送下行消息
     * 
     * @param gatewayId 网关ID
     * @param message   下行消息
     */
    public boolean sendDownlinkMessage(String gatewayId, ProtocolMessage message) {
        try {
            // 设置为下行消息
            message.setIsUplink(false);
            message.setSendTime(System.currentTimeMillis());

            // 编码消息
            String messageContent = messageCodec.encode(message);
            if (messageContent == null) {
                log.error("[sendDownlinkMessage][下行消息编码失败 - 网关ID: {}]", gatewayId);
                return false;
            }

            log.info("[sendDownlinkMessage][发送TCP下行消息 - 网关ID: {}, 功能码: {}]", gatewayId, message.getCode());

            // 发送消息
            boolean sent = connectionManager.sendMessageToGateway(gatewayId, messageContent);
            if (!sent) {
                log.warn("[sendDownlinkMessage][下行消息发送失败，网关可能不在线 - 网关ID: {}]", gatewayId);
            }

            return sent;

        } catch (Exception e) {
            log.error("[sendDownlinkMessage][发送下行消息时发生异常 - 网关ID: {}]", gatewayId, e);
            return false;
        }
    }

    /**
     * 根据连接ID设置网关ID到消息中
     * 
     * @param message 协议消息
     */
    private void setGatewayIdFromConnection(ProtocolMessage message) {
        try {
            String connectionId = message.getConnectionId();
            if (connectionId != null) {
                // 从连接管理器获取连接信息
                ConnectionManager.ConnectionInfo connectionInfo = connectionManager.getConnection(connectionId);
                if (connectionInfo != null && connectionInfo.getGatewayId() != null) {
                    // 如果连接已绑定网关ID，则设置到消息中
                    message.setGatewayId(connectionInfo.getGatewayId());
                }
            }
        } catch (Exception e) {
            log.warn("[setGatewayIdFromConnection][设置网关ID失败 - 连接ID: {}]", message.getConnectionId(), e);
        }
    }

    /**
     * 验证消息基本格式
     */
    private boolean validateMessage(ProtocolMessage message) {
        if (message.getCode() == null || message.getCode() < 0 || message.getCode() > 11) {
            return false;
        }

        // 检查必要字段
        if (message.getIsUplink() == null || message.getIsResponse() == null) {
            return false;
        }

        // 响应消息必须有结果码
        return !message.isResponse() || message.getRetCode() != null;
    }

    /**
     * 发送错误响应
     */
    private void sendErrorResponse(NetSocket socket, int retCode, String errorMessage) {
        try {
            ProtocolMessage errorResponse = new ProtocolMessage();
            errorResponse.setIsUplink(false);
            errorResponse.setIsResponse(true);
            errorResponse.setPackId(0); // 错误响应使用默认包序号0
            errorResponse.setCode(0); // 错误响应使用功能码0
            errorResponse.setRetCode(retCode);

            String responseContent = messageCodec.encode(errorResponse);
            if (responseContent != null) {
                socket.write(responseContent);
            }
        } catch (Exception e) {
            log.error("[sendErrorResponse][发送错误响应时发生异常]", e);
        }
    }

}