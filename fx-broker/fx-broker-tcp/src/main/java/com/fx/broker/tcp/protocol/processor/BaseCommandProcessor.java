package com.fx.broker.tcp.protocol.processor;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.fx.broker.tcp.domain.ProtocolMessage;
import com.fx.broker.tcp.enums.FunctionCodeEnum;
import com.fx.broker.tcp.enums.ReturnCodeEnum;
import com.fx.broker.tcp.manager.ConnectionManager;
import com.fx.broker.tcp.protocol.MessageCodec;
import com.fx.broker.tcp.protocol.ProtocolHandler;
import com.fx.broker.tcp.service.GatewayManageService;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

/**
 * 功能码处理器抽象基类
 * 定义统一的处理接口和通用功能，所有具体处理器都继承此类
 * 
 * <AUTHOR>
 */
@Slf4j
public abstract class BaseCommandProcessor {

    @Resource
    protected MessageCodec messageCodec;

    @Resource
    protected ConnectionManager connectionManager;

    @Resource
    protected ProtocolHandler protocolHandler;

    @Resource
    protected GatewayManageService gatewayManageService;

    /**
     * 特殊返回值：表示无需响应（如上行响应消息）
     */
    protected static final ProtocolMessage NO_RESPONSE = new ProtocolMessage();

    /**
     * 获取处理器支持的功能码
     * 
     * @return 功能码
     */
    public abstract Integer getFunctionCode();

    /**
     * 获取处理器名称
     * 
     * @return 处理器名称
     */
    public abstract String getProcessorName();

    /**
     * 处理协议消息的核心方法
     * 
     * @param message 协议消息
     */
    public final void process(ProtocolMessage message) {
        try {
            // 1. 验证消息基本格式
            if (hasInvalidMessageFormat(message)) {
                log.warn("[process][消息验证失败 - 处理器: {}, 连接ID: {}]",
                        getProcessorName(), message.getConnectionId());
                sendErrorResponse(message, ReturnCodeEnum.MISSING_PARAMETER);
                return;
            }

            // 2. 验证是否登录，除登录消息外，其他消息需要验证是否登录
            if (isGatewayUnauthenticated(message.getGatewayId())
                    && !FunctionCodeEnum.LOGIN.getCode().equals(message.getCode())) {
                sendErrorResponse(message, ReturnCodeEnum.GATEWAY_NOT_LOGIN);
                return;
            }

            // 2. 执行具体的处理逻辑
            ProtocolMessage response = doProcess(message);

            if (response == NO_RESPONSE) {
                // 无需响应的情况（如上行响应消息）
                log.debug("[process][消息处理完成，无需响应 - 处理器: {}, 功能码: {}, 连接ID: {}]",
                        getProcessorName(), getFunctionCode(), message.getConnectionId());
            } else if (response != null) {
                // 3. 发送响应
                protocolHandler.sendResponse(message.getConnectionId(), response);

                log.debug("[process][消息处理成功 - 处理器: {}, 功能码: {}, 连接ID: {}, 结果码: {}]",
                        getProcessorName(), getFunctionCode(), message.getConnectionId(),
                        response.getRetCode());
            } else {
                // 处理失败
                sendErrorResponse(message, ReturnCodeEnum.MEMORY_INSUFFICIENT);

                log.warn("[process][消息处理失败 - 处理器: {}, 功能码: {}, 连接ID: {}]",
                        getProcessorName(), getFunctionCode(), message.getConnectionId());
            }

        } catch (Exception e) {
            log.error("[process][消息处理异常 - 处理器: {}, 功能码: {}, 连接ID: {}]",
                    getProcessorName(), getFunctionCode(), message.getConnectionId(), e);

            sendErrorResponse(message, ReturnCodeEnum.MEMORY_INSUFFICIENT);
        }
    }

    /**
     * 具体的处理逻辑，由子类实现
     * 
     * @param message 协议消息
     * @return 响应消息，处理失败返回null，无需响应返回NO_RESPONSE
     */
    protected abstract ProtocolMessage doProcess(ProtocolMessage message);

    /**
     * 检查消息格式是否无效
     * 
     * @param message 协议消息
     * @return 是否格式无效
     */
    private boolean hasInvalidMessageFormat(ProtocolMessage message) {
        // 1. 功能码验证
        if (message.getCode() == null) {
            log.warn("[hasInvalidMessageFormat][功能码为空 - 处理器: {}, 功能码: {}, 连接ID: {}]",
                    getProcessorName(), getFunctionCode(), message.getConnectionId());
            return true;
        }

        if (!message.getCode().equals(getFunctionCode())) {
            log.warn("[hasInvalidMessageFormat][功能码不匹配 - 期望: {}, 实际: {} - 处理器: {}, 功能码: {}, 连接ID: {}]",
                    getFunctionCode(), message.getCode(), getProcessorName(), getFunctionCode(),
                    message.getConnectionId());
            return true;
        }

        // 2. 消息方向标识验证
        if (message.getIsUplink() == null) {
            log.warn("[hasInvalidMessageFormat][上行标识为空 - 处理器: {}, 功能码: {}, 连接ID: {}]",
                    getProcessorName(), getFunctionCode(), message.getConnectionId());
            return true;
        }

        if (message.getIsResponse() == null) {
            log.warn("[hasInvalidMessageFormat][响应标识为空 - 处理器: {}, 功能码: {}, 连接ID: {}]",
                    getProcessorName(), getFunctionCode(), message.getConnectionId());
            return true;
        }

        // 3. 包ID验证
        if (message.getPackId() == null) {
            log.warn("[hasInvalidMessageFormat][包ID为空 - 处理器: {}, 功能码: {}, 连接ID: {}]",
                    getProcessorName(), getFunctionCode(), message.getConnectionId());
            return true;
        }

        log.debug("[hasInvalidMessageFormat][消息验证通过 - 处理器: {}, 功能码: {}, 连接ID: {}, 网关ID: {}]",
                getProcessorName(), getFunctionCode(), message.getConnectionId(), getGatewayId(message));
        return false;
    }

    /**
     * 检查网关是否未认证
     *
     * @param gatewayId 网关ID
     * @return 是否未认证
     */
    private boolean isGatewayUnauthenticated(String gatewayId) {
        if (StrUtil.isBlank(gatewayId)) {
            return true;
        }
        return gatewayManageService.getGatewayInfo(gatewayId) == null;
    }

    /**
     * 检查是否缺少必要的数据字段
     *
     * @param data           数据对象
     * @param requiredFields 必要字段列表
     * @return 是否缺少必要字段
     */
    protected boolean hasMissingRequiredFields(JSONObject data, String... requiredFields) {
        if (data == null || requiredFields == null) {
            return true;
        }

        for (String field : requiredFields) {
            if (!data.containsKey(field) || data.get(field) == null) {
                log.debug("[hasMissingRequiredFields][缺少必要字段 - 处理器: {}, 功能码: {}, 字段: {}]",
                        getProcessorName(), getFunctionCode(), field);
                return true;
            }
        }

        return false;
    }

    /**
     * 创建成功响应
     * 
     * @param originalMessage 原始消息
     * @return 成功响应
     */
    protected ProtocolMessage createSuccessResponse(ProtocolMessage originalMessage) {
        return createResponse(originalMessage, 0, null);
    }

    /**
     * 创建成功响应（带数据）
     * 
     * @param originalMessage 原始消息
     * @param responseData    响应数据
     * @return 成功响应
     */
    protected ProtocolMessage createSuccessResponse(ProtocolMessage originalMessage, JSONObject responseData) {
        return createResponse(originalMessage, 0, responseData);
    }

    /**
     * 创建错误响应
     * 
     * @param originalMessage 原始消息
     * @param returnCode      返回码枚举
     * @return 错误响应
     */
    protected ProtocolMessage createErrorResponse(ProtocolMessage originalMessage, ReturnCodeEnum returnCode) {
        return createResponse(originalMessage, returnCode.getCode(), null);
    }

    /**
     * 创建响应消息
     * 
     * @param originalMessage 原始消息
     * @param retCode         返回码
     * @param responseData    响应数据
     * @return 响应消息
     */
    private ProtocolMessage createResponse(ProtocolMessage originalMessage, Integer retCode,
            JSONObject responseData) {
        ProtocolMessage response = new ProtocolMessage();
        response.setIsUplink(false);
        response.setIsResponse(true);
        response.setPackId(originalMessage.getPackId());
        response.setCode(originalMessage.getCode());
        response.setRetCode(retCode);
        response.setConnectionId(originalMessage.getConnectionId());
        response.setSocket(originalMessage.getSocket());
        response.setGatewayId(originalMessage.getGatewayId());

        if (responseData != null) {
            response.setData(responseData);
        } else {
            response.setData(new JSONObject());
        }

        return response;
    }

    /**
     * 发送错误响应
     * 
     * @param originalMessage 原始消息
     * @param returnCode      返回码枚举
     */
    private void sendErrorResponse(ProtocolMessage originalMessage, ReturnCodeEnum returnCode) {
        try {
            ProtocolMessage errorResponse = createErrorResponse(originalMessage, returnCode);
            protocolHandler.sendResponse(originalMessage.getConnectionId(), errorResponse);
        } catch (Exception e) {
            log.error("[sendErrorResponse][发送错误响应失败 - 处理器: {}, 连接ID: {}]",
                    getProcessorName(), originalMessage.getConnectionId(), e);
        }
    }

    /**
     * 获取网关ID（从连接管理器或消息中获取）
     * 
     * @param message 消息
     * @return 网关ID
     */
    protected String getGatewayId(ProtocolMessage message) {
        // 优先从消息中获取
        if (StrUtil.isNotBlank(message.getGatewayId())) {
            return message.getGatewayId();
        }
        return "";
    }

    /**
     * 记录处理日志
     * 
     * @param message 消息
     * @param action  操作描述
     */
    protected void logProcessing(ProtocolMessage message, String action) {
        log.info("[logProcessing][处理器操作 - 处理器: {}, 功能码: {}, 连接ID: {}, 网关ID: {}, 操作: {}]",
                getProcessorName(), getFunctionCode(), message.getConnectionId(),
                getGatewayId(message), action);
    }

}