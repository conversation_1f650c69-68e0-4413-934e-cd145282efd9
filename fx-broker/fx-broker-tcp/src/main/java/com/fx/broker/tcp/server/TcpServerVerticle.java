package com.fx.broker.tcp.server;

import com.fx.broker.tcp.config.TcpServerConfig;
import com.fx.broker.tcp.manager.ConnectionManager;
import com.fx.broker.tcp.protocol.ProtocolHandler;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.net.NetServer;
import io.vertx.core.net.NetServerOptions;
import io.vertx.core.net.NetSocket;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * TCP 服务器 Verticle
 * 基于 Vert.x 事件循环处理 TCP 连接和消息
 * 
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
public class TcpServerVerticle extends AbstractVerticle {

    /**
     * 服务器配置
     */
    private final TcpServerConfig serverConfig;
    /**
     * 连接管理器
     */
    private final ConnectionManager connectionManager;
    /**
     * 协议处理器
     */
    private final ProtocolHandler protocolHandler;

    /**
     * NetServer
     */
    private NetServer netServer;

    @Override
    public void start(Promise<Void> startPromise) {
        log.info("[start][启动 TCP 服务器 - 主机: {}, 端口: {}]", serverConfig.getHost(), serverConfig.getPort());

        // 创建NetServer配置选项
        NetServerOptions options = new NetServerOptions()
                .setTcpNoDelay(true)
                .setTcpKeepAlive(true)
                .setReceiveBufferSize(serverConfig.getBufferSize())
                .setSendBufferSize(serverConfig.getBufferSize());

        // 创建NetServer
        netServer = vertx.createNetServer(options);

        // 设置连接处理器
        netServer.connectHandler(this::handleConnection);

        // 启动监听
        netServer.listen(serverConfig.getPort(), serverConfig.getHost(), result -> {
            if (result.succeeded()) {
                log.info("[start][TCP 服务器监听成功 - 主机: {}, 端口: {}]", serverConfig.getHost(), serverConfig.getPort());
                startPromise.complete();
            } else {
                log.error("[start][TCP 服务器监听失败 - 主机: {}, 端口: {}]", serverConfig.getHost(), serverConfig.getPort(),
                        result.cause());
                startPromise.fail(result.cause());
            }
        });
    }

    @Override
    public void stop(Promise<Void> stopPromise) {
        log.info("[stop][停止 TCP 服务器]");

        if (netServer != null) {
            netServer.close(result -> {
                if (result.succeeded()) {
                    log.info("[stop][TCP 服务器关闭成功]");
                    stopPromise.complete();
                } else {
                    log.error("[stop][TCP 服务器关闭失败]", result.cause());
                    stopPromise.fail(result.cause());
                }
            });
        } else {
            stopPromise.complete();
        }
    }

    /**
     * 处理新的TCP连接
     */
    private void handleConnection(NetSocket socket) {
        String remoteAddress = socket.remoteAddress().toString();
        log.debug("[handleConnection][收到新的 TCP 连接 - 远程地址: {}]", remoteAddress);

        try {
            // 检查连接数限制
            if (connectionManager.getConnectionCount() >= serverConfig.getMaxConnections()) {
                log.warn("[handleConnection][连接数已达上限 {}, 拒绝新连接: {}]", serverConfig.getMaxConnections(),
                        remoteAddress);
                socket.close();
                return;
            }

            // 注册连接
            String connectionId = connectionManager.addConnection(socket);

            // 设置连接关闭处理器
            socket.closeHandler(v -> {
                log.debug("[handleConnection][TCP 连接关闭 - 连接 ID: {}, 远程地址: {}]", connectionId, remoteAddress);
                connectionManager.removeConnection(connectionId);
            });

            // 设置异常处理器
            socket.exceptionHandler(throwable -> {
                log.error("[handleConnection][TCP 连接异常 - 连接 ID: {}, 远程地址: {}]", connectionId, remoteAddress,
                        throwable);
                connectionManager.removeConnection(connectionId);
                socket.close();
            });

            // 设置数据处理器
            socket.handler(buffer -> handleMessage(connectionId, socket, buffer));

            log.debug("[handleConnection][TCP 连接注册完成 - 连接 ID: {}, 远程地址: {}, 当前连接数: {}]",
                    connectionId, remoteAddress, connectionManager.getConnectionCount());

        } catch (Exception e) {
            log.error("[handleConnection][处理 TCP 连接时发生异常 - 远程地址: {}]", remoteAddress, e);
            socket.close();
        }
    }

    /**
     * 处理接收到的消息
     */
    private void handleMessage(String connectionId, NetSocket socket, Buffer buffer) {
        try {
            String message = buffer.toString("UTF-8");
            log.info("[handleMessage][收到 TCP 消息 - 连接 ID: {}, 消息内容: {}]", connectionId, message);

            // 更新连接活跃时间
            connectionManager.updateLastActiveTime(connectionId);

            // 处理消息 - 这里可以集成协议处理器
            processMessage(connectionId, socket, message);

        } catch (Exception e) {
            log.error("[handleMessage][处理 TCP 消息时发生异常 - 连接ID: {}]", connectionId, e);

            // 发送错误响应
            sendErrorResponse(socket, "消息处理异常");
        }
    }

    /**
     * 处理具体的协议消息
     */
    private void processMessage(String connectionId, NetSocket socket, String message) {
        try {
            // 使用 ProtocolHandler 处理消息
            if (protocolHandler != null) {
                protocolHandler.handleMessage(connectionId, socket, message);
            } else {
                log.warn("[processMessage][ProtocolHandler 未初始化，发送简单确认响应]");
                // 临时回复确认
                String response = "{\"status\":\"received\",\"connectionId\":\"" + connectionId + "\"}";
                sendResponse(socket, response);
            }
        } catch (Exception e) {
            log.error("[processMessage][协议消息处理异常 - 连接 ID: {}]", connectionId, e);
            sendErrorResponse(socket, "协议消息处理异常");
        }
    }

    /**
     * 发送响应消息
     */
    private void sendResponse(NetSocket socket, String response) {
        try {
            Buffer buffer = Buffer.buffer(response, "UTF-8");
            socket.write(buffer);
            log.debug("[sendResponse][发送响应消息 - 长度: {}]", response.length());
        } catch (Exception e) {
            log.error("[sendResponse][发送响应消息失败]", e);
        }
    }

    /**
     * 发送错误响应
     */
    private void sendErrorResponse(NetSocket socket, String errorMessage) {
        String errorResponse = "{\"status\":\"error\",\"message\":\"" + errorMessage + "\"}";
        sendResponse(socket, errorResponse);
    }
}