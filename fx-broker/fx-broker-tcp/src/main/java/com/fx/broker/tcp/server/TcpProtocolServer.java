package com.fx.broker.tcp.server;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.fx.broker.tcp.config.TcpServerConfig;
import com.fx.broker.tcp.manager.ConnectionManager;
import com.fx.broker.tcp.protocol.ProtocolHandler;
import io.vertx.core.Vertx;
import io.vertx.core.VertxOptions;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;

/**
 * TCP 协议服务器主类
 * 负责启动和管理基于 Vert.x 的 TCP 服务器
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
public class TcpProtocolServer {

    @Resource
    private TcpServerConfig tcpServerConfig;

    @Resource
    private ConnectionManager connectionManager;

    @Resource
    private ProtocolHandler protocolHandler;

    private Vertx vertx;
    private String deploymentId;
    private volatile boolean isRunning = false;
    private volatile boolean isShuttingDown = false;

    /**
     * 启动 TCP 服务器
     */
    @PostConstruct
    public void start() {
        try {
            // 1.1 检查服务器状态
            if (isRunning) {
                log.warn("[start][TCP 协议服务器已经在运行中]");
                return;
            }

            // 1.2 验证配置参数
            validateConfiguration();

            // 1.3 初始化 Vert.x 实例
            initializeVertx();

            // 1.4 部署 TCP 服务器 Verticle
            deployTcpServer();

            // 1.5 启动心跳检测
            startHeartbeatDetection();

            isRunning = true;
            log.info("[start][TCP 协议服务器组件初始化完成]");

        } catch (Exception e) {
            log.error("[start][TCP 协议服务器启动异常]", e);
            cleanup();
            throw new RuntimeException("TCP 服务器启动失败", e);
        }
    }

    /**
     * 停止 TCP 服务器
     */
    @PreDestroy
    public void stop() {
        if (isShuttingDown) {
            log.warn("[stop][TCP 协议服务器正在关闭中，请勿重复调用]");
            return;
        }

        isShuttingDown = true;
        isRunning = false;

        try {
            // 2.1 停止心跳检测
            stopHeartbeatDetection();

            // 2.2 关闭所有连接
            closeAllConnections();

            // 2.3 取消部署 Verticle
            undeployVerticle();

            // 2.4 关闭 Vert.x 实例
            closeVertx();

            log.info("[stop][TCP 协议服务器停止完成]");

        } catch (Exception e) {
            log.error("[stop][TCP 协议服务器停止异常]", e);
        } finally {
            cleanup();
        }
    }

    /**
     * 验证配置参数
     */
    private void validateConfiguration() {
        if (ObjectUtil.isNull(tcpServerConfig)) {
            throw new IllegalArgumentException("[validateConfiguration][TCP 服务器配置不能为空]");
        }

        // 1. 验证端口号（这是唯一必须验证的配置）
        if (tcpServerConfig.getPort() < 1 || tcpServerConfig.getPort() > 65535) {
            throw new IllegalArgumentException(
                    "[validateConfiguration][端口号无效: " + tcpServerConfig.getPort() + "，有效范围: 1-65535]");
        }

        // 2. 验证最大连接数（有默认值，但需要合理范围）
        if (tcpServerConfig.getMaxConnections() < 1) {
            log.warn("[validateConfiguration][最大连接数无效: {}，已使用默认值: {}]",
                    tcpServerConfig.getMaxConnections(), 1000);
            tcpServerConfig.setMaxConnections(1000);
        }

        // 3. 确保Vert.x配置不为空（有默认值，但需要初始化）
        if (tcpServerConfig.getVertx() == null) {
            tcpServerConfig.setVertx(new TcpServerConfig.VertxConfig());
        }

        log.info("[validateConfiguration][配置验证通过 - 端口: {}, 最大连接数: {}, 超时: {}ms, 缓冲区: {}字节]",
                tcpServerConfig.getPort(), tcpServerConfig.getMaxConnections(),
                tcpServerConfig.getTimeout(), tcpServerConfig.getBufferSize());
    }

    /**
     * 初始化 Vert.x 实例
     */
    private void initializeVertx() {
        // 8.1 配置 Vert.x 选项
        VertxOptions options = new VertxOptions()
                .setWorkerPoolSize(tcpServerConfig.getVertx().getWorkerPoolSize())
                .setEventLoopPoolSize(tcpServerConfig.getVertx().getEventLoopSize());

        // 8.2 创建 Vert.x 实例
        vertx = Vertx.vertx(options);
        log.info("[initializeVertx][Vert.x 实例创建成功]");
    }

    /**
     * 部署 TCP 服务器 Verticle
     */
    private void deployTcpServer() {
        TcpServerVerticle verticle = new TcpServerVerticle(
                tcpServerConfig, connectionManager, protocolHandler);

        vertx.deployVerticle(verticle, result -> {
            if (result.succeeded()) {
                deploymentId = result.result();
                log.info("[deployTcpServer][TCP 协议服务器启动成功 - Host: {}, Port: {}, 最大连接数: {}]",
                        tcpServerConfig.getHost(), tcpServerConfig.getPort(), tcpServerConfig.getMaxConnections());
            } else {
                log.error("[deployTcpServer][TCP 协议服务器启动失败]", result.cause());
                throw new RuntimeException("TCP 服务器启动失败", result.cause());
            }
        });
    }

    /**
     * 启动心跳检测
     */
    private void startHeartbeatDetection() {
        if (ObjectUtil.isNotNull(connectionManager)) {
            connectionManager.startHeartbeat(vertx);
            log.info("[startHeartbeatDetection][心跳检测启动成功]");
        } else {
            log.warn("[startHeartbeatDetection][连接管理器为空，跳过心跳检测启动]");
        }
    }

    /**
     * 停止心跳检测
     */
    private void stopHeartbeatDetection() {
        if (ObjectUtil.isNotNull(connectionManager)) {
            connectionManager.stopHeartbeat();
            log.info("[stopHeartbeatDetection][心跳检测停止成功]");
        }
    }

    /**
     * 关闭所有连接
     */
    private void closeAllConnections() {
        if (ObjectUtil.isNotNull(connectionManager)) {
            connectionManager.closeAllConnections();
            log.info("[closeAllConnections][所有连接已关闭]");
        }
    }

    /**
     * 取消部署 Verticle
     */
    private void undeployVerticle() {
        if (ObjectUtil.isNotNull(vertx) && StrUtil.isNotBlank(deploymentId)) {
            vertx.undeploy(deploymentId, result -> {
                if (result.succeeded()) {
                    log.info("[undeployVerticle][TCP 服务器 Verticle 取消部署成功]");
                } else {
                    log.error("[undeployVerticle][TCP 服务器 Verticle 取消部署失败]", result.cause());
                }
            });
        }
    }

    /**
     * 关闭 Vert.x 实例
     */
    private void closeVertx() {
        if (ObjectUtil.isNotNull(vertx)) {
            vertx.close(result -> {
                if (result.succeeded()) {
                    log.info("[closeVertx][Vert.x 实例关闭成功]");
                } else {
                    log.error("[closeVertx][Vert.x 实例关闭失败]", result.cause());
                }
            });
        }
    }

    /**
     * 清理资源
     */
    private void cleanup() {
        vertx = null;
        deploymentId = null;
        isRunning = false;
        isShuttingDown = false;
    }
}