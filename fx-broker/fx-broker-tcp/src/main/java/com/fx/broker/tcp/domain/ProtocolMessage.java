package com.fx.broker.tcp.domain;

import cn.hutool.json.JSONObject;
import io.vertx.core.net.NetSocket;
import lombok.Data;

/**
 * 协议消息实体类
 * 对应协议文档中的JSON消息格式
 * 
 * <AUTHOR>
 */
@Data
public class ProtocolMessage {

    /**
     * 数据方向：true上行，false下行
     */
    private Boolean isUplink;

    /**
     * 发送/回复：true回复，false发送
     */
    private Boolean isResponse;

    /**
     * 包序号：每个发送和回复的ID必须对应，每次交互后下一条＋1，区分每次交互过程
     */
    private Integer packId;

    /**
     * 功能码：1-10
     */
    private Integer code;

    /**
     * 数据内容
     */
    private JSONObject data;

    /**
     * 处理结果码（仅回复报文需要）
     */
    private Integer retCode;

    // 扩展字段，用于内部处理
    private String connectionId;
    private NetSocket socket;
    private String gatewayId;
    private long receiveTime;
    private long sendTime;
    private String rawMessage;

    // 构造函数
    public ProtocolMessage() {
        this.data = new JSONObject();
    }

    public ProtocolMessage(Boolean isUplink, Boolean isResponse, Integer code) {
        this.isUplink = isUplink;
        this.isResponse = isResponse;
        this.code = code;
        this.data = new JSONObject();
    }

    public ProtocolMessage(Boolean isUplink, Boolean isResponse, Integer packId, Integer code) {
        this.isUplink = isUplink;
        this.isResponse = isResponse;
        this.packId = packId;
        this.code = code;
        this.data = new JSONObject();
    }

    public boolean isUplink() {
        return Boolean.TRUE.equals(isUplink);
    }

    public boolean isDownlink() {
        return !isUplink();
    }

    public boolean isResponse() {
        return Boolean.TRUE.equals(isResponse);
    }

    public Object getData(String key) {
        return data != null ? data.get(key) : null;
    }

    public Integer getDataInt(String key) {
        return data != null ? data.getInt(key) : null;
    }

    /**
     * 创建响应消息
     */
    public ProtocolMessage createResponse(Integer retCode) {
        ProtocolMessage response = new ProtocolMessage();
        response.setIsUplink(false);
        response.setIsResponse(true);
        response.setPackId(this.packId);
        response.setCode(this.code);
        response.setRetCode(retCode);
        response.setConnectionId(this.connectionId);
        response.setSocket(this.socket);
        response.setGatewayId(this.gatewayId);
        return response;
    }

    /**
     * 创建成功响应
     */
    public ProtocolMessage createSuccessResponse() {
        return createResponse(0);
    }

    /**
     * 创建错误响应
     */
    public ProtocolMessage createErrorResponse(Integer errorCode) {
        return createResponse(errorCode);
    }
}