package com.fx.broker.tcp.protocol;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fx.broker.tcp.domain.ProtocolMessage;
import com.fx.broker.tcp.enums.FunctionCodeEnum;
import com.fx.broker.tcp.enums.ReturnCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 消息编解码器
 * 负责协议消息与JSON字符串之间的编解码转换
 * 包含完整的消息验证、统计信息和错误处理
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class MessageCodec {

    /**
     * 协议字段常量
     */
    private static final String FIELD_IS_UPLINK = "is_uplink";
    private static final String FIELD_IS_RESPONSE = "is_response";
    private static final String FIELD_PACK_ID = "pack_id";
    private static final String FIELD_CODE = "code";
    private static final String FIELD_DATA = "data";
    private static final String FIELD_RET_CODE = "ret_code";

    /**
     * 解码JSON字符串为协议消息
     * 
     * @param jsonContent JSON字符串
     * @return 协议消息，解码失败返回null
     */
    public ProtocolMessage decode(String jsonContent) {
        try {
            // 基本验证
            if (StrUtil.isBlank(jsonContent)) {
                log.warn("[decode][解码失败：消息内容为空]");
                return null;
            }

            // 解析JSON
            JSONObject jsonObject;
            try {
                jsonObject = JSONUtil.parseObj(jsonContent);
            } catch (Exception e) {
                log.warn("[decode][解码失败：JSON格式错误 - {}]", e.getMessage());
                return null;
            }

            // 验证必要字段
            if (hasInvalidRequiredFields(jsonObject)) {
                log.warn("[decode][解码失败：缺少必要字段]");
                return null;
            }

            // 创建协议消息对象
            ProtocolMessage message = new ProtocolMessage();

            // 设置基本字段
            message.setIsUplink(jsonObject.getBool(FIELD_IS_UPLINK));
            message.setIsResponse(jsonObject.getBool(FIELD_IS_RESPONSE));
            message.setPackId(jsonObject.getInt(FIELD_PACK_ID));
            message.setCode(jsonObject.getInt(FIELD_CODE));

            // 设置数据字段
            Object dataValue = jsonObject.get(FIELD_DATA);
            if (dataValue instanceof JSONObject) {
                message.setData((JSONObject) dataValue);
            } else if (dataValue != null) {
                // 如果data不是JSONObject，尝试转换
                try {
                    JSONObject dataObject = JSONUtil.parseObj(dataValue.toString());
                    message.setData(dataObject);
                } catch (Exception e) {
                    // 转换失败，创建空的JSONObject
                    message.setData(new JSONObject());
                    log.warn("[decode][data字段转换失败，使用空对象 - 原值: {}]", dataValue);
                }
            } else {
                message.setData(new JSONObject());
            }

            // 设置返回码（仅响应消息需要）
            if (jsonObject.containsKey(FIELD_RET_CODE)) {
                message.setRetCode(jsonObject.getInt(FIELD_RET_CODE));
            }

            // 保存原始消息
            message.setRawMessage(jsonContent);

            // 验证消息完整性
            if (isInvalidMessage(message)) {
                log.warn("[decode][解码失败：消息验证不通过]");
                return null;
            }

            return message;

        } catch (Exception e) {
            log.error("[decode][解码消息时发生异常]", e);
            return null;
        }
    }

    /**
     * 编码协议消息为JSON字符串
     * 
     * @param message 协议消息
     * @return JSON字符串，编码失败返回null
     */
    public String encode(ProtocolMessage message) {

        try {
            // 基本验证
            if (message == null) {
                log.warn("[encode][编码失败：消息对象为空]");
                return null;
            }

            // 验证消息完整性
            if (isInvalidMessage(message)) {
                log.warn("[encode][编码失败：消息验证不通过]");
                return null;
            }

            // 创建JSON对象
            JSONObject jsonObject = new JSONObject();

            // 设置基本字段
            jsonObject.set(FIELD_IS_UPLINK, message.getIsUplink());
            jsonObject.set(FIELD_IS_RESPONSE, message.getIsResponse());
            jsonObject.set(FIELD_PACK_ID, message.getPackId());
            jsonObject.set(FIELD_CODE, message.getCode());

            // 设置数据字段
            JSONObject data = message.getData();
            if (data != null && !data.isEmpty()) {
                jsonObject.set(FIELD_DATA, data);
            } else {
                // 即使数据为空，也要设置一个空对象
                jsonObject.set(FIELD_DATA, new JSONObject());
            }

            // 设置返回码（仅响应消息需要）
            if (message.isResponse() && message.getRetCode() != null) {
                jsonObject.set(FIELD_RET_CODE, message.getRetCode());
            }

            return jsonObject.toString();

        } catch (Exception e) {
            log.error("[encode][编码消息时发生异常]", e);
            return null;
        }
    }

    /**
     * 检查JSON对象是否缺少必要字段
     * 
     * @param jsonObject JSON对象
     * @return true表示缺少必要字段，false表示字段完整
     */
    private boolean hasInvalidRequiredFields(JSONObject jsonObject) {
        if (jsonObject == null) {
            return true;
        }

        // 检查必要字段是否存在
        if (!jsonObject.containsKey(FIELD_IS_UPLINK)) {
            log.debug("[hasInvalidRequiredFields][缺少字段: {}]", FIELD_IS_UPLINK);
            return true;
        }

        if (!jsonObject.containsKey(FIELD_IS_RESPONSE)) {
            log.debug("[hasInvalidRequiredFields][缺少字段: {}]", FIELD_IS_RESPONSE);
            return true;
        }

        if (!jsonObject.containsKey(FIELD_PACK_ID)) {
            log.debug("[hasInvalidRequiredFields][缺少字段: {}]", FIELD_PACK_ID);
            return true;
        }

        if (!jsonObject.containsKey(FIELD_CODE)) {
            log.debug("[hasInvalidRequiredFields][缺少字段: {}]", FIELD_CODE);
            return true;
        }

        // 验证字段类型
        try {
            Boolean isUplink = jsonObject.getBool(FIELD_IS_UPLINK);
            Boolean isResponse = jsonObject.getBool(FIELD_IS_RESPONSE);
            Integer packId = jsonObject.getInt(FIELD_PACK_ID);
            Integer code = jsonObject.getInt(FIELD_CODE);

            if (isUplink == null || isResponse == null || packId == null || code == null) {
                log.debug("[hasInvalidRequiredFields][字段类型不正确]");
                return true;
            }

            // 验证功能码范围
            if (FunctionCodeEnum.isInvalidCode(code)) {
                log.debug("[hasInvalidRequiredFields][功能码超出范围: {}]", code);
                return true;
            }

        } catch (Exception e) {
            log.debug("[hasInvalidRequiredFields][字段类型验证失败: {}]", e.getMessage());
            return true;
        }

        return false;
    }

    /**
     * 检查协议消息是否无效
     * 
     * @param message 协议消息
     * @return true表示消息无效，false表示消息有效
     */
    private boolean isInvalidMessage(ProtocolMessage message) {
        if (message == null) {
            return true;
        }

        // 基本字段验证
        if (message.getIsUplink() == null || message.getIsResponse() == null ||
                message.getPackId() == null || message.getCode() == null) {
            log.debug("[isInvalidMessage][基本字段为空]");
            return true;
        }

        // 功能码范围验证
        Integer code = message.getCode();
        if (FunctionCodeEnum.isInvalidCode(code)) {
            log.debug("[isInvalidMessage][功能码超出范围: {}]", code);
            return true;
        }

        // 响应消息必须有返回码
        if (message.isResponse() && message.getRetCode() == null) {
            log.debug("[isInvalidMessage][响应消息缺少返回码]");
            return true;
        }

        // 返回码范围验证
        if (message.getRetCode() != null) {
            Integer retCode = message.getRetCode();
            if (ReturnCodeEnum.getByCode(retCode) == null) {
                log.debug("[isInvalidMessage][返回码超出范围: {}]", retCode);
                return true;
            }
        }

        // 数据字段验证
        if (message.getData() == null) {
            message.setData(new JSONObject());
        }

        return false;
    }

}