package com.fx.broker.tcp.domain;

import cn.hutool.json.JSONObject;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 指令任务实体
 * 包含指令的完整生命周期管理信息
 * 
 * <AUTHOR>
 */
@Data
public class CommandTask implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 网关ID
     */
    private String gatewayId;

    /**
     * 指令类型
     */
    private String commandType;

    /**
     * 指令内容
     */
    private JSONObject content;

    /**
     * 指令状态
     */
    private CommandStatus status;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 发送时间
     */
    private LocalDateTime sentTime;

    /**
     * 完成时间
     */
    private LocalDateTime completedTime;

    /**
     * 执行结果
     */
    private JSONObject result;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 最大重试次数
     */
    private Integer maxRetryCount;

    /**
     * 超时时间（毫秒）
     */
    private Long timeout;

    /**
     * 优先级（1-10，数字越小优先级越高）
     */
    private Integer priority;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 扩展属性
     */
    private JSONObject attributes;

    /**
     * 构造函数
     */
    public CommandTask() {
        this.content = new JSONObject();
        this.result = new JSONObject();
        this.attributes = new JSONObject();
        this.status = CommandStatus.PENDING;
        this.priority = 5; // 默认优先级
        this.retryCount = 0;
    }

    public CommandTask(String gatewayId, String commandType, JSONObject content) {
        this();
        this.gatewayId = gatewayId;
        this.commandType = commandType;
        this.content = content != null ? content : new JSONObject();
    }

    /**
     * 指令状态枚举
     */
    public enum CommandStatus {
        PENDING("待处理"),
        SENT("已发送"),
        COMPLETED("已完成"),
        FAILED("执行失败"),
        TIMEOUT("执行超时"),
        CANCELLED("已取消");

        private final String description;

        CommandStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }

        /**
         * 判断是否为终止状态
         * 
         * @return 是否为终止状态
         */
        public boolean isTerminal() {
            return this == COMPLETED || this == FAILED || this == TIMEOUT || this == CANCELLED;
        }

        /**
         * 判断是否为成功状态
         * 
         * @return 是否为成功状态
         */
        public boolean isSuccess() {
            return this == COMPLETED;
        }

        /**
         * 判断是否为失败状态
         * 
         * @return 是否为失败状态
         */
        public boolean isFailure() {
            return this == FAILED || this == TIMEOUT;
        }
    }

    /**
     * 指令类型枚举
     */
    public enum CommandType {
        RESTART_GATEWAY("重启网关"),
        SEND_COMMAND("下发指令"),
        BINARY_TRANSFER("下发二进制数据"),
        TIME_SYNC("时钟同步"),
        READ_COMMAND_COUNT("读指令条数"),
        READ_COMMAND_CONTENT("读指令内容"),
        READ_RUNTIME_INFO("读运行信息"),
        CUSTOM("自定义指令");

        private final String description;

        CommandType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    // 便捷方法

    /**
     * 判断指令是否完成
     * 
     * @return 是否完成
     */
    public boolean isCompleted() {
        return status != null && status.isTerminal();
    }

    /**
     * 判断指令是否成功
     * 
     * @return 是否成功
     */
    public boolean isSuccess() {
        return status != null && status.isSuccess();
    }

    /**
     * 判断指令是否失败
     * 
     * @return 是否失败
     */
    public boolean isFailure() {
        return status != null && status.isFailure();
    }

    /**
     * 判断是否可以重试
     * 
     * @return 是否可以重试
     */
    public boolean canRetry() {
        return status == CommandStatus.FAILED && 
               retryCount != null && maxRetryCount != null && 
               retryCount < maxRetryCount;
    }

    /**
     * 获取执行时长（毫秒）
     * 
     * @return 执行时长，如果未完成返回null
     */
    public Long getExecutionDuration() {
        if (sentTime == null || completedTime == null) {
            return null;
        }
        
        return java.time.Duration.between(sentTime, completedTime).toMillis();
    }

    /**
     * 获取等待时长（毫秒）
     * 
     * @return 等待时长，如果未发送返回null
     */
    public Long getWaitingDuration() {
        if (createdTime == null || sentTime == null) {
            return null;
        }
        
        return java.time.Duration.between(createdTime, sentTime).toMillis();
    }

    /**
     * 判断是否超时
     * 
     * @return 是否超时
     */
    public boolean isTimeout() {
        if (sentTime == null || timeout == null) {
            return false;
        }
        
        LocalDateTime timeoutTime = sentTime.plusNanos(timeout * 1_000_000);
        return LocalDateTime.now().isAfter(timeoutTime);
    }

    /**
     * 设置内容属性
     * 
     * @param key 键
     * @param value 值
     */
    public void putContent(String key, Object value) {
        if (content == null) {
            content = new JSONObject();
        }
        content.set(key, value);
    }

    /**
     * 获取内容属性
     * 
     * @param key 键
     * @return 值
     */
    public Object getContentValue(String key) {
        return content != null ? content.get(key) : null;
    }

    /**
     * 设置结果属性
     * 
     * @param key 键
     * @param value 值
     */
    public void putResult(String key, Object value) {
        if (result == null) {
            result = new JSONObject();
        }
        result.set(key, value);
    }

    /**
     * 获取结果属性
     * 
     * @param key 键
     * @return 值
     */
    public Object getResultValue(String key) {
        return result != null ? result.get(key) : null;
    }

    /**
     * 设置扩展属性
     * 
     * @param key 键
     * @param value 值
     */
    public void putAttribute(String key, Object value) {
        if (attributes == null) {
            attributes = new JSONObject();
        }
        attributes.set(key, value);
    }

    /**
     * 获取扩展属性
     * 
     * @param key 键
     * @return 值
     */
    public Object getAttributeValue(String key) {
        return attributes != null ? attributes.get(key) : null;
    }

    /**
     * 标记为成功完成
     * 
     * @param result 执行结果
     */
    public void markCompleted(JSONObject result) {
        this.status = CommandStatus.COMPLETED;
        this.result = result != null ? result : new JSONObject();
        this.completedTime = LocalDateTime.now();
        this.errorMessage = null;
    }

    /**
     * 标记为失败
     * 
     * @param errorMessage 错误信息
     */
    public void markFailed(String errorMessage) {
        this.status = CommandStatus.FAILED;
        this.errorMessage = errorMessage;
        this.completedTime = LocalDateTime.now();
    }

    /**
     * 标记为超时
     */
    public void markTimeout() {
        this.status = CommandStatus.TIMEOUT;
        this.errorMessage = "指令执行超时";
        this.completedTime = LocalDateTime.now();
    }

    /**
     * 标记为已取消
     * 
     * @param reason 取消原因
     */
    public void markCancelled(String reason) {
        this.status = CommandStatus.CANCELLED;
        this.errorMessage = reason != null ? reason : "指令已被取消";
        this.completedTime = LocalDateTime.now();
    }

    /**
     * 增加重试次数
     */
    public void incrementRetryCount() {
        if (retryCount == null) {
            retryCount = 0;
        }
        retryCount++;
    }

    /**
     * 重置为待处理状态
     */
    public void resetToPending() {
        this.status = CommandStatus.PENDING;
        this.sentTime = null;
        this.completedTime = null;
        this.errorMessage = null;
    }

    /**
     * 创建指令任务的摘要信息
     * 
     * @return 摘要信息
     */
    public String getSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append("指令任务[")
               .append("ID=").append(taskId)
               .append(", 网关=").append(gatewayId)
               .append(", 类型=").append(commandType)
               .append(", 状态=").append(status != null ? status.getDescription() : "未知")
               .append(", 重试=").append(retryCount).append("/").append(maxRetryCount)
               .append("]");
        return summary.toString();
    }

    @Override
    public String toString() {
        return getSummary();
    }
} 