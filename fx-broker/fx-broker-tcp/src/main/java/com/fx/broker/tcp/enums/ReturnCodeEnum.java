package com.fx.broker.tcp.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 处理结果码枚举
 * 定义协议处理的各种返回码
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ReturnCodeEnum {

    SUCCESS(0, "成功", "处理成功"),
    NOT_SUPPORTED(1, "不支持", "当前功能码不支持"),
    GATEWAY_NOT_EXIST(2, "网关编号不存在", "未在平台配置此网关编号"),
    PASSWORD_ERROR(3, "密码错误", "网关密码验证失败"),
    FORMAT_ERROR(4, "上报格式错误", "上报数据时，格式仅支持json和bin"),
    DEVICE_KEY_ERROR(5, "设备key错误", "平台未配置此KEY"),
    MISSING_PARAMETER(6, "缺少参数", "发送的包内容中，参数不完整"),
    TOTAL_SIZE_OVERFLOW(7, "总体积超出范围", "下发二进制文件，超过最大范围"),
    PACKAGE_SIZE_OVERFLOW(8, "当前包超出范围", "下发二进制文件，超过一次写入最大值"),
    MEMORY_INSUFFICIENT(9, "内存不足", "内存不足，无法打包发送信息"),
    PARAMETER_TYPE_ERROR(10, "参数类型错误", "参数类型不匹配"),
    UNSUPPORTED_BINARY_TYPE(11, "不支持的二进制数据类型", "下发二进制数据时数据类型错误"),
    BASE_DECODE_FAILED(12, "base解码失败", "Base64解码失败"),
    CROSS_PAGE_FORBIDDEN(13, "禁止跨页", "下发固件时，一页时2048，禁止跨页写入数据"),
    STRING_LENGTH_OVERFLOW(14, "String长度超出范围", "String类型的数据，长度超过范围"),
    GATEWAY_NOT_LOGIN(15, "网关未登陆", "当前IP地址未登陆");

    /**
     * 结果码
     */
    private final Integer code;

    /**
     * 结果码名称
     */
    private final String name;

    /**
     * 结果码说明
     */
    private final String description;

    /**
     * 根据结果码获取枚举
     *
     * @param code 结果码
     * @return 对应的枚举，如果不存在返回null
     */
    public static ReturnCodeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }

        for (ReturnCodeEnum returnCode : values()) {
            if (returnCode.getCode().equals(code)) {
                return returnCode;
            }
        }
        return null;
    }

    /**
     * 根据结果码获取名称
     *
     * @param code 结果码
     * @return 结果码名称，如果不存在返回null
     */
    public static String getNameByCode(Integer code) {
        ReturnCodeEnum returnCode = getByCode(code);
        return returnCode != null ? returnCode.getName() : null;
    }

    /**
     * 根据结果码获取说明
     *
     * @param code 结果码
     * @return 结果码说明，如果不存在返回null
     */
    public static String getDescriptionByCode(Integer code) {
        ReturnCodeEnum returnCode = getByCode(code);
        return returnCode != null ? returnCode.getDescription() : null;
    }

    /**
     * 判断是否为成功结果码
     *
     * @param code 结果码
     * @return 是否为成功
     */
    public static boolean isSuccess(Integer code) {
        return SUCCESS.getCode().equals(code);
    }

    /**
     * 判断是否为失败结果码
     *
     * @param code 结果码
     * @return 是否为失败
     */
    public static boolean isFailure(Integer code) {
        return !isSuccess(code);
    }

    /**
     * 获取错误信息
     *
     * @return 格式化的错误信息
     */
    public String getErrorMessage() {
        return String.format("错误码: %d, 错误名称: %s, 错误说明: %s",
                this.code, this.name, this.description);
    }

    /**
     * 获取简短错误信息
     *
     * @return 简短的错误信息
     */
    public String getShortErrorMessage() {
        return String.format("%s(%d)", this.name, this.code);
    }

}