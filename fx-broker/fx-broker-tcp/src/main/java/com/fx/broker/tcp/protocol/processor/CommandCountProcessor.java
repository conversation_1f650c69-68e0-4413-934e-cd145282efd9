package com.fx.broker.tcp.protocol.processor;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.fx.broker.tcp.domain.ProtocolMessage;
import com.fx.broker.tcp.enums.FunctionCodeEnum;
import com.fx.broker.tcp.enums.ReturnCodeEnum;
import com.fx.broker.tcp.service.CommandService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 功能码 8：读指令条数处理器
 * 处理服务器查询网关未处理指令数量的请求
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class CommandCountProcessor extends BaseCommandProcessor {

    @Autowired
    private CommandService commandService;

    @Override
    public Integer getFunctionCode() {
        return FunctionCodeEnum.COMMAND_COUNT.getCode();
    }

    @Override
    public String getProcessorName() {
        return "读指令条数处理器";
    }

    @Override
    protected ProtocolMessage doProcess(ProtocolMessage message) {
//        if (validateDownlinkRequest(message)) {
//            // 处理下行查询请求（服务器发送给网关）
//            return processDownlinkCountQuery(message);
//        } else if (validateUplinkResponse(message)) {
//            // 处理上行响应（网关回复服务器）
//            return processUplinkCountResponse(message);
//        } else {
//            log.warn("指令条数查询消息格式错误 - 连接ID: {}, 上行: {}, 响应: {}",
//                    message.getConnectionId(), message.isUplink(), message.isResponse());
//            return createErrorResponse(message, ReturnCodeEnum.FORMAT_ERROR);
//        }
        return null;
    }

    /**
     * 处理下行指令条数查询
     * 服务器查询网关未处理的指令数量
     */
    private ProtocolMessage processDownlinkCountQuery(ProtocolMessage message) {
        try {
            String gatewayId = getGatewayId(message);

            log.info("收到指令条数查询 - 网关ID: {}, 连接ID: {}", gatewayId, message.getConnectionId());

            // 查询未处理的指令
            List<Long> pendingTaskIds = commandService.getPendingCommandIds(gatewayId);
            int taskCount = pendingTaskIds.size();

            // 创建响应数据
            JSONObject responseData = new JSONObject();
            responseData.set("task_num", taskCount);

            // 转换任务ID列表
            JSONArray taskIdArray = new JSONArray();
            for (Long taskId : pendingTaskIds) {
                taskIdArray.add(taskId);
            }
            responseData.set("task_id", taskIdArray);

            logProcessing(message, "指令条数查询成功 - 网关ID: " + gatewayId +
                    ", 未处理指令数: " + taskCount);

            // 记录查询事件
            recordCommandCountEvent(gatewayId, taskCount, pendingTaskIds, "COUNT_QUERIED");

            return createSuccessResponse(message, responseData);

        } catch (Exception e) {
            log.error("处理下行指令条数查询时发生异常 - 连接ID: {}", message.getConnectionId(), e);
            return createErrorResponse(message, ReturnCodeEnum.MEMORY_INSUFFICIENT);
        }
    }

    /**
     * 处理上行指令条数响应
     * 网关回复的指令条数信息
     */
    private ProtocolMessage processUplinkCountResponse(ProtocolMessage message) {
        Integer retCode = message.getRetCode();
        if (retCode == null) {
            log.warn("指令条数响应缺少结果码 - 连接ID: {}", message.getConnectionId());
            return createErrorResponse(message, ReturnCodeEnum.MISSING_PARAMETER);
        }

        JSONObject data = message.getData();
        if (data == null && retCode == 0) {
            log.warn("指令条数响应缺少数据 - 连接ID: {}", message.getConnectionId());
            return createErrorResponse(message, ReturnCodeEnum.MISSING_PARAMETER);
        }

        try {
            String gatewayId = getGatewayId(message);

            if (retCode == 0) {
                // 验证响应数据格式
                if (hasMissingRequiredFields(data, "task_num", "task_id")) {
                    log.warn("指令条数响应数据格式错误 - 连接ID: {}", message.getConnectionId());
                    return createErrorResponse(message, ReturnCodeEnum.MISSING_PARAMETER);
                }

                Integer taskNum = data.getInt("task_num");
                JSONArray taskIdArray = data.getJSONArray("task_id");

                if (taskNum == null || taskNum < 0) {
                    log.warn("指令条数无效 - 连接ID: {}, 指令数: {}", message.getConnectionId(), taskNum);
                    return createErrorResponse(message, ReturnCodeEnum.MISSING_PARAMETER);
                }

                // 转换任务ID数组
                List<Long> taskIds = new java.util.ArrayList<>();
                if (taskIdArray != null) {
                    for (int i = 0; i < taskIdArray.size(); i++) {
                        Object taskIdObj = taskIdArray.get(i);
                        if (taskIdObj instanceof Number) {
                            taskIds.add(((Number) taskIdObj).longValue());
                        }
                    }
                }

                // 验证数据一致性
                if (taskNum != taskIds.size()) {
                    log.warn("指令条数与任务ID数量不匹配 - 连接ID: {}, 声明数量: {}, 实际数量: {}",
                            message.getConnectionId(), taskNum, taskIds.size());
                }

                log.info("网关回复指令条数 - 网关ID: {}, 指令数: {}, 任务ID: {}, 连接ID: {}",
                        gatewayId, taskNum, taskIds, message.getConnectionId());

                // 记录响应事件
                recordCommandCountEvent(gatewayId, taskNum, taskIds, "COUNT_REPORTED");

                logProcessing(message, "网关回复指令条数 - 网关ID: " + gatewayId +
                        ", 指令数: " + taskNum);

            } else {
                log.warn("网关查询指令条数失败 - 网关ID: {}, 结果码: {}", gatewayId, retCode);

                // 记录查询失败事件
                recordCommandCountEvent(gatewayId, 0, null, "COUNT_QUERY_FAILED");
            }

            // 上行响应不需要再回复
            return NO_RESPONSE;

        } catch (Exception e) {
            log.error("处理上行指令条数响应时发生异常 - 连接ID: {}", message.getConnectionId(), e);
            return createErrorResponse(message, ReturnCodeEnum.MEMORY_INSUFFICIENT);
        }
    }

    /**
     * 记录指令条数查询事件
     */
    private void recordCommandCountEvent(String gatewayId, Integer taskCount,
            List<Long> taskIds, String eventType) {
        try {
            JSONObject countEvent = new JSONObject();
            countEvent.set("gateway_id", gatewayId);
            countEvent.set("task_count", taskCount);
            countEvent.set("event_type", eventType);
            countEvent.set("function_code", 8);
            countEvent.set("event_time", System.currentTimeMillis() / 1000);
            countEvent.set("description", getEventDescription(eventType));

            if (taskIds != null && !taskIds.isEmpty()) {
                JSONArray taskIdArray = new JSONArray();
                taskIds.forEach(taskIdArray::add);
                countEvent.set("task_ids", taskIdArray);
            }

            // TODO: 保存到数据库或缓存
            log.info("记录指令条数事件 - 网关ID: {}, 指令数: {}, 事件类型: {}",
                    gatewayId, taskCount, eventType);

        } catch (Exception e) {
            log.error("记录指令条数事件失败 - 网关ID: {}, 事件类型: {}", gatewayId, eventType, e);
        }
    }

    /**
     * 获取事件描述
     */
    private String getEventDescription(String eventType) {
        switch (eventType) {
            case "COUNT_QUERIED":
                return "服务器查询网关指令条数";
            case "COUNT_REPORTED":
                return "网关回复指令条数";
            case "COUNT_QUERY_FAILED":
                return "网关查询指令条数失败";
            default:
                return "未知指令条数事件";
        }
    }

    /**
     * 创建指令条数查询消息
     * 供外部调用，用于主动查询网关指令条数
     */
    public ProtocolMessage createCommandCountQuery(String gatewayId) {
        ProtocolMessage queryMessage = new ProtocolMessage();
        queryMessage.setIsUplink(false);
        queryMessage.setIsResponse(false);
        queryMessage.setPackId(0); // 使用默认包序号
        queryMessage.setCode(8);
        queryMessage.setGatewayId(gatewayId);
        queryMessage.setData(new JSONObject());

        return queryMessage;
    }

    /**
     * 获取网关指令统计信息
     */
    public JSONObject getGatewayCommandStatistics(String gatewayId) {
        try {
            List<Long> pendingTaskIds = commandService.getPendingCommandIds(gatewayId);
            List<Long> executingTaskIds = commandService.getExecutingCommandIds(gatewayId);
            List<Long> completedTaskIds = commandService.getCompletedCommandIds(gatewayId);

            JSONObject stats = new JSONObject();
            stats.set("gateway_id", gatewayId);
            stats.set("pending_count", pendingTaskIds.size());
            stats.set("executing_count", executingTaskIds.size());
            stats.set("completed_count", completedTaskIds.size());
            stats.set("total_count", pendingTaskIds.size() + executingTaskIds.size() + completedTaskIds.size());

            // 添加任务ID列表
            JSONArray pendingArray = new JSONArray();
            pendingTaskIds.forEach(pendingArray::add);
            stats.set("pending_task_ids", pendingArray);

            JSONArray executingArray = new JSONArray();
            executingTaskIds.forEach(executingArray::add);
            stats.set("executing_task_ids", executingArray);

            return stats;

        } catch (Exception e) {
            log.error("获取网关指令统计信息失败 - 网关ID: {}", gatewayId, e);

            JSONObject errorStats = new JSONObject();
            errorStats.set("gateway_id", gatewayId);
            errorStats.set("error", "获取统计信息失败");
            return errorStats;
        }
    }

    /**
     * 检查网关是否有未处理指令
     */
    public boolean hasPendingCommands(String gatewayId) {
        try {
            List<Long> pendingTaskIds = commandService.getPendingCommandIds(gatewayId);
            return !pendingTaskIds.isEmpty();
        } catch (Exception e) {
            log.error("检查网关未处理指令失败 - 网关ID: {}", gatewayId, e);
            return false;
        }
    }

    /**
     * 获取网关最早的未处理指令
     */
    public Long getOldestPendingCommand(String gatewayId) {
        try {
            List<Long> pendingTaskIds = commandService.getPendingCommandIds(gatewayId);
            if (!pendingTaskIds.isEmpty()) {
                // 假设列表已按时间排序，返回第一个
                return pendingTaskIds.get(0);
            }
            return null;
        } catch (Exception e) {
            log.error("获取网关最早未处理指令失败 - 网关ID: {}", gatewayId, e);
            return null;
        }
    }

    /**
     * 清理已完成的指令记录
     */
    public int cleanupCompletedCommands(String gatewayId, long beforeTimestamp) {
        try {
            int cleanedCount = commandService.cleanupCompletedCommands(gatewayId, beforeTimestamp);
            log.info("清理已完成指令记录 - 网关ID: {}, 清理数量: {}", gatewayId, cleanedCount);
            return cleanedCount;
        } catch (Exception e) {
            log.error("清理已完成指令记录失败 - 网关ID: {}", gatewayId, e);
            return 0;
        }
    }
}