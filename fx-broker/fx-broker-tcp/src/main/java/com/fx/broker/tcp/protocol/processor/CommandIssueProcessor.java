package com.fx.broker.tcp.protocol.processor;

import cn.hutool.json.JSONObject;
import com.fx.broker.tcp.domain.CommandTask;
import com.fx.broker.tcp.domain.ProtocolMessage;
import com.fx.broker.tcp.enums.FunctionCodeEnum;
import com.fx.broker.tcp.enums.ReturnCodeEnum;
import com.fx.broker.tcp.service.CommandService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 功能码 5：下发指令处理器
 * 处理服务器向网关下发的摄像模块指令和网关的接收确认
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class CommandIssueProcessor extends BaseCommandProcessor {

    @Resource
    private CommandService commandService;

    @Override
    public Integer getFunctionCode() {
        return FunctionCodeEnum.COMMAND_ISSUE.getCode();
    }

    @Override
    public String getProcessorName() {
        return "下发指令处理器";
    }

    @Override
    protected ProtocolMessage doProcess(ProtocolMessage message) {
//        if (validateDownlinkRequest(message)) {
//            // 处理下行指令（服务器发送给网关）
//            return processDownlinkCommand(message);
//        } else if (validateUplinkResponse(message)) {
//            // 处理上行响应（网关回复服务器）
//            return processUplinkCommandResponse(message);
//        } else {
//            log.warn("指令消息格式错误 - 连接ID: {}, 上行: {}, 响应: {}",
//                    message.getConnectionId(), message.isUplink(), message.isResponse());
//            return createErrorResponse(message, ReturnCodeEnum.FORMAT_ERROR);
//        }
        return null;
    }

    /**
     * 处理下行指令
     * 服务器发送给网关的摄像模块指令
     */
    private ProtocolMessage processDownlinkCommand(ProtocolMessage message) {
        JSONObject data = message.getData();
        if (data == null) {
            log.warn("下发指令消息缺少数据 - 连接ID: {}", message.getConnectionId());
            return createErrorResponse(message, ReturnCodeEnum.MISSING_PARAMETER);
        }

        // 验证必要字段
        if (hasMissingRequiredFields(data, "task_id", "content")) {
            log.warn("下发指令消息缺少必要参数 - 连接ID: {}", message.getConnectionId());
            return createErrorResponse(message, ReturnCodeEnum.MISSING_PARAMETER);
        }

        Long taskId = data.getLong("task_id");
        JSONObject content = data.getJSONObject("content");

        if (taskId == null || taskId <= 0) {
            log.warn("指令ID无效 - 连接ID: {}, 指令ID: {}", message.getConnectionId(), taskId);
            return createErrorResponse(message, ReturnCodeEnum.MISSING_PARAMETER);
        }

        if (content == null || content.isEmpty()) {
            log.warn("指令内容为空 - 连接ID: {}, 指令ID: {}", message.getConnectionId(), taskId);
            return createErrorResponse(message, ReturnCodeEnum.MISSING_PARAMETER);
        }

        try {
            String gatewayId = getGatewayId(message);

            log.info("收到下发指令 - 网关ID: {}, 指令ID: {}, 连接ID: {}",
                    gatewayId, taskId, message.getConnectionId());

            // 创建指令任务
            CommandTask created = commandService.createCommand(gatewayId, "COMMAND_ISSUED", content);
            if (created == null) {
                log.warn("创建指令任务失败 - 网关ID: {}, 指令ID: {}", gatewayId, taskId);
                return createErrorResponse(message, ReturnCodeEnum.MEMORY_INSUFFICIENT);
            }

            // 记录指令下发事件
            recordCommandIssueEvent(gatewayId, taskId, content, "COMMAND_ISSUED");

            // 发布指令下发事件到MQ
            publishCommandEventToMQ(gatewayId, taskId, "COMMAND_ISSUED", "指令下发成功", content);

            logProcessing(message, "下发指令成功 - 网关ID: " + gatewayId + ", 指令ID: " + taskId);

            // 下行指令不需要返回响应，网关会发送上行响应
            return NO_RESPONSE;

        } catch (Exception e) {
            log.error("处理下发指令时发生异常 - 连接ID: {}, 指令ID: {}",
                    message.getConnectionId(), taskId, e);
            return createErrorResponse(message, ReturnCodeEnum.MEMORY_INSUFFICIENT);
        }
    }

    /**
     * 处理上行指令响应
     * 网关对下发指令的接收确认
     */
    private ProtocolMessage processUplinkCommandResponse(ProtocolMessage message) {
        Integer retCode = message.getRetCode();
        if (retCode == null) {
            log.warn("指令响应缺少结果码 - 连接ID: {}", message.getConnectionId());
            return createErrorResponse(message, ReturnCodeEnum.MISSING_PARAMETER);
        }

        // 从消息中获取指令ID（可能在data中）
        Long taskId = null;
        JSONObject data = message.getData();
        if (data != null) {
            taskId = data.getLong("task_id");
        }

        try {
            String gatewayId = getGatewayId(message);

            if (retCode == 0) {
                log.info("网关确认接收指令 - 网关ID: {}, 指令ID: {}, 连接ID: {}",
                        gatewayId, taskId, message.getConnectionId());

                // 更新指令状态为已接收
                if (taskId != null) {
                    // TODO: 实现指令状态更新逻辑
                    log.info("网关确认接收指令 - 任务ID: {}", taskId);
                }

                // 记录指令接收确认事件
                recordCommandIssueEvent(gatewayId, taskId, null, "COMMAND_RECEIVED");

                // 发布指令接收事件到MQ
                publishCommandEventToMQ(gatewayId, taskId, "COMMAND_RECEIVED", "网关确认接收指令", null);

                logProcessing(message, "网关确认接收指令 - 网关ID: " + gatewayId + ", 指令ID: " + taskId);

            } else {
                log.warn("网关拒绝接收指令 - 网关ID: {}, 指令ID: {}, 结果码: {}",
                        gatewayId, taskId, retCode);

                // 更新指令状态为拒绝
                if (taskId != null) {
                    // TODO: 实现指令状态更新逻辑
                    log.warn("网关拒绝接收指令 - 任务ID: {}", taskId);
                }

                // 记录指令拒绝事件
                recordCommandIssueEvent(gatewayId, taskId, null, "COMMAND_REJECTED");

                // 发布指令拒绝事件到MQ
                publishCommandEventToMQ(gatewayId, taskId, "COMMAND_REJECTED",
                        "网关拒绝接收指令，结果码: " + retCode, null);
            }

            // 上行响应不需要再回复
            return NO_RESPONSE;

        } catch (Exception e) {
            log.error("处理上行指令响应时发生异常 - 连接ID: {}, 指令ID: {}",
                    message.getConnectionId(), taskId, e);
            return createErrorResponse(message, ReturnCodeEnum.MEMORY_INSUFFICIENT);
        }
    }

    /**
     * 记录指令下发事件
     */
    private void recordCommandIssueEvent(String gatewayId, Long taskId, JSONObject content, String eventType) {
        try {
            JSONObject commandEvent = new JSONObject();
            commandEvent.set("gateway_id", gatewayId);
            commandEvent.set("task_id", taskId);
            commandEvent.set("event_type", eventType);
            commandEvent.set("function_code", 5);
            commandEvent.set("event_time", System.currentTimeMillis() / 1000);
            commandEvent.set("description", getEventDescription(eventType));

            if (content != null) {
                commandEvent.set("command_content", content);
            }

            // TODO: 保存到数据库或缓存
            log.info("记录指令事件 - 网关ID: {}, 指令ID: {}, 事件类型: {}", gatewayId, taskId, eventType);

        } catch (Exception e) {
            log.error("记录指令事件失败 - 网关ID: {}, 指令ID: {}, 事件类型: {}",
                    gatewayId, taskId, eventType, e);
        }
    }

    /**
     * 发布指令事件到MQ
     */
    private void publishCommandEventToMQ(String gatewayId, Long taskId, String eventType,
            String description, JSONObject content) {
        try {
            JSONObject mqData = new JSONObject();
            mqData.set("gateway_id", gatewayId);
            mqData.set("task_id", taskId);
            mqData.set("event_type", eventType);
            mqData.set("function_code", 5);
            mqData.set("description", description);
            mqData.set("timestamp", System.currentTimeMillis());

            if (content != null) {
                mqData.set("command_content", content);
            }

            // 发布到下行指令主题
            // rocketMQPublisher.publishDownlinkCommand(gatewayId, mqData);

            log.debug("指令事件已发布到MQ - 网关ID: {}, 指令ID: {}, 事件类型: {}",
                    gatewayId, taskId, eventType);

        } catch (Exception e) {
            log.error("发布指令事件到MQ失败 - 网关ID: {}, 指令ID: {}, 事件类型: {}",
                    gatewayId, taskId, eventType, e);
        }
    }

    /**
     * 获取事件描述
     */
    private String getEventDescription(String eventType) {
        switch (eventType) {
            case "COMMAND_ISSUED":
                return "服务器下发摄像模块指令";
            case "COMMAND_RECEIVED":
                return "网关确认接收指令";
            case "COMMAND_REJECTED":
                return "网关拒绝接收指令";
            case "COMMAND_EXECUTED":
                return "指令执行完成";
            case "COMMAND_FAILED":
                return "指令执行失败";
            default:
                return "未知指令事件";
        }
    }
}