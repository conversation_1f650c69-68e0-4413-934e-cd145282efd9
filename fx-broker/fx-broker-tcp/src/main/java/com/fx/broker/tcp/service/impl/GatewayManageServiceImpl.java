package com.fx.broker.tcp.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.fx.broker.tcp.domain.GatewayInfo;
import com.fx.broker.tcp.manager.ConnectionManager;
import com.fx.broker.tcp.mq.publisher.RocketMQPublisher;
import com.fx.broker.tcp.service.GatewayManageService;
import com.fx.link.api.domain.device.model.DeviceInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 网关管理服务实现类
 * 负责网关的状态管理和信息维护
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class GatewayManageServiceImpl implements GatewayManageService {

    @Resource
    private ConnectionManager connectionManager;

    @Resource
    private RocketMQPublisher rocketMQPublisher;

    /**
     * 网关信息缓存
     */
    private final ConcurrentHashMap<String, GatewayInfo> gatewayInfoCache = new ConcurrentHashMap<>();

    /**
     * 未知IP地址常量
     */
    private static final String UNKNOWN_IP = "unknown";

    @Override
    public void updateGatewayInfo(String gatewayId, String version, String connectionId, DeviceInfo deviceInfo) {
        try {
            GatewayInfo gatewayInfo = gatewayInfoCache.computeIfAbsent(gatewayId, k -> new GatewayInfo());

            // 1. 提取客户端IP地址
            String clientIp = extractClientIp(connectionId);

            // 2. 更新基本信息
            gatewayInfo.setGatewayId(gatewayId);
            gatewayInfo.setGatewayName(deviceInfo.getDeviceName());
            gatewayInfo.setVersion(version);
            gatewayInfo.setConnectionId(connectionId);
            gatewayInfo.setClientIp(clientIp);
            gatewayInfo.setOnline(true);
            gatewayInfo.setConnectTime(LocalDateTime.now());
            gatewayInfo.setLastHeartbeatTime(LocalDateTime.now());

            // 3. 发送设备消息
            JSONObject messageData = new JSONObject();
            messageData.set("ip_address", clientIp);
            messageData.set("firmware_version", version);
            messageData.set("last_interaction_time",
                    LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            rocketMQPublisher.publishUplinkData(deviceInfo, messageData);

            log.debug("更新网关信息 - 网关ID: {}, 设备名称: {}, 版本: {}, 连接ID: {}, 客户端IP: {}",
                    gatewayId, deviceInfo.getDeviceName(), version, connectionId, clientIp);

        } catch (Exception e) {
            log.error("更新网关信息异常 - 网关ID: {}", gatewayId, e);
        }
    }

    @Override
    public void handleGatewayDisconnect(String gatewayId, String connectionId) {
        try {
            log.debug("处理网关断开 - 网关ID: {}, 连接ID: {}", gatewayId, connectionId);

            // 检查网关是否还有其他活跃连接
            boolean hasOtherActiveConnections = connectionManager.checkGatewayHasOtherActiveConnections(gatewayId,
                    connectionId);
            if (hasOtherActiveConnections) {
                log.debug("网关还有其他活跃连接，不更新网关状态为离线 - 网关ID: {}, 断开连接ID: {}", gatewayId, connectionId);
                return;
            }

            // 更新网关状态
            GatewayInfo gatewayInfo = gatewayInfoCache.get(gatewayId);
            if (gatewayInfo != null) {
                // 记录断开前的状态信息
                LocalDateTime lastHeartbeat = gatewayInfo.getLastHeartbeatTime();

                // 更新网关状态为离线
                gatewayInfo.setOnline(false);
                gatewayInfo.setDisconnectTime(LocalDateTime.now());
                gatewayInfo.setConnectionId(null);

                // 设置离线原因
                if (lastHeartbeat != null) {
                    long minutesSinceLastHeartbeat = java.time.Duration.between(lastHeartbeat, LocalDateTime.now())
                            .toMinutes();
                    if (minutesSinceLastHeartbeat > 2) {
                        gatewayInfo.setOfflineReason("心跳超时断开");
                    } else {
                        gatewayInfo.setOfflineReason("连接异常断开");
                    }
                } else {
                    gatewayInfo.setOfflineReason("连接断开");
                }

                // 记录离线信息
                log.debug("网关离线信息 - 最后在线时间: {}, 离线时间: {}, 在线时长: {}分钟",
                        gatewayInfo.getConnectTime(), gatewayInfo.getDisconnectTime(),
                        gatewayInfo.getOnlineDurationMinutes());

                log.info("网关状态已更新为离线 - 网关ID: {}, 离线原因: {}, 在线时长: {}分钟",
                        gatewayId, gatewayInfo.getOfflineReason(), gatewayInfo.getOnlineDurationMinutes());
            } else {
                log.warn("网关信息不存在，无法更新状态 - 网关ID: {}", gatewayId);
            }

            log.debug("网关断开处理完成 - 网关ID: {}", gatewayId);

        } catch (Exception e) {
            log.error("处理网关断开异常 - 网关ID: {}, 连接ID: {}", gatewayId, connectionId, e);
        }
    }

    @Override
    public GatewayInfo getGatewayInfo(String gatewayId) {
        return gatewayInfoCache.get(gatewayId);
    }

    @Override
    public void updateHeartbeat(String gatewayId) {
        GatewayInfo gatewayInfo = gatewayInfoCache.get(gatewayId);
        if (gatewayInfo != null) {
            gatewayInfo.setLastHeartbeatTime(LocalDateTime.now());

            // 同时更新连接活跃时间
            ConnectionManager.ConnectionInfo connectionInfo = connectionManager.getConnectionByGatewayId(gatewayId);
            if (connectionInfo != null) {
                connectionManager.updateLastActiveTime(connectionInfo.getConnectionId());
            }
        }
    }

    @Override
    public boolean isGatewayOnline(String gatewayId) {
        GatewayInfo gatewayInfo = gatewayInfoCache.get(gatewayId);
        return gatewayInfo != null && gatewayInfo.isOnline();
    }

    /**
     * 提取客户端 IP 地址（只取IP，不取端口）
     *
     * @param connectionId 连接ID
     * @return 客户端IP地址
     */
    private String extractClientIp(String connectionId) {
        try {
            if (StrUtil.isBlank(connectionId)) {
                return UNKNOWN_IP;
            }

            ConnectionManager.ConnectionInfo connectionInfo = connectionManager.getConnection(connectionId);
            if (connectionInfo == null || connectionInfo.getRemoteAddress() == null) {
                return UNKNOWN_IP;
            }

            String remoteAddress = connectionInfo.getRemoteAddress();

            // 提取IP地址，去除端口号
            if (remoteAddress.contains(":")) {
                return remoteAddress.substring(0, remoteAddress.lastIndexOf(":"));
            }

            return remoteAddress;

        } catch (Exception e) {
            log.warn("提取客户端IP失败 - 连接ID: {}", connectionId, e);
            return UNKNOWN_IP;
        }
    }

}