package com.fx.broker.tcp.config;

import lombok.Data;
import org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

/**
 * RocketMQ 配置类
 * 管理 RocketMQ 相关配置
 * 
 * <AUTHOR>
 */
@Configuration
@ConditionalOnClass(RocketMQTemplate.class)
@Import(RocketMQAutoConfiguration.class)
@ConfigurationProperties(prefix = "rocketmq")
@Data
public class RocketMQConfig {

    /**
     * NameServer 地址
     */
    private String nameServer;

    /**
     * 生产者配置
     */
    private Producer producer = new Producer();

    /**
     * 消费者配置
     */
    private Consumer consumer = new Consumer();

    /**
     * 生产者配置
     */
    @Data
    public static class Producer {
        /**
         * 生产者组名
         */
        private String group;

        /**
         * 发送超时时间（毫秒）
         */
        private Integer sendTimeout = 3000;

        /**
         * 重试次数
         */
        private Integer retryTimes = 3;
    }

    /**
     * 消费者配置
     */
    @Data
    public static class Consumer {
        /**
         * 消费者组名
         */
        private String group;
    }
}