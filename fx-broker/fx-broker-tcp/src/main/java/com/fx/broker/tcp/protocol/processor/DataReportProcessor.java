package com.fx.broker.tcp.protocol.processor;

import cn.hutool.json.JSONObject;
import com.fx.broker.tcp.domain.ProtocolMessage;
import com.fx.broker.tcp.enums.FunctionCodeEnum;
import com.fx.broker.tcp.enums.ReturnCodeEnum;
import com.fx.broker.tcp.service.DataProcessService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 功能码 2：数据上报处理器
 * 处理网关上报的数据，支持JSON格式和二进制格式
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class DataReportProcessor extends BaseCommandProcessor {

    @Resource
    private DataProcessService dataProcessService;

    @Override
    public Integer getFunctionCode() {
        return FunctionCodeEnum.DATA_REPORT.getCode();
    }

    @Override
    public String getProcessorName() {
        return "数据上报处理器";
    }

    @Override
    protected ProtocolMessage doProcess(ProtocolMessage message) {
        // 1. 验证必要参数
        if (hasMissingRequiredFields(message.getData(), "format", "key")) {
            logProcessing(message, "[doProcess][缺少必要参数]");
            return createErrorResponse(message, ReturnCodeEnum.MISSING_PARAMETER);
        }

        // 2. 验证数据格式
        String format = message.getData().getStr("format");
        if (!isValidDataFormat(format)) {
            logProcessing(message, "[doProcess][数据格式错误]");
            return createErrorResponse(message, ReturnCodeEnum.PARAMETER_TYPE_ERROR);
        }

        try {
            // 3. 设置网关ID
            String gatewayId = getGatewayId(message);
            message.setGatewayId(gatewayId);

            // 4. 使用 DataProcessService 统一处理数据上报
            ProtocolMessage result = dataProcessService.processDataUpload(message);

            // 5. 记录处理结果
            if (result.getRetCode() == 0) {
                recordSuccessfulDataReport(message);
            } else {
                logProcessing(message, String.format("[doProcess][数据上报处理失败 - 错误码: %d]", result.getRetCode()));
            }

            return result;

        } catch (Exception e) {
            logProcessing(message, "[doProcess][处理数据上报时发生异常]");
            log.error("[doProcess][处理数据上报时发生异常 - 网关ID: {}, 连接ID: {}]",
                    getGatewayId(message), message.getConnectionId(), e);
            return createErrorResponse(message, ReturnCodeEnum.MEMORY_INSUFFICIENT);
        }
    }

    /**
     * 验证数据格式是否支持
     */
    private boolean isValidDataFormat(String format) {
        return "json".equalsIgnoreCase(format) ||
                "binary".equalsIgnoreCase(format) ||
                "bin".equalsIgnoreCase(format) ||
                "hex".equalsIgnoreCase(format);
    }

    /**
     * 记录成功的数据上报
     */
    private void recordSuccessfulDataReport(ProtocolMessage message) {
        try {
            JSONObject data = message.getData();
            String format = data != null ? data.getStr("format") : "unknown";
            String deviceKey = data != null ? data.getStr("key") : "unknown";

            logProcessing(message,
                    String.format("[recordSuccessfulDataReport][数据上报成功 - 格式: %s, 设备Key: %s]", format, deviceKey));

        } catch (Exception e) {
            log.error("[recordSuccessfulDataReport][记录数据上报信息失败 - 网关ID: {}]", getGatewayId(message), e);
        }
    }

}