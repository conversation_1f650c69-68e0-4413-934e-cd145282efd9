package com.fx.broker.tcp.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * TCP服务器配置类
 * 统一管理TCP服务器相关配置参数，包括心跳配置
 * 
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "tcp-server")
public class TcpServerConfig {

    /**
     * 服务器主机地址
     */
    private String host = "0.0.0.0";

    /**
     * 服务器端口
     */
    private Integer port = 9999;

    /**
     * 最大连接数
     */
    private Integer maxConnections = 1000;

    /**
     * 超时时间（毫秒）- 统一用于连接、读取、写入超时
     */
    private Long timeout = 60000L;

    /**
     * 缓冲区大小（字节）
     */
    private Integer bufferSize = 8192;

    /**
     * 是否启用SSL
     */
    private Boolean sslEnabled = false;

    /**
     * SSL配置
     */
    private SslConfig ssl = new SslConfig();

    /**
     * Vert.x配置
     */
    private VertxConfig vertx = new VertxConfig();

    /**
     * 心跳配置
     */
    private HeartbeatConfig heartbeat = new HeartbeatConfig();

    /**
     * SSL配置
     */
    @Data
    public static class SslConfig {
        /**
         * SSL证书文件路径
         */
        private String certPath;

        /**
         * SSL私钥文件路径
         */
        private String keyPath;

        /**
         * SSL信任证书文件路径
         */
        private String trustPath;

        /**
         * SSL证书密码
         */
        private String certPassword;

        /**
         * SSL私钥密码
         */
        private String keyPassword;
    }

    /**
     * Vert.x配置
     */
    @Data
    public static class VertxConfig {
        /**
         * Worker线程池大小
         */
        private Integer workerPoolSize = 10;

        /**
         * Event Loop线程池大小
         */
        private Integer eventLoopSize = 4;
    }

    /**
     * 心跳配置
     */
    @Data
    public static class HeartbeatConfig {
        /**
         * 是否启用心跳检测
         */
        private Boolean enabled = true;

        /**
         * 心跳检测间隔（毫秒）
         */
        private Long interval = 30000L;

        /**
         * 心跳超时时间（毫秒）
         */
        private Long timeout = 90000L;

        /**
         * 是否将超时连接标记为超时状态而不是删除
         */
        private Boolean markAsTimeout = true;
    }

}