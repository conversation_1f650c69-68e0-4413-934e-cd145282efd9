package com.fx.broker.tcp.service;

import com.fx.broker.tcp.domain.GatewayInfo;
import com.fx.link.api.domain.device.model.DeviceInfo;

/**
 * 网关管理服务接口
 * 负责网关的状态管理和信息维护
 * 
 * <AUTHOR>
 */
public interface GatewayManageService {

    /**
     * 更新网关信息（使用设备信息）
     *
     * @param gatewayId    网关ID
     * @param version      版本
     * @param connectionId 连接ID
     * @param deviceInfo   设备信息
     */
    void updateGatewayInfo(String gatewayId, String version, String connectionId, DeviceInfo deviceInfo);

    /**
     * 处理网关断开
     * 
     * @param gatewayId    网关ID
     * @param connectionId 连接ID
     */
    void handleGatewayDisconnect(String gatewayId, String connectionId);

    /**
     * 获取网关信息
     * 
     * @param gatewayId 网关ID
     * @return 网关信息
     */
    GatewayInfo getGatewayInfo(String gatewayId);

    /**
     * 更新网关心跳
     *
     * @param gatewayId 网关ID
     */
    void updateHeartbeat(String gatewayId);

    /**
     * 检查网关是否在线
     * 
     * @param gatewayId 网关ID
     * @return 是否在线
     */
    boolean isGatewayOnline(String gatewayId);
}