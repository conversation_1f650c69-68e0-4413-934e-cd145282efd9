package com.fx.broker.tcp.manager;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.fx.broker.tcp.config.TcpServerConfig;
import com.fx.broker.tcp.mq.publisher.RocketMQPublisher;
import com.fx.broker.tcp.service.GatewayManageService;
import com.fx.common.core.domain.R;
import com.fx.common.core.domain.ThingModelMessage;
import com.fx.link.api.RemoteDeviceService;
import com.fx.link.api.domain.device.model.DeviceInfo;
import io.vertx.core.Vertx;
import io.vertx.core.net.NetSocket;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 连接管理器
 * 负责管理 TCP 连接的生命周期、心跳检测和网关绑定
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class ConnectionManager {

    @Resource
    private TcpServerConfig tcpServerConfig;

    @Resource
    private GatewayManageService gatewayManageService;

    @Resource
    private RemoteDeviceService remoteDeviceService;

    @Resource
    private RocketMQPublisher rocketMQPublisher;

    /**
     * 连接 ID 生成器
     */
    private final AtomicLong connectionIdGenerator = new AtomicLong(0);

    /**
     * 连接信息存储
     */
    private final ConcurrentHashMap<String, ConnectionInfo> connections = new ConcurrentHashMap<>();

    /**
     * 网关 ID 到连接 ID 的映射
     */
    private final ConcurrentHashMap<String, String> gatewayConnections = new ConcurrentHashMap<>();

    /**
     * 心跳管理
     */
    private Vertx vertx;
    private Long heartbeatTimerId;

    /**
     * 启动心跳检测
     */
    public void startHeartbeat(Vertx vertx) {
        if (ObjectUtil.isNull(vertx)) {
            log.error("[startHeartbeat][启动心跳检测失败：Vert.x 实例为空]");
            return;
        }

        this.vertx = vertx;
        TcpServerConfig.HeartbeatConfig config = tcpServerConfig.getHeartbeat();

        if (ObjectUtil.isNull(config) || ObjectUtil.isNull(config.getEnabled()) || !config.getEnabled()) {
            log.info("[startHeartbeat][心跳检测已禁用]");
            return;
        }

        long interval = ObjectUtil.isNotNull(config.getInterval()) ? config.getInterval() : 30000L;
        log.info("[startHeartbeat][启动心跳检测 - 检测间隔: {}ms]", interval);

        // 1. 启动定时心跳检测任务
        heartbeatTimerId = vertx.setPeriodic(interval, this::performHeartbeatCheck);
        log.info("[startHeartbeat][心跳检测启动成功]");
    }

    /**
     * 停止心跳检测
     */
    public void stopHeartbeat() {
        log.info("[stopHeartbeat][停止心跳检测]");

        if (ObjectUtil.isNotNull(heartbeatTimerId) && ObjectUtil.isNotNull(vertx)) {
            vertx.cancelTimer(heartbeatTimerId);
            heartbeatTimerId = null;
        }

        log.info("[stopHeartbeat][心跳检测停止完成]");
    }

    /**
     * 执行心跳检测
     */
    private void performHeartbeatCheck(Long timerId) {
        if (connections.isEmpty()) {
            return;
        }

        TcpServerConfig.HeartbeatConfig config = tcpServerConfig.getHeartbeat();
        if (ObjectUtil.isNull(config)) {
            return;
        }

        LocalDateTime now = LocalDateTime.now();
        long timeoutThreshold = ObjectUtil.isNotNull(config.getTimeout()) ? config.getTimeout() : 90000L;
        int timeoutCount = 0;

        // 1. 遍历所有连接，检查超时
        for (ConnectionInfo connectionInfo : connections.values()) {
            try {
                long idleTimeMs = ChronoUnit.MILLIS.between(connectionInfo.getLastActiveTime(), now);

                if (idleTimeMs > timeoutThreshold) {
                    handleTimeoutConnection(connectionInfo);
                    timeoutCount++;
                }
            } catch (Exception e) {
                log.error("[performHeartbeatCheck][心跳检测异常 - 连接 ID: {}]", connectionInfo.getConnectionId(), e);
            }
        }

        if (timeoutCount > 0) {
            log.info("[performHeartbeatCheck][心跳检测完成 - 超时处理: {}, 监控总数: {}]", timeoutCount, connections.size());
        }
    }

    /**
     * 处理超时连接
     */
    private void handleTimeoutConnection(ConnectionInfo connectionInfo) {
        try {
            String connectionId = connectionInfo.getConnectionId();
            NetSocket socket = connectionInfo.getSocket();
            String gatewayId = connectionInfo.getGatewayId();

            // 1. 关闭 Socket 连接
            if (ObjectUtil.isNotNull(socket)) {
                socket.close();
            }

            // 2. 根据配置决定是否标记为超时状态
            boolean markAsTimeout = ObjectUtil.isNotNull(tcpServerConfig.getHeartbeat()) &&
                    ObjectUtil.isNotNull(tcpServerConfig.getHeartbeat().getMarkAsTimeout())
                    ? tcpServerConfig.getHeartbeat().getMarkAsTimeout()
                    : true;

            if (markAsTimeout) {
                connectionInfo.setStatus(ConnectionStatus.TIMEOUT);
                log.info("[handleTimeoutConnection][心跳超时，连接已标记为超时状态 - 连接 ID: {}]", connectionId);
            } else {
                // 传统模式：直接删除连接
                removeConnection(connectionId);
                log.info("[handleTimeoutConnection][心跳超时，连接已删除 - 连接 ID: {}]", connectionId);
                return;
            }

            // 3. 检查网关是否还有其他活跃连接
            if (StrUtil.isNotBlank(gatewayId) && !checkGatewayHasOtherActiveConnections(gatewayId, connectionId)) {
                log.info("[handleTimeoutConnection][心跳超时，更新网关状态为离线 - 网关 ID: {}, 连接 ID: {}]", gatewayId,
                        connectionId);
                gatewayManageService.handleGatewayDisconnect(gatewayId, connectionId);
            }

        } catch (Exception e) {
            log.error("[handleTimeoutConnection][处理超时连接时发生异常 - 连接 ID: {}]", connectionInfo.getConnectionId(), e);
        }
    }

    /**
     * 添加新连接
     */
    public String addConnection(NetSocket socket) {
        if (ObjectUtil.isNull(socket)) {
            log.warn("[addConnection][添加连接失败：socket 为空]");
            return null;
        }

        String connectionId = generateConnectionId();
        String remoteAddress = socket.remoteAddress().toString();

        ConnectionInfo connectionInfo = new ConnectionInfo(
                connectionId,
                socket,
                remoteAddress,
                LocalDateTime.now(),
                ConnectionStatus.CONNECTED);

        connections.put(connectionId, connectionInfo);

        log.info("[addConnection][添加 TCP 连接 - 连接 ID: {}, 远程地址: {}, 当前连接数: {}]",
                connectionId, remoteAddress, connections.size());

        return connectionId;
    }

    /**
     * 移除连接
     */
    public void removeConnection(String connectionId) {
        if (StrUtil.isBlank(connectionId)) {
            log.warn("[removeConnection][移除连接失败：连接 ID 为空]");
            return;
        }

        ConnectionInfo connectionInfo = connections.remove(connectionId);

        if (ObjectUtil.isNotNull(connectionInfo)) {
            // 1. 处理网关连接解绑
            handleGatewayConnectionUnbind(connectionInfo, connectionId);

            log.info("[removeConnection][移除 TCP 连接 - 连接 ID: {}, 远程地址: {}, 剩余连接数: {}]",
                    connectionId, connectionInfo.getRemoteAddress(), connections.size());
        } else {
            log.warn("[removeConnection][尝试移除不存在的连接 - 连接 ID: {}]", connectionId);
        }
    }

    /**
     * 处理网关连接解绑
     */
    private void handleGatewayConnectionUnbind(ConnectionInfo connectionInfo, String connectionId) {
        String gatewayId = connectionInfo.getGatewayId();
        if (StrUtil.isNotBlank(gatewayId)) {
            // 1. 检查网关是否还有其他活跃连接
            boolean hasOtherActiveConnections = checkGatewayHasOtherActiveConnections(gatewayId, connectionId);

            if (!hasOtherActiveConnections) {
                // 2. 发送设备离线消息
                R<DeviceInfo> deviceInfoResult = remoteDeviceService.getDeviceInfoByKey(gatewayId);
                if (deviceInfoResult == null || deviceInfoResult.getData() == null) {
                    log.error("[handleGatewayConnectionUnbind][无此设备,deviceKey:{}]", gatewayId);
                    return;
                }
                DeviceInfo deviceInfo = deviceInfoResult.getData();
                rocketMQPublisher.publishUplinkState(deviceInfo, null, ThingModelMessage.ID_TYPE_STATE_OFFLINE);

                // 3. 更新网关状态为离线
                log.info("[handleGatewayConnectionUnbind][连接断开，更新网关状态为离线 - 网关 ID: {}, 连接 ID: {}]", gatewayId,
                        connectionId);
                gatewayManageService.handleGatewayDisconnect(gatewayId, connectionId);

                gatewayConnections.remove(gatewayId);
                log.info("[handleGatewayConnectionUnbind][移除网关连接映射 - 网关 ID: {}, 连接 ID: {}]", gatewayId,
                        connectionId);
            } else {
                gatewayConnections.remove(gatewayId);
            }
        }
    }

    /**
     * 检查网关是否还有其他活跃连接
     */
    public boolean checkGatewayHasOtherActiveConnections(String gatewayId, String excludeConnectionId) {
        try {
            String currentGatewayConnectionId = gatewayConnections.get(gatewayId);

            if (StrUtil.isNotBlank(currentGatewayConnectionId)
                    && !currentGatewayConnectionId.equals(excludeConnectionId)) {
                ConnectionInfo currentConnection = connections.get(currentGatewayConnectionId);
                return ObjectUtil.isNotNull(currentConnection)
                        && currentConnection.getStatus() != ConnectionStatus.TIMEOUT;
            }
            return false;
        } catch (Exception e) {
            log.warn("[checkGatewayHasOtherActiveConnections][检查网关其他活跃连接时发生异常 - 网关 ID: {}, 排除连接 ID: {}]",
                    gatewayId, excludeConnectionId, e);
            return false;
        }
    }

    /**
     * 关闭所有连接
     */
    public void closeAllConnections() {
        log.info("[closeAllConnections][关闭所有 TCP 连接 - 总连接数: {}]", connections.size());

        connections.values().forEach(connectionInfo -> {
            try {
                if (ObjectUtil.isNotNull(connectionInfo.getSocket())) {
                    connectionInfo.getSocket().close();
                }
            } catch (Exception e) {
                log.error("[closeAllConnections][关闭连接时发生异常 - 连接 ID: {}]", connectionInfo.getConnectionId(), e);
            }
        });

        connections.clear();
        gatewayConnections.clear();

        log.info("[closeAllConnections][所有 TCP 连接已关闭]");
    }

    /**
     * 获取连接信息
     */
    public ConnectionInfo getConnection(String connectionId) {
        return StrUtil.isNotBlank(connectionId) ? connections.get(connectionId) : null;
    }

    /**
     * 通过网关 ID 获取连接
     */
    public ConnectionInfo getConnectionByGatewayId(String gatewayId) {
        if (StrUtil.isBlank(gatewayId)) {
            return null;
        }
        String connectionId = gatewayConnections.get(gatewayId);
        return StrUtil.isNotBlank(connectionId) ? connections.get(connectionId) : null;
    }

    /**
     * 绑定网关连接
     */
    public void bindGatewayConnection(String connectionId, String gatewayId) {
        if (StrUtil.isBlank(connectionId) || StrUtil.isBlank(gatewayId)) {
            return;
        }

        ConnectionInfo connectionInfo = connections.get(connectionId);
        if (ObjectUtil.isNotNull(connectionInfo)) {
            connectionInfo.setGatewayId(gatewayId);
            connectionInfo.setStatus(ConnectionStatus.AUTHENTICATED);
            gatewayConnections.put(gatewayId, connectionId);
        }
    }

    /**
     * 更新连接最后活跃时间（心跳更新）
     */
    public void updateLastActiveTime(String connectionId) {
        if (StrUtil.isNotBlank(connectionId)) {
            ConnectionInfo connectionInfo = connections.get(connectionId);
            if (ObjectUtil.isNotNull(connectionInfo)) {
                connectionInfo.setLastActiveTime(LocalDateTime.now());
            }
        }
    }

    /**
     * 获取当前连接数
     */
    public int getConnectionCount() {
        return connections.size();
    }

    /**
     * 检查网关是否在线
     */
    public boolean isGatewayOnline(String gatewayId) {
        return StrUtil.isNotBlank(gatewayId) && gatewayConnections.containsKey(gatewayId);
    }

    /**
     * 向指定连接发送消息
     */
    public boolean sendMessage(String connectionId, String message) {
        if (StrUtil.isBlank(connectionId) || StrUtil.isBlank(message)) {
            return false;
        }

        ConnectionInfo connectionInfo = connections.get(connectionId);
        if (ObjectUtil.isNotNull(connectionInfo) && ObjectUtil.isNotNull(connectionInfo.getSocket())) {
            try {
                connectionInfo.getSocket().write(message);
                return true;
            } catch (Exception e) {
                log.error("[sendMessage][向连接发送消息失败 - 连接 ID: {}]", connectionId, e);
                return false;
            }
        } else {
            return false;
        }
    }

    /**
     * 向指定网关发送消息
     */
    public boolean sendMessageToGateway(String gatewayId, String message) {
        if (StrUtil.isBlank(gatewayId)) {
            return false;
        }

        String connectionId = gatewayConnections.get(gatewayId);
        if (StrUtil.isNotBlank(connectionId)) {
            return sendMessage(connectionId, message);
        } else {
            return false;
        }
    }

    /**
     * 生成连接 ID
     */
    private String generateConnectionId() {
        return StrUtil.format("conn_{}_{}", connectionIdGenerator.incrementAndGet(), System.currentTimeMillis());
    }

    /**
     * 连接状态枚举
     */
    public enum ConnectionStatus {
        CONNECTED, // 已连接但未认证
        AUTHENTICATED, // 已认证
        TIMEOUT, // 连接超时但保留对应关系
        DISCONNECTED // 已断开
    }

    /**
     * 连接信息类
     */
    @Data
    public static class ConnectionInfo {
        private final String connectionId;
        private final NetSocket socket;
        private final String remoteAddress;
        private final LocalDateTime connectTime;
        private LocalDateTime lastActiveTime;
        private ConnectionStatus status;
        private String gatewayId;

        public ConnectionInfo(String connectionId, NetSocket socket, String remoteAddress,
                              LocalDateTime connectTime, ConnectionStatus status) {
            this.connectionId = connectionId;
            this.socket = socket;
            this.remoteAddress = remoteAddress;
            this.connectTime = connectTime;
            this.lastActiveTime = connectTime;
            this.status = status;
        }
    }
}