package com.fx.broker.tcp.protocol.processor;

import cn.hutool.json.JSONObject;
import com.fx.broker.tcp.domain.ProtocolMessage;
import com.fx.broker.tcp.enums.FunctionCodeEnum;
import com.fx.broker.tcp.enums.ReturnCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 功能码 3：网关与服务器时钟同步
 * 时钟同步处理器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class TimeSyncProcessor extends BaseCommandProcessor {

    private static final long TIME_DIFF_THRESHOLD = 300; // 时间差阈值5分钟

    @Override
    public Integer getFunctionCode() {
        return FunctionCodeEnum.TIME_SYNC.getCode();
    }

    @Override
    public String getProcessorName() {
        return "时钟同步处理器";
    }

    @Override
    protected ProtocolMessage doProcess(ProtocolMessage message) {
        // 1. 获取网关时间参数
        Long gatewayTime = message.getData().getLong("time");
        if (gatewayTime == null) {
            logProcessing(message, "[doProcess][网关时间参数无效]");
            return createErrorResponse(message, ReturnCodeEnum.MISSING_PARAMETER);
        }

        // 2. 计算时间差
        long serverTime = System.currentTimeMillis() / 1000;
        long timeDiff = Math.abs(serverTime - gatewayTime);

        logProcessing(message, String.format("[doProcess][时钟同步处理 - 网关时间: %d, 服务器时间: %d, 时间差: %d秒]",
                gatewayTime, serverTime, timeDiff));

        // 3. 检查时间差是否过大
        if (timeDiff > TIME_DIFF_THRESHOLD) {
            logProcessing(message, String.format("[doProcess][网关时间差过大 - 时间差: %d秒, 阈值: %d秒]",
                    timeDiff, TIME_DIFF_THRESHOLD));

            // 记录时间同步告警
            recordTimeSyncAlert(getGatewayId(message), gatewayTime, serverTime, timeDiff);
        }

        // 4. 构建响应数据
        JSONObject responseData = new JSONObject();
        responseData.set("time", serverTime);

        // 5. 记录时钟同步事件
        recordTimeSyncEvent(getGatewayId(message), gatewayTime, serverTime, timeDiff);

        // 6. 发送成功响应
        logProcessing(message, "[doProcess][时钟同步处理成功]");
        return createSuccessResponse(message, responseData);
    }

    /**
     * 记录时钟同步事件
     */
    private void recordTimeSyncEvent(String gatewayId, Long gatewayTime, Long serverTime, Long timeDiff) {
        try {
            JSONObject syncEvent = new JSONObject();
            syncEvent.set("gateway_id", gatewayId);
            syncEvent.set("gateway_time", gatewayTime);
            syncEvent.set("server_time", serverTime);
            syncEvent.set("time_difference", timeDiff);
            syncEvent.set("sync_time", System.currentTimeMillis() / 1000);
            syncEvent.set("event_type", "time_sync");

            // TODO: 发送时钟同步事件到MQ或记录到数据库
            log.debug("[recordTimeSyncEvent][记录时钟同步事件 - 事件: {}]", syncEvent);

        } catch (Exception e) {
            log.error("[recordTimeSyncEvent][记录时钟同步事件失败 - 网关ID: {}]", gatewayId, e);
        }
    }

    /**
     * 记录时间同步告警
     */
    private void recordTimeSyncAlert(String gatewayId, Long gatewayTime, Long serverTime, Long timeDiff) {
        try {
            JSONObject alertEvent = new JSONObject();
            alertEvent.set("gateway_id", gatewayId);
            alertEvent.set("gateway_time", gatewayTime);
            alertEvent.set("server_time", serverTime);
            alertEvent.set("time_difference", timeDiff);
            alertEvent.set("threshold", TIME_DIFF_THRESHOLD);
            alertEvent.set("alert_time", System.currentTimeMillis() / 1000);
            alertEvent.set("alert_type", "time_sync_large_diff");
            alertEvent.set("alert_level", timeDiff > 3600 ? "HIGH" : "MEDIUM"); // 超过1小时为高级别

            // TODO: 发送告警事件到MQ
            log.warn("[recordTimeSyncAlert][时间同步告警 - 告警: {}]", alertEvent);

        } catch (Exception e) {
            log.error("[recordTimeSyncAlert][记录时间同步告警失败 - 网关ID: {}]", gatewayId, e);
        }
    }

}