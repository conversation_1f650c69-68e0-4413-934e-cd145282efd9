package com.fx.broker.tcp.service.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.fx.broker.tcp.domain.ProtocolMessage;
import com.fx.broker.tcp.mq.publisher.RocketMQPublisher;
import com.fx.broker.tcp.service.BinaryDataCacheService;
import com.fx.broker.tcp.service.DataProcessService;
import com.fx.broker.tcp.service.GatewayManageService;
import com.fx.common.core.domain.R;
import com.fx.common.core.domain.ThingModelMessage;
import com.fx.link.api.RemoteDeviceService;
import com.fx.link.api.domain.device.model.DeviceInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 数据处理服务实现类
 * 负责网关上报数据的预处理、验证、格式转换和业务转发
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class DataProcessServiceImpl implements DataProcessService {

    private static final String DATA_TYPE_JSON = "json"; // 上报格式JSON
    private static final String DATA_TYPE_BINARY = "bin"; // 上报格式二进制

    @Resource
    private RocketMQPublisher rocketMQPublisher;

    @Resource
    private GatewayManageService gatewayManageService;

    @Resource
    private BinaryDataCacheService binaryDataCacheService;

    @Resource
    private RemoteDeviceService remoteDeviceService;

    @Override
    public ProtocolMessage processDataUpload(ProtocolMessage message) {
        try {
            log.debug("开始处理数据上报 - 网关ID: {}, 连接ID: {}",
                    message.getGatewayId(), message.getConnectionId());

            // 1. 验证消息基本格式
            if (!validateBasicFormat(message)) {
                return message.createErrorResponse(6); // 缺少参数
            }

            JSONObject data = message.getData();
            String format = data.getStr("format");

            // 2. 根据数据格式分别处理
            ProtocolMessage result;
            if (DATA_TYPE_JSON.equalsIgnoreCase(format)) {
                result = processJsonData(message);
            } else if (DATA_TYPE_BINARY.equalsIgnoreCase(format)) {
                result = processBinaryData(message);
            } else {
                log.warn("不支持的数据格式 - 格式: {}, 网关ID: {}", format, message.getGatewayId());
                return message.createErrorResponse(4); // 上报格式错误
            }

            return result;

        } catch (Exception e) {
            log.error("处理数据上报异常 - 网关ID: {}", message.getGatewayId(), e);
            return message.createErrorResponse(9); // 内存不足，无法处理消息
        }
    }

    /**
     * 处理JSON格式数据(485采集数据)
     *
     * @param message 消息
     * @return 处理结果
     */
    private ProtocolMessage processJsonData(ProtocolMessage message) {
        try {
            JSONObject data = message.getData();
            String key = data.getStr("key");
            String name = data.getStr("name");
            String time = data.getStr("time");
            JSONObject parameter = data.getJSONObject("parameter");

            log.debug("处理JSON数据 - 网关ID: {}, 设备名称: {}, Key: {}",
                    message.getGatewayId(), name, key);

            // 1. 验证必要字段
            if (StrUtil.isBlank(key) || StrUtil.isBlank(name) || parameter == null) {
                log.warn("JSON数据缺少必要字段 - 网关ID: {}", message.getGatewayId());
                return message.createErrorResponse(6); // 缺少参数
            }

            // 2. 数据预处理和标准化
            JSONObject processedData = preprocessJsonData(message.getGatewayId(), key, name, time, parameter);

            // 3. 发布到MQ
            // boolean published = publishToMQ(message.getGatewayId(), "json",
            // processedData);
            // if (!published) {
            // log.error("发布JSON数据到MQ失败 - 网关ID: {}", message.getGatewayId());
            // return message.createErrorResponse(9); // 内存不足
            // }

            log.debug("JSON数据处理成功 - 网关ID: {}, 设备名称: {}", message.getGatewayId(), name);
            return message.createSuccessResponse();

        } catch (Exception e) {
            log.error("处理JSON数据异常 - 网关ID: {}", message.getGatewayId(), e);
            return message.createErrorResponse(9);
        }
    }

    /**
     * 处理二进制格式数据(图片数据)
     * 1. 先缓存分包数据
     * 2. 等所有包上传完成后，调用 file 模块的上报接口
     * 3. 返回的 url 再进行 mq 的推送
     *
     * @param message 消息
     * @return 处理结果
     */
    private ProtocolMessage processBinaryData(ProtocolMessage message) {
        try {
            JSONObject data = message.getData();
            String key = data.getStr("key");
            String name = data.getStr("name");
            String time = data.getStr("time");
            Integer total = data.getInt("total"); // 当前设备图片总包数
            Integer num = data.getInt("num"); // 当前包序号
            String bin = data.getStr("bin"); // 包内容，采用base64编码

            log.debug("处理二进制数据 - 网关ID: {}, 设备名称: {}, 包序号: {}/{}",
                    message.getGatewayId(), name, num, total);

            // 1. 验证必要字段
            if (StrUtil.isBlank(key) || StrUtil.isBlank(name) ||
                    total == null || num == null || StrUtil.isBlank(bin)) {
                log.warn("二进制数据缺少必要字段 - 网关ID: {}", message.getGatewayId());
                return message.createErrorResponse(6); // 缺少参数
            }

            // 2. 验证包序号
            if (num <= 0 || total <= 0 || num > total) {
                log.warn("二进制数据包序号无效 - 网关ID: {}, 包序号: {}/{}",
                        message.getGatewayId(), num, total);
                return message.createErrorResponse(6); // 缺少参数
            }

            // 3. 验证Base64编码
            byte[] binaryData;
            try {
                binaryData = Base64.decode(bin);
            } catch (Exception e) {
                log.warn("Base64解码失败 - 网关ID: {}", message.getGatewayId(), e);
                return message.createErrorResponse(4); // 上报格式错误
            }

            // 4. 缓存二进制数据包
            boolean cached = binaryDataCacheService.cacheBinaryPacket(
                    message.getGatewayId(), key, name, total, num, binaryData, time);
            if (!cached) {
                log.error("缓存二进制数据包失败 - 网关ID: {}, 设备Key: {}, 包序号: {}/{}",
                        message.getGatewayId(), key, num, total);
                return message.createErrorResponse(9); // 内存不足
            }

            // 5. 检查是否所有包都已接收完成
            if (binaryDataCacheService.isAllPacketsReceived(message.getGatewayId(), key, total)) {
                log.info("所有包接收完成，开始组装并上传文件 - 网关ID: {}, 设备Key: {}, 总包数: {}",
                        message.getGatewayId(), key, total);

                // 6. 组装完整数据并上传到文件服务
                String fileUrl = binaryDataCacheService.assembleAndUploadFile(
                        message.getGatewayId(), key, name, total, time);

                if (StrUtil.isNotBlank(fileUrl)) {
                    // 7. 构建包含文件URL的数据并发布到MQ
                    JSONObject processedData = new JSONObject();
                    processedData.set("image_url", fileUrl);

                    boolean published = publishToMQ(key, processedData);
                    if (!published) {
                        log.error("发布二进制数据到MQ失败 - 网关ID: {}", message.getGatewayId());
                        return message.createErrorResponse(9); // 内存不足
                    }

                    log.info("二进制数据处理完成 - 网关ID: {}, 设备Key: {}, 文件URL: {}",
                            message.getGatewayId(), key, fileUrl);
                } else {
                    log.error("文件上传失败 - 网关ID: {}, 设备Key: {}", message.getGatewayId(), key);
                    return message.createErrorResponse(9); // 内存不足
                }
            } else {
                log.debug("包接收中 - 网关ID: {}, 设备Key: {}, 包序号: {}/{}",
                        message.getGatewayId(), key, num, total);
            }

            return message.createSuccessResponse();

        } catch (Exception e) {
            log.error("处理二进制数据异常 - 网关ID: {}", message.getGatewayId(), e);
            return message.createErrorResponse(9);
        }
    }

    /**
     * 验证消息基本格式
     *
     * @param message 消息
     * @return 是否有效
     */
    private boolean validateBasicFormat(ProtocolMessage message) {
        if (message == null || message.getData() == null) {
            return false;
        }

        JSONObject data = message.getData();
        String format = data.getStr("format");

        return StrUtil.isNotBlank(format) &&
                ("json".equalsIgnoreCase(format) || "bin".equalsIgnoreCase(format));
    }

    /**
     * 预处理JSON数据
     *
     * @param gatewayId 网关ID
     * @param key       设备Key
     * @param name      设备名称
     * @param time      时间
     * @param parameter 参数
     * @return 处理后的数据
     */
    private JSONObject preprocessJsonData(String gatewayId, String key, String name,
            String time, JSONObject parameter) {
        JSONObject processedData = new JSONObject();

        return processedData;
    }

    /**
     * 发布到MQ
     *
     * @param deviceKey     设备 key
     * @param processedData 处理后的数据
     * @return 是否成功
     */
    private boolean publishToMQ(String deviceKey, JSONObject processedData) {
        try {
            R<DeviceInfo> deviceInfoResult = remoteDeviceService.getDeviceInfoByKey(deviceKey);
            if (deviceInfoResult == null || deviceInfoResult.getData() == null) {
                log.error("无此设备,deviceKey:{}", deviceKey);
                return false;
            }
            DeviceInfo deviceInfo = deviceInfoResult.getData();
            // 发布设备状态消息
            rocketMQPublisher.publishUplinkState(deviceInfo, null, ThingModelMessage.ID_TYPE_STATE_ONLINE);
            // 发布设备数据消息
            return rocketMQPublisher.publishUplinkData(deviceInfo, processedData);
        } catch (Exception e) {
            log.error("");
            return false;
        }
    }

}