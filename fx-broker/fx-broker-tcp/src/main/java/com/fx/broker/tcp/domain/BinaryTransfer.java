package com.fx.broker.tcp.domain;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 二进制传输实体
 * 负责大文件的分包传输管理，支持配置包、固件包等二进制数据的传输
 * 
 * <AUTHOR>
 */
@Data
public class BinaryTransfer implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 传输ID
     */
    private String transferId;

    /**
     * 网关ID
     */
    private String gatewayId;

    /**
     * 数据类型
     */
    private DataType dataType;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 文件总大小（字节）
     */
    private Long totalSize;

    /**
     * 分包大小（字节）
     */
    private Integer packetSize;

    /**
     * 总包数
     */
    private Integer totalPackets;

    /**
     * 已传输包数
     */
    private Integer transferredPackets;

    /**
     * 传输状态
     */
    private TransferStatus status;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 开始传输时间
     */
    private LocalDateTime startTime;

    /**
     * 完成时间
     */
    private LocalDateTime completedTime;

    /**
     * 最后传输时间
     */
    private LocalDateTime lastTransferTime;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 文件MD5校验
     */
    private String fileMd5;

    /**
     * 已传输数据大小
     */
    private Long transferredSize;

    /**
     * 传输进度（百分比）
     */
    private Double progress;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 最大重试次数
     */
    private Integer maxRetryCount;

    /**
     * 传输超时时间（毫秒）
     */
    private Long timeout;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 分包传输状态记录（包序号 -> 是否成功）
     */
    private ConcurrentHashMap<Integer, Boolean> packetStatus;

    /**
     * 完整二进制数据
     */
    private byte[] completeData;

    /**
     * 构造函数
     */
    public BinaryTransfer() {
        this.packetStatus = new ConcurrentHashMap<>();
        this.status = TransferStatus.PENDING;
        this.transferredPackets = 0;
        this.transferredSize = 0L;
        this.progress = 0.0;
        this.retryCount = 0;
        this.maxRetryCount = 3;
        this.timeout = 300000L; // 默认5分钟超时
    }

    public BinaryTransfer(String gatewayId, DataType dataType, String fileName,
            byte[] fileData, Integer packetSize) {
        this();
        this.gatewayId = gatewayId;
        this.dataType = dataType;
        this.fileName = fileName;
        this.totalSize = (long) fileData.length;
        this.packetSize = packetSize;
        this.totalPackets = (int) Math.ceil((double) fileData.length / packetSize);
        this.fileMd5 = DigestUtil.md5Hex(fileData);
        this.transferId = generateTransferId();
    }

    /**
     * 数据类型枚举
     */
    public enum DataType {
        GATEWAY_FIRMWARE(1, "网关固件"),
        CAMERA_FIRMWARE(2, "摄像模块固件"),
        RS485_CONFIG(3, "RS485配置"),
        IMAGE_CONFIG(4, "图片采集配置"),
        CUSTOM_CONFIG(5, "自定义配置");

        private final int code;
        private final String description;

        DataType(int code, String description) {
            this.code = code;
            this.description = description;
        }

        public int getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static DataType fromCode(int code) {
            for (DataType type : values()) {
                if (type.code == code) {
                    return type;
                }
            }
            return CUSTOM_CONFIG;
        }
    }

    /**
     * 传输状态枚举
     */
    public enum TransferStatus {
        PENDING("待传输"),
        TRANSFERRING("传输中"),
        COMPLETED("传输完成"),
        FAILED("传输失败"),
        CANCELLED("已取消"),
        TIMEOUT("传输超时");

        private final String description;

        TransferStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }

        /**
         * 判断是否为终止状态
         * 
         * @return 是否为终止状态
         */
        public boolean isTerminal() {
            return this == COMPLETED || this == FAILED || this == CANCELLED || this == TIMEOUT;
        }

        /**
         * 判断是否为成功状态
         * 
         * @return 是否为成功状态
         */
        public boolean isSuccess() {
            return this == COMPLETED;
        }
    }

    // 业务方法

    /**
     * 开始传输
     */
    public void startTransfer() {
        this.status = TransferStatus.TRANSFERRING;
        this.startTime = LocalDateTime.now();
        this.lastTransferTime = LocalDateTime.now();
    }

    /**
     * 标记包传输成功
     * 
     * @param packetNumber 包序号
     * @param packetSize   包大小
     */
    public void markPacketSuccess(Integer packetNumber, Integer packetSize) {
        if (packetStatus.put(packetNumber, true) == null) {
            // 新成功的包
            this.transferredPackets++;
            this.transferredSize += packetSize;
            this.progress = (double) transferredPackets / totalPackets * 100;
            this.lastTransferTime = LocalDateTime.now();
        }

        // 检查是否全部完成
        if (transferredPackets.equals(totalPackets)) {
            markCompleted();
        }
    }

    /**
     * 标记包传输失败
     * 
     * @param packetNumber 包序号
     */
    public void markPacketFailed(Integer packetNumber) {
        packetStatus.put(packetNumber, false);
        this.lastTransferTime = LocalDateTime.now();
    }

    /**
     * 标记传输完成
     */
    public void markCompleted() {
        this.status = TransferStatus.COMPLETED;
        this.completedTime = LocalDateTime.now();
        this.progress = 100.0;
        this.errorMessage = null;
    }

    /**
     * 标记传输失败
     * 
     * @param errorMessage 错误信息
     */
    public void markFailed(String errorMessage) {
        this.status = TransferStatus.FAILED;
        this.errorMessage = errorMessage;
        this.completedTime = LocalDateTime.now();
    }

    /**
     * 标记传输超时
     */
    public void markTimeout() {
        this.status = TransferStatus.TIMEOUT;
        this.errorMessage = "传输超时";
        this.completedTime = LocalDateTime.now();
    }

    /**
     * 标记传输取消
     * 
     * @param reason 取消原因
     */
    public void markCancelled(String reason) {
        this.status = TransferStatus.CANCELLED;
        this.errorMessage = reason != null ? reason : "传输已被取消";
        this.completedTime = LocalDateTime.now();
    }

    /**
     * 获取失败的包列表
     * 
     * @return 失败包序号列表
     */
    public java.util.List<Integer> getFailedPackets() {
        return packetStatus.entrySet().stream()
                .filter(entry -> !entry.getValue())
                .map(java.util.Map.Entry::getKey)
                .sorted()
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 获取待传输的包列表
     * 
     * @return 待传输包序号列表
     */
    public java.util.List<Integer> getPendingPackets() {
        java.util.List<Integer> pendingPackets = new java.util.ArrayList<>();
        for (int i = 1; i <= totalPackets; i++) {
            if (!packetStatus.containsKey(i)) {
                pendingPackets.add(i);
            }
        }
        return pendingPackets;
    }

    /**
     * 获取下一个要传输的包序号
     * 
     * @return 包序号，如果全部完成返回null
     */
    public Integer getNextPacketNumber() {
        for (int i = 1; i <= totalPackets; i++) {
            if (!packetStatus.containsKey(i) || !packetStatus.get(i)) {
                return i;
            }
        }
        return null;
    }

    /**
     * 计算传输速度（字节/秒）
     * 
     * @return 传输速度
     */
    public Double getTransferSpeed() {
        if (startTime == null || transferredSize == 0) {
            return 0.0;
        }

        LocalDateTime now = LocalDateTime.now();
        long durationSeconds = java.time.Duration.between(startTime, now).getSeconds();

        if (durationSeconds == 0) {
            return 0.0;
        }

        return (double) transferredSize / durationSeconds;
    }

    /**
     * 计算预计剩余时间（秒）
     * 
     * @return 预计剩余时间
     */
    public Long getEstimatedRemainingTime() {
        Double speed = getTransferSpeed();
        if (speed == 0 || totalSize == null) {
            return null;
        }

        long remainingSize = totalSize - transferredSize;
        if (remainingSize <= 0) {
            return 0L;
        }

        return (long) (remainingSize / speed);
    }

    /**
     * 判断是否超时
     * 
     * @return 是否超时
     */
    public boolean isTimeout() {
        if (startTime == null || timeout == null) {
            return false;
        }

        LocalDateTime timeoutTime = startTime.plusNanos(timeout * 1_000_000);
        return LocalDateTime.now().isAfter(timeoutTime);
    }

    /**
     * 判断是否可以重试
     * 
     * @return 是否可以重试
     */
    public boolean canRetry() {
        return status == TransferStatus.FAILED &&
                retryCount != null && maxRetryCount != null &&
                retryCount < maxRetryCount;
    }

    /**
     * 增加重试次数
     */
    public void incrementRetryCount() {
        if (retryCount == null) {
            retryCount = 0;
        }
        retryCount++;
    }

    /**
     * 重置传输状态
     */
    public void resetTransfer() {
        this.status = TransferStatus.PENDING;
        this.startTime = null;
        this.completedTime = null;
        this.lastTransferTime = null;
        this.transferredPackets = 0;
        this.transferredSize = 0L;
        this.progress = 0.0;
        this.errorMessage = null;
        this.packetStatus.clear();
    }

    /**
     * 创建传输包数据
     * 
     * @param fileData     文件数据
     * @param packetNumber 包序号
     * @return 包数据
     */
    public BinaryPacket createPacket(byte[] fileData, Integer packetNumber) {
        if (packetNumber < 1 || packetNumber > totalPackets) {
            throw new IllegalArgumentException("包序号超出范围: " + packetNumber);
        }

        int startIndex = (packetNumber - 1) * packetSize;
        int endIndex = Math.min(startIndex + packetSize, fileData.length);

        byte[] packetData = new byte[endIndex - startIndex];
        System.arraycopy(fileData, startIndex, packetData, 0, packetData.length);

        return new BinaryPacket(
                transferId,
                gatewayId,
                dataType,
                packetNumber,
                totalPackets,
                startIndex,
                packetData);
    }

    /**
     * 生成传输ID
     * 
     * @return 传输ID
     */
    private String generateTransferId() {
        return "TRANSFER_" + System.currentTimeMillis() + "_" +
                Math.abs(gatewayId.hashCode() % 10000);
    }

    /**
     * 获取传输摘要信息
     * 
     * @return 摘要信息
     */
    public String getSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append("二进制传输[")
                .append("ID=").append(transferId)
                .append(", 网关=").append(gatewayId)
                .append(", 类型=").append(dataType != null ? dataType.getDescription() : "未知")
                .append(", 文件=").append(fileName)
                .append(", 状态=").append(status != null ? status.getDescription() : "未知")
                .append(", 进度=").append(String.format("%.1f", progress)).append("%")
                .append(", 包数=").append(transferredPackets).append("/").append(totalPackets)
                .append("]");
        return summary.toString();
    }

    @Override
    public String toString() {
        return getSummary();
    }

    /**
     * 二进制包数据
     */
    @Data
    public static class BinaryPacket implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 传输ID
         */
        private String transferId;

        /**
         * 网关ID
         */
        private String gatewayId;

        /**
         * 数据类型
         */
        private DataType dataType;

        /**
         * 包序号
         */
        private Integer packetNumber;

        /**
         * 总包数
         */
        private Integer totalPackets;

        /**
         * 偏移地址
         */
        private Integer offset;

        /**
         * 包数据
         */
        private byte[] data;

        /**
         * Base64编码的数据
         */
        private String encodedData;

        public BinaryPacket(String transferId, String gatewayId, DataType dataType,
                Integer packetNumber, Integer totalPackets, Integer offset, byte[] data) {
            this.transferId = transferId;
            this.gatewayId = gatewayId;
            this.dataType = dataType;
            this.packetNumber = packetNumber;
            this.totalPackets = totalPackets;
            this.offset = offset;
            this.data = data;
            this.encodedData = Base64.encode(data);
        }

        /**
         * 获取包大小
         * 
         * @return 包大小
         */
        public int getPacketSize() {
            return data != null ? data.length : 0;
        }

        /**
         * 验证包数据完整性
         * 
         * @return 是否完整
         */
        public boolean isValid() {
            return transferId != null && gatewayId != null && dataType != null &&
                    packetNumber != null && totalPackets != null && offset != null &&
                    data != null && data.length > 0 && StrUtil.isNotBlank(encodedData);
        }
    }
}