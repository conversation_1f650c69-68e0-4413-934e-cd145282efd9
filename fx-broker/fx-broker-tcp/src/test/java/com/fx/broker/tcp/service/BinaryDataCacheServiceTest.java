package com.fx.broker.tcp.service;

import cn.hutool.core.codec.Base64;
import cn.hutool.json.JSONObject;
import com.fx.broker.tcp.domain.ProtocolMessage;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.Random;

/**
 * 二进制数据缓存服务测试类
 *
 * <AUTHOR>
 */
@Slf4j
@SpringBootTest
public class BinaryDataCacheServiceTest {

    @Resource
    private BinaryDataCacheService binaryDataCacheService;

    @Resource
    private DataProcessService dataProcessService;

    @Test
    public void testBinaryDataProcessing() {
        String gatewayId = "000000000001";
        String deviceKey = "000000000001_001";
        String deviceName = "水表001";
        String time = String.valueOf(System.currentTimeMillis());
        int totalPackets = 19;

        log.info("开始测试二进制数据处理 - 网关ID: {}, 设备Key: {}, 总包数: {}",
                gatewayId, deviceKey, totalPackets);

        // 模拟发送多个数据包
        for (int i = 1; i <= totalPackets; i++) {
            // 1. 生成测试数据
            byte[] testData = generateTestData(1024); // 1KB测试数据
            String base64Data = Base64.encode(testData);

            // 2. 创建协议消息
            ProtocolMessage message = createProtocolMessage(gatewayId, deviceKey, deviceName,
                    time, totalPackets, i, base64Data);

            // 3. 处理数据
            ProtocolMessage result = dataProcessService.processDataUpload(message);

            log.info("包 {}/{} 处理结果 - 返回码: {}", i, totalPackets, result.getRetCode());

            // 4. 检查缓存状态
            boolean allReceived = binaryDataCacheService.isAllPacketsReceived(gatewayId, deviceKey, totalPackets);
            log.info("包 {}/{} 接收后，是否全部接收完成: {}", i, totalPackets, allReceived);

            // 模拟网络延迟
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }

        log.info("二进制数据处理测试完成");
    }

    @Test
    public void testBinaryDataCacheService() {
        String gatewayId = "TEST_GATEWAY_002";
        String deviceKey = "DEV_SENSOR_001";
        String deviceName = "测试传感器";
        String time = String.valueOf(System.currentTimeMillis());
        int totalPackets = 2;

        log.info("开始测试二进制数据缓存服务 - 网关ID: {}, 设备Key: {}", gatewayId, deviceKey);

        // 1. 缓存第一个包
        byte[] data1 = generateTestData(512);
        boolean cached1 = binaryDataCacheService.cacheBinaryPacket(gatewayId, deviceKey, deviceName,
                totalPackets, 1, data1, time);
        log.info("第一个包缓存结果: {}", cached1);

        // 2. 检查接收状态
        boolean allReceived1 = binaryDataCacheService.isAllPacketsReceived(gatewayId, deviceKey, totalPackets);
        log.info("第一个包后，是否全部接收完成: {}", allReceived1);

        // 3. 缓存第二个包
        byte[] data2 = generateTestData(512);
        boolean cached2 = binaryDataCacheService.cacheBinaryPacket(gatewayId, deviceKey, deviceName,
                totalPackets, 2, data2, time);
        log.info("第二个包缓存结果: {}", cached2);

        // 4. 再次检查接收状态
        boolean allReceived2 = binaryDataCacheService.isAllPacketsReceived(gatewayId, deviceKey, totalPackets);
        log.info("第二个包后，是否全部接收完成: {}", allReceived2);

        // 5. 尝试组装并上传文件
        if (allReceived2) {
            String fileUrl = binaryDataCacheService.assembleAndUploadFile(gatewayId, deviceKey,
                    deviceName, totalPackets, time);
            log.info("文件上传结果: {}", fileUrl);
        }

        log.info("二进制数据缓存服务测试完成");
    }

    /**
     * 生成测试数据
     *
     * @param size 数据大小
     * @return 测试数据
     */
    private byte[] generateTestData(int size) {
        byte[] data = new byte[size];
        new Random().nextBytes(data);
        return data;
    }

    /**
     * 创建协议消息
     *
     * @param gatewayId  网关ID
     * @param deviceKey  设备Key
     * @param deviceName 设备名称
     * @param time       时间戳
     * @param total      总包数
     * @param num        当前包序号
     * @param base64Data Base64编码的数据
     * @return 协议消息
     */
    private ProtocolMessage createProtocolMessage(String gatewayId, String deviceKey, String deviceName,
            String time, int total, int num, String base64Data) {
        ProtocolMessage message = new ProtocolMessage();
        message.setGatewayId(gatewayId);
        message.setConnectionId("TEST_CONN_001");
        message.setRetCode(0);

        JSONObject data = new JSONObject();
        data.set("format", "bin");
        data.set("key", deviceKey);
        data.set("name", deviceName);
        data.set("time", time);
        data.set("total", total);
        data.set("num", num);
        data.set("bin", base64Data);

        message.setData(data);
        return message;
    }
}