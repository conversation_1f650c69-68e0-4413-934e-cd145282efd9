package com.fx.broker.tcp.client;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

/**
 * TCP客户端测试类
 * 
 * <AUTHOR>
 */
@Slf4j
public class TcpClientTest {

    /**
     * 测试JSON消息构建
     */
    @Test
    public void testMessageBuilder() {
        log.info("测试消息构建功能");

        // 测试登录消息
        JSONObject loginMessage = buildLoginMessage("000000000001", "8910jqka", "1.0.0");
        log.info("登录消息: {}", JSONUtil.toJsonStr(loginMessage));

        // 测试心跳消息
        JSONObject heartbeatMessage = buildHeartbeatMessage();
        log.info("心跳消息: {}", JSONUtil.toJsonStr(heartbeatMessage));

        // 测试数据上报消息
        JSONObject dataMessage = buildDataReportMessage();
        log.info("数据上报消息: {}", JSONUtil.toJsonStr(dataMessage));

        // 测试时钟同步消息
        JSONObject syncMessage = buildTimeSyncMessage();
        log.info("时钟同步消息: {}", JSONUtil.toJsonStr(syncMessage));
    }

    /**
     * 构建登录消息
     */
    private JSONObject buildLoginMessage(String gatewayNum, String password, String version) {
        JSONObject message = new JSONObject();
        message.set("is_uplink", true);
        message.set("is_response", false);
        message.set("code", 1);

        JSONObject data = new JSONObject();
        data.set("gateway_num", gatewayNum);
        data.set("password", password);
        data.set("version", version);
        message.set("data", data);

        return message;
    }

    /**
     * 构建心跳消息
     */
    private JSONObject buildHeartbeatMessage() {
        JSONObject message = new JSONObject();
        message.set("is_uplink", true);
        message.set("is_response", false);
        message.set("code", 0);

        return message;
    }

    /**
     * 构建数据上报消息
     */
    private JSONObject buildDataReportMessage() {
        JSONObject message = new JSONObject();
        message.set("is_uplink", true);
        message.set("is_response", false);
        message.set("code", 2);

        JSONObject data = new JSONObject();
        data.set("format", "json");
        data.set("key", "device_001");
        data.set("name", "测试设备");
        data.set("time", "2024-01-15 10:30:00");

        JSONObject parameter = new JSONObject();
        parameter.set("temperature", 25.5);
        parameter.set("humidity", 60.2);
        parameter.set("pressure", 1013.25);
        data.set("parameter", parameter);

        message.set("data", data);

        return message;
    }

    /**
     * 构建时钟同步消息
     */
    private JSONObject buildTimeSyncMessage() {
        JSONObject message = new JSONObject();
        message.set("is_uplink", true);
        message.set("is_response", false);
        message.set("code", 3);

        JSONObject data = new JSONObject();
        data.set("time", System.currentTimeMillis() / 1000);
        message.set("data", data);

        return message;
    }

    /**
     * 测试响应消息解析
     */
    @Test
    public void testResponseParsing() {
        log.info("测试响应消息解析");

        // 测试登录成功响应
        String loginResponse = "{\"is_uplink\":false,\"is_response\":true,\"code\":1,\"ret_code\":0}";
        parseResponse(loginResponse, "登录响应");

        // 测试心跳响应
        String heartbeatResponse = "{\"is_uplink\":false,\"is_response\":true,\"code\":0,\"ret_code\":0}";
        parseResponse(heartbeatResponse, "心跳响应");

        // 测试时钟同步响应
        String syncResponse = "{\"is_uplink\":false,\"is_response\":true,\"code\":3,\"data\":{\"time\":1705296600},\"ret_code\":0}";
        parseResponse(syncResponse, "时钟同步响应");

        // 测试错误响应
        String errorResponse = "{\"is_uplink\":false,\"is_response\":true,\"code\":1,\"ret_code\":2}";
        parseResponse(errorResponse, "错误响应");
    }

    /**
     * 解析响应消息
     */
    private void parseResponse(String responseStr, String type) {
        try {
            JSONObject response = JSONUtil.parseObj(responseStr);
            Integer code = response.getInt("code");
            Boolean isResponse = response.getBool("is_response");
            Integer retCode = response.getInt("ret_code");

            log.info("{} - 功能码: {}, 是否响应: {}, 结果码: {}", type, code, isResponse, retCode);

            if (retCode != null && retCode == 0) {
                log.info("{} - 处理成功", type);
            } else {
                log.warn("{} - 处理失败，错误码: {}", type, retCode);
            }

        } catch (Exception e) {
            log.error("解析{}失败: {}", type, responseStr, e);
        }
    }

    /**
     * 测试指令处理
     */
    @Test
    public void testCommandHandling() {
        log.info("测试指令处理功能");

        // 测试重启指令
        String restartCommand = "{\"is_uplink\":false,\"is_response\":false,\"code\":4}";
        handleCommand(restartCommand, "重启指令");

        // 测试下发指令
        String instructionCommand = "{\"is_uplink\":false,\"is_response\":false,\"code\":5,\"data\":{\"task_id\":1001,\"content\":{\"action\":\"capture\",\"device\":\"camera_01\"}}}";
        handleCommand(instructionCommand, "下发指令");

        // 测试读取运行信息指令
        String runtimeCommand = "{\"is_uplink\":false,\"is_response\":false,\"code\":10}";
        handleCommand(runtimeCommand, "读取运行信息指令");
    }

    /**
     * 处理指令
     */
    private void handleCommand(String commandStr, String type) {
        try {
            JSONObject command = JSONUtil.parseObj(commandStr);
            Integer code = command.getInt("code");
            Boolean isResponse = command.getBool("is_response");

            log.info("收到{} - 功能码: {}, 是否响应: {}", type, code, isResponse);

            // 模拟处理
            ThreadUtil.sleep(100);

            // 构建响应
            JSONObject response = new JSONObject();
            response.set("is_uplink", true);
            response.set("is_response", true);
            response.set("code", code);
            response.set("ret_code", 0);

            log.info("发送{}响应: {}", type, JSONUtil.toJsonStr(response));

        } catch (Exception e) {
            log.error("处理{}失败: {}", type, commandStr, e);
        }
    }

    /**
     * 测试协议完整性
     */
    @Test
    public void testProtocolIntegrity() {
        log.info("测试协议完整性");

        // 测试所有功能码
        for (int code = 0; code <= 11; code++) {
            testFunctionCode(code);
        }
    }

    /**
     * 测试功能码
     */
    private void testFunctionCode(int code) {
        String functionName = getFunctionName(code);
        log.info("测试功能码 {} - {}", code, functionName);

        // 构建基本消息结构
        JSONObject message = new JSONObject();
        message.set("is_uplink", true);
        message.set("is_response", false);
        message.set("code", code);

        // 根据功能码添加特定数据
        switch (code) {
            case 1: // 登录
                JSONObject loginData = new JSONObject();
                loginData.set("gateway_num", "000000000001");
                loginData.set("password", "8910jqka");
                loginData.set("version", "1.0.0");
                message.set("data", loginData);
                break;
            case 2: // 上报数据
                JSONObject reportData = new JSONObject();
                reportData.set("format", "json");
                reportData.set("key", "test_device");
                reportData.set("name", "测试设备");
                reportData.set("time", "2024-01-15 10:30:00");
                message.set("data", reportData);
                break;
            case 3: // 时钟同步
                JSONObject syncData = new JSONObject();
                syncData.set("time", System.currentTimeMillis() / 1000);
                message.set("data", syncData);
                break;
            case 6: // 上报指令结果
                JSONObject resultData = new JSONObject();
                resultData.set("task_id", 1001);
                resultData.set("result", new JSONObject().set("status", "success"));
                message.set("data", resultData);
                break;
        }

        log.debug("功能码 {} 消息: {}", code, JSONUtil.toJsonStr(message));
    }

    /**
     * 获取功能码名称
     */
    private String getFunctionName(int code) {
        switch (code) {
            case 0:
                return "心跳";
            case 1:
                return "登录";
            case 2:
                return "上报数据";
            case 3:
                return "请求时钟同步";
            case 4:
                return "重启网关";
            case 5:
                return "下发指令";
            case 6:
                return "上报指令结果";
            case 7:
                return "下发二进制数据";
            case 8:
                return "读指令条数";
            case 9:
                return "读指令内容";
            case 10:
                return "读运行信息";
            case 11:
                return "网关升级";
            default:
                return "未知功能";
        }
    }
}