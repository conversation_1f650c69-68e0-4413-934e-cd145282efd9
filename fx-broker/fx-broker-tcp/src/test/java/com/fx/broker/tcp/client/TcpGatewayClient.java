package com.fx.broker.tcp.client;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.net.Socket;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Scanner;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * TCP网关客户端
 * 模拟网关与平台的交互，实现协议中定义的各种功能
 * 
 * <AUTHOR>
 */
@Slf4j
public class TcpGatewayClient {

    private static final String DEFAULT_HOST = "localhost";
    private static final int DEFAULT_PORT = 9999;
    private static final String DEFAULT_GATEWAY_NUM = "000000000001";
    private static final String DEFAULT_PASSWORD = "8910jqka";
    private static final String DEFAULT_VERSION = "1.0.0";

    private Socket socket;
    private PrintWriter writer;
    private final AtomicBoolean connected = new AtomicBoolean(false);
    private final AtomicBoolean loggedIn = new AtomicBoolean(false);
    private ScheduledExecutorService heartbeatExecutor;

    // 网关配置
    private String host = DEFAULT_HOST;
    private int port = DEFAULT_PORT;
    private String gatewayNum = DEFAULT_GATEWAY_NUM;
    private String password = DEFAULT_PASSWORD;
    private String version = DEFAULT_VERSION;

    public static void main(String[] args) {
        TcpGatewayClient client = new TcpGatewayClient();

        // 解析命令行参数
        if (args.length >= 2) {
            client.host = args[0];
            client.port = Integer.parseInt(args[1]);
        }
        if (args.length >= 3) {
            client.gatewayNum = args[2];
        }
        if (args.length >= 4) {
            client.password = args[3];
        }

        // 启动客户端
        client.start();
    }

    /**
     * 启动客户端
     */
    public void start() {
        log.info("启动TCP网关客户端...");
        log.info("服务器地址: {}:{}", host, port);
        log.info("网关编号: {}", gatewayNum);

        try {
            // 连接服务器
            connect();

            // 登录
            if (login()) {
                log.info("网关登录成功，开始心跳...");

                // 启动心跳
                startHeartbeat();

                // 启动控制台交互
                startConsoleInterface();
            } else {
                log.error("网关登录失败");
            }

        } catch (Exception e) {
            log.error("客户端运行异常", e);
        } finally {
            disconnect();
        }
    }

    /**
     * 连接服务器
     */
    private void connect() throws IOException {
        log.info("正在连接服务器 {}:{}...", host, port);

        socket = new Socket(host, port);
        log.debug("Socket连接建立成功");

        // 不使用BufferedReader，直接使用InputStream
        writer = new PrintWriter(new OutputStreamWriter(socket.getOutputStream(), StandardCharsets.UTF_8), true);
        log.debug("输入输出流初始化完成");

        connected.set(true);
        log.info("连接服务器成功，本地地址: {}, 远程地址: {}",
                socket.getLocalSocketAddress(), socket.getRemoteSocketAddress());

        // 启动消息接收线程
        startMessageReceiver();
    }

    /**
     * 断开连接
     */
    private void disconnect() {
        log.info("正在断开连接...");

        connected.set(false);
        loggedIn.set(false);

        // 停止心跳
        if (heartbeatExecutor != null) {
            heartbeatExecutor.shutdown();
        }

        // 关闭连接
        try {
            if (writer != null)
                writer.close();
            if (socket != null)
                socket.close();
        } catch (IOException e) {
            log.warn("关闭连接时发生异常", e);
        }

        log.info("连接已断开");
    }

    /**
     * 登录
     */
    private boolean login() {
        log.info("正在登录网关...");

        try {
            // 重置登录状态
            loggedIn.set(false);

            // 构建登录消息
            JSONObject loginMessage = new JSONObject();
            loginMessage.set("is_uplink", true);
            loginMessage.set("is_response", false);
            loginMessage.set("code", 1);

            JSONObject data = new JSONObject();
            data.set("gateway_num", gatewayNum);
            data.set("password", password);
            data.set("version", version);
            loginMessage.set("data", data);

            // 发送登录消息
            sendMessage(loginMessage);
            log.info("登录消息已发送，等待服务器响应...");

            // 等待登录响应，最多等待10秒
            int waitTime = 0;
            int maxWaitTime = 10000; // 10秒超时
            int checkInterval = 100; // 每100ms检查一次

            while (waitTime < maxWaitTime && !loggedIn.get()) {
                ThreadUtil.sleep(checkInterval);
                waitTime += checkInterval;

                // 每秒输出一次等待状态
                if (waitTime % 1000 == 0) {
                    log.debug("等待登录响应中... {}秒", waitTime / 1000);
                }
            }

            if (loggedIn.get()) {
                log.info("登录验证成功");
                return true;
            } else {
                log.error("登录超时，未收到服务器响应或登录失败");
                return false;
            }

        } catch (Exception e) {
            log.error("登录过程中发生异常", e);
            return false;
        }
    }

    /**
     * 启动心跳
     */
    private void startHeartbeat() {
        heartbeatExecutor = new ScheduledThreadPoolExecutor(1, r -> {
            Thread thread = new Thread(r, "HeartbeatThread");
            thread.setDaemon(true);
            return thread;
        });

        // 每5秒发送一次心跳
        heartbeatExecutor.scheduleWithFixedDelay(this::sendHeartbeat, 5, 5, TimeUnit.SECONDS);

        log.info("心跳已启动，间隔5秒");
    }

    /**
     * 发送心跳
     */
    private void sendHeartbeat() {
        if (!connected.get() || !loggedIn.get()) {
            return;
        }

        try {
            JSONObject heartbeatMessage = new JSONObject();
            heartbeatMessage.set("is_uplink", true);
            heartbeatMessage.set("is_response", false);
            heartbeatMessage.set("code", 0);

            sendMessage(heartbeatMessage);
            // 完全静默心跳发送，不输出任何日志

        } catch (Exception e) {
            log.error("发送心跳失败", e);
        }
    }

    /**
     * 手动发送心跳（用于测试）
     */
    private void sendManualHeartbeat() {
        if (!connected.get() || !loggedIn.get()) {
            System.out.println("连接未建立或未登录，无法发送心跳");
            return;
        }

        try {
            JSONObject heartbeatMessage = new JSONObject();
            heartbeatMessage.set("is_uplink", true);
            heartbeatMessage.set("is_response", false);
            heartbeatMessage.set("code", 0);

            log.info("手动发送心跳消息");
            sendMessage(heartbeatMessage);

        } catch (Exception e) {
            log.error("手动发送心跳失败", e);
        }
    }

    /**
     * 启动消息接收线程
     */
    private void startMessageReceiver() {
        Thread receiverThread = new Thread(() -> {
            log.info("消息接收线程已启动，开始监听服务器消息...");

            try {
                byte[] buffer = new byte[8192];
                StringBuilder messageBuffer = new StringBuilder();

                while (connected.get()) {
                    // log.debug("等待接收服务器消息..."); // 移除等待消息的日志

                    int bytesRead = socket.getInputStream().read(buffer);
                    if (bytesRead > 0) {
                        String data = new String(buffer, 0, bytesRead, StandardCharsets.UTF_8);
                        // log.debug("接收到原始数据: [{}]", data); // 恢复DEBUG级别

                        messageBuffer.append(data);

                        // 尝试解析完整的JSON消息
                        String bufferedData = messageBuffer.toString();
                        // log.debug("当前缓冲区内容: [{}]", bufferedData); // 缓冲区调试日志

                        int messageStart = 0;

                        while (messageStart < bufferedData.length()) {
                            // 查找JSON消息的开始和结束
                            int jsonStart = bufferedData.indexOf('{', messageStart);
                            if (jsonStart == -1) {
                                // 没有找到JSON开始，清空缓冲区（避免垃圾数据积累）
                                messageBuffer = new StringBuilder();
                                // log.debug("未找到JSON开始标记，清空缓冲区");
                                break;
                            }

                            // 查找匹配的JSON结束
                            int braceCount = 0;
                            int jsonEnd = -1;

                            for (int i = jsonStart; i < bufferedData.length(); i++) {
                                char c = bufferedData.charAt(i);
                                if (c == '{') {
                                    braceCount++;
                                } else if (c == '}') {
                                    braceCount--;
                                    if (braceCount == 0) {
                                        jsonEnd = i;
                                        break;
                                    }
                                }
                            }

                            if (jsonEnd != -1) {
                                // 找到完整的JSON消息
                                String completeMessage = bufferedData.substring(jsonStart, jsonEnd + 1);
                                // log.debug("解析到完整消息: [{}]", completeMessage); // 恢复DEBUG级别
                                handleReceivedMessage(completeMessage);
                                messageStart = jsonEnd + 1;
                            } else {
                                // JSON消息不完整，保留剩余数据等待更多数据
                                String remainingData = bufferedData.substring(jsonStart);
                                messageBuffer = new StringBuilder(remainingData);
                                // log.debug("JSON消息不完整，保留数据: [{}]", remainingData);
                                break;
                            }
                        }

                        // 如果所有消息都处理完了，清空缓冲区
                        if (messageStart >= bufferedData.length()) {
                            messageBuffer = new StringBuilder();
                            // log.debug("所有消息处理完毕，清空缓冲区");
                        }

                    } else if (bytesRead == -1) {
                        log.warn("服务器关闭了连接");
                        break;
                    }
                }
            } catch (IOException e) {
                if (connected.get()) {
                    log.error("接收消息时发生异常", e);
                } else {
                    log.debug("连接已断开，消息接收线程正常退出");
                }
            }

            log.info("消息接收线程已停止");
        }, "MessageReceiverThread");

        receiverThread.setDaemon(true);
        receiverThread.start();

        // 给接收线程一点时间启动
        ThreadUtil.sleep(100);
        log.info("消息接收线程启动完成");
    }

    /**
     * 处理接收到的消息
     */
    private void handleReceivedMessage(String messageStr) {
        try {
            // log.debug("开始解析消息: [{}]", messageStr); // 调试日志
            JSONObject message = JSONUtil.parseObj(messageStr);
            // log.debug("JSON解析后的对象: {}", message.toString()); // 调试日志

            Integer code = message.getInt("code");
            Boolean isResponse = message.getBool("is_response");
            Integer retCode = message.getInt("ret_code");

            // log.debug("解析出的字段 - code: {}, isResponse: {}, retCode: {}", code, isResponse,
            // retCode); // 调试日志

            if (isResponse != null && isResponse) {
                // 处理响应消息
                if (code != null && code == 1) {
                    // 登录响应
                    // log.debug("收到登录响应消息: {}", messageStr); // 恢复DEBUG级别
                    if (retCode != null && retCode == 0) {
                        if (!loggedIn.get()) {
                            // 只在首次登录成功时输出日志
                            loggedIn.set(true);
                            log.info("网关登录成功");
                        } else {
                            // 已经登录成功，但又收到登录响应，这是异常情况
                            log.error("重复收到登录成功响应，可能是服务器端问题: {}", messageStr);
                        }
                    } else {
                        loggedIn.set(false);
                        log.error("网关登录失败，错误码: {}", retCode);
                        String errorMsg = getErrorMessage(retCode);
                        log.error("错误详情: {}", errorMsg);
                    }
                } else if (code != null && code == 0) {
                    // 心跳响应 - 静默处理，不输出任何日志
                    // 心跳响应正常，不需要特殊处理
                } else if (code != null && code == 3) {
                    // 时钟同步响应
                    // log.info("收到时钟同步响应");
                    log.info("收到时钟同步响应: {}", messageStr);
                    JSONObject data = message.getJSONObject("data");
                    if (data != null) {
                        Long serverTime = data.getLong("time");
                        log.info("服务器时间: {}", serverTime);
                    }
                } else {
                    // 其他响应
                    log.info("收到功能码{}的响应，结果码: {}", code, retCode);
                }
            } else {
                // 处理下行指令
                log.info("收到下行指令: {}", messageStr);
                handleDownlinkCommand(message);
            }

        } catch (Exception e) {
            log.error("处理接收消息时发生异常: {}", messageStr, e);
        }
    }

    /**
     * 获取错误消息描述
     */
    private String getErrorMessage(Integer retCode) {
        if (retCode == null)
            return "未知错误";

        switch (retCode) {
            case 0:
                return "成功";
            case 1:
                return "不支持";
            case 2:
                return "网关编号不存在";
            case 3:
                return "密码错误";
            case 4:
                return "上报格式错误";
            case 5:
                return "设备key错误";
            case 6:
                return "缺少参数";
            case 7:
                return "总体积超出范围";
            case 8:
                return "当前包超出范围";
            case 9:
                return "内存不足";
            case 10:
                return "参数类型错误";
            case 11:
                return "不支持的二进制数据类型";
            case 12:
                return "base解码失败";
            case 13:
                return "禁止跨页";
            default:
                return "未知错误码: " + retCode;
        }
    }

    /**
     * 处理下行指令
     */
    private void handleDownlinkCommand(JSONObject message) {
        Integer code = message.getInt("code");

        if (code == null) {
            log.warn("收到无效指令，缺少功能码");
            return;
        }

        switch (code) {
            case 4:
                handleRestartCommand(message);
                break;
            case 5:
                handleDownlinkInstruction(message);
                break;
            case 7:
                handleBinaryDataCommand(message);
                break;
            case 8:
                handleReadTaskCountCommand(message);
                break;
            case 9:
                handleReadTaskContentCommand(message);
                break;
            case 10:
                handleReadRuntimeInfoCommand(message);
                break;
            case 11:
                handleUpgradeCommand(message);
                break;
            default:
                log.warn("收到不支持的指令，功能码: {}", code);
                sendErrorResponse(message, 1); // 不支持
        }
    }

    /**
     * 处理重启指令
     */
    private void handleRestartCommand(JSONObject message) {
        log.info("收到重启指令");

        // 发送响应
        sendSuccessResponse(message);

        // 模拟重启（实际应该重启网关）
        log.info("模拟网关重启...");
        ThreadUtil.sleep(2000);

        // 重新连接
        try {
            disconnect();
            ThreadUtil.sleep(3000);
            connect();

            if (login()) {
                log.info("重启后重新登录成功");
                startHeartbeat();
            } else {
                log.error("重启后重新登录失败");
            }
        } catch (Exception e) {
            log.error("重启后重连失败", e);
        }
    }

    /**
     * 处理下发指令
     */
    private void handleDownlinkInstruction(JSONObject message) {
        log.info("收到下发指令: {}", message);

        // 发送响应
        sendSuccessResponse(message);

        // 模拟处理指令
        JSONObject data = message.getJSONObject("data");
        if (data != null) {
            Integer taskId = data.getInt("task_id");
            JSONObject content = data.getJSONObject("content");

            log.info("处理指令 - 任务ID: {}, 内容: {}", taskId, content);

            // 模拟异步处理，然后上报结果
            ThreadUtil.execute(() -> {
                ThreadUtil.sleep(1000);
                reportInstructionResult(taskId, "处理成功");
            });
        }
    }

    /**
     * 上报指令结果
     */
    private void reportInstructionResult(Integer taskId, String result) {
        try {
            JSONObject message = new JSONObject();
            message.set("is_uplink", true);
            message.set("is_response", false);
            message.set("code", 6);

            JSONObject data = new JSONObject();
            data.set("task_id", taskId);

            JSONObject resultData = new JSONObject();
            resultData.set("status", "success");
            resultData.set("message", result);
            resultData.set("timestamp", System.currentTimeMillis() / 1000);
            data.set("result", resultData);

            message.set("data", data);

            sendMessage(message);
            log.info("上报指令结果 - 任务ID: {}", taskId);

        } catch (Exception e) {
            log.error("上报指令结果失败", e);
        }
    }

    /**
     * 处理二进制数据下发
     */
    private void handleBinaryDataCommand(JSONObject message) {
        log.info("收到二进制数据下发指令");

        JSONObject data = message.getJSONObject("data");
        if (data != null) {
            Integer dataType = data.getInt("data_type");
            Integer offset = data.getInt("offect"); // 注意：协议中是offect，不是offset
            String bin = data.getStr("bin");

            log.info("二进制数据 - 类型: {}, 偏移: {}, 数据长度: {}",
                    dataType, offset, bin != null ? bin.length() : 0);

            // 发送响应
            JSONObject response = createResponse(message, 0);
            JSONObject responseData = new JSONObject();
            responseData.set("data_type", dataType);
            responseData.set("offect", offset);
            response.set("data", responseData);

            sendMessage(response);
        }
    }

    /**
     * 处理读取指令条数
     */
    private void handleReadTaskCountCommand(JSONObject message) {
        log.info("收到读取指令条数请求");

        // 模拟返回指令数量
        JSONObject response = createResponse(message, 0);
        JSONObject data = new JSONObject();
        data.set("task_num", 2);
        data.set("task_id", new int[] { 1001, 1002 });
        response.set("data", data);

        sendMessage(response);
    }

    /**
     * 处理读取指令内容
     */
    private void handleReadTaskContentCommand(JSONObject message) {
        log.info("收到读取指令内容请求");

        JSONObject requestData = message.getJSONObject("data");
        if (requestData != null) {
            Integer taskId = requestData.getInt("task_id");

            // 模拟返回指令内容
            JSONObject response = createResponse(message, 0);
            JSONObject data = new JSONObject();
            data.set("task_id", taskId);

            JSONObject content = new JSONObject();
            content.set("action", "capture_image");
            content.set("device", "camera_01");
            content.set("quality", "high");
            data.set("content", content);

            response.set("data", data);
            sendMessage(response);
        }
    }

    /**
     * 处理读取运行信息
     */
    private void handleReadRuntimeInfoCommand(JSONObject message) {
        log.info("收到读取运行信息请求");

        // 模拟返回运行信息
        JSONObject response = createResponse(message, 0);
        JSONObject data = new JSONObject();

        long currentTime = System.currentTimeMillis() / 1000;
        data.set("start_time", currentTime - 86400); // 模拟运行1天

        // OTA信息
        JSONObject ota = new JSONObject();
        ota.set("issued_time", currentTime - 3600);
        ota.set("online_time", currentTime - 3500);
        ota.set("issued_people", "张三");
        data.set("ota", ota);

        // RS485信息
        JSONObject rs485 = new JSONObject();
        rs485.set("config_time", currentTime - 7200);
        rs485.set("online_time", currentTime - 7100);
        rs485.set("config_people", "李四");
        data.set("re485", rs485);

        // 图片配置信息
        JSONObject img = new JSONObject();
        img.set("config_time", currentTime - 1800);
        img.set("online_time", currentTime - 1700);
        img.set("config_people", "王五");
        data.set("img", img);

        // 任务信息
        JSONObject[] tasks = {
                createTaskInfo("uart collect", 0, 2, 20),
                createTaskInfo("image capture", 1, 3, 15),
                createTaskInfo("data upload", 0, 1, 25)
        };
        data.set("task", tasks);

        response.set("data", data);
        sendMessage(response);
    }

    /**
     * 创建任务信息
     */
    private JSONObject createTaskInfo(String name, int state, int priority, int stackMin) {
        JSONObject task = new JSONObject();
        task.set("name", name);
        task.set("state", state);
        task.set("priority", priority);
        task.set("stack_min", stackMin);
        return task;
    }

    /**
     * 处理网关升级指令
     */
    private void handleUpgradeCommand(JSONObject message) {
        log.info("收到网关升级指令");

        JSONObject data = message.getJSONObject("data");
        if (data != null) {
            Integer len = data.getInt("len");
            Integer crc16 = data.getInt("CRC16");
            String people = data.getStr("people");

            log.info("升级信息 - 长度: {}, CRC16: {}, 操作人: {}", len, crc16, people);

            // 模拟升级过程
            log.info("开始升级...");
            ThreadUtil.sleep(3000);
            log.info("升级完成");

            sendSuccessResponse(message);
        }
    }

    /**
     * 启动控制台交互
     */
    private void startConsoleInterface() {
        Scanner scanner = new Scanner(System.in);

        System.out.println("\n=== TCP网关客户端控制台 ===");
        System.out.println("输入命令（输入 'help' 查看帮助，'quit' 退出）:");

        while (connected.get()) {
            System.out.print("> ");
            String input = scanner.nextLine().trim();

            if (StrUtil.isEmpty(input)) {
                continue;
            }

            try {
                handleConsoleCommand(input);
            } catch (Exception e) {
                System.out.println("执行命令时发生错误: " + e.getMessage());
            }
        }

        scanner.close();
    }

    /**
     * 处理控制台命令
     */
    private void handleConsoleCommand(String input) {
        String[] parts = input.split("\\s+");
        String command = parts[0].toLowerCase();

        switch (command) {
            case "help":
                showHelp();
                break;
            case "quit":
            case "exit":
                connected.set(false);
                break;
            case "status":
                showStatus();
                break;
            case "heartbeat":
                sendManualHeartbeat();
                System.out.println("手动发送心跳");
                break;
            case "sync":
                requestTimeSync();
                break;
            case "report":
                if (parts.length > 1) {
                    reportData(parts[1]);
                } else {
                    reportData("json");
                }
                break;
            case "login":
                if (login()) {
                    System.out.println("重新登录成功");
                } else {
                    System.out.println("重新登录失败");
                }
                break;
            default:
                System.out.println("未知命令: " + command + "，输入 'help' 查看帮助");
        }
    }

    /**
     * 显示帮助信息
     */
    private void showHelp() {
        System.out.println("\n可用命令:");
        System.out.println("  help      - 显示此帮助信息");
        System.out.println("  quit/exit - 退出客户端");
        System.out.println("  status    - 显示连接状态");
        System.out.println("  heartbeat - 手动发送心跳");
        System.out.println("  sync      - 请求时钟同步");
        System.out.println("  report    - 上报数据 (report json/bin)");
        System.out.println("  login     - 重新登录");
        System.out.println();
    }

    /**
     * 显示状态信息
     */
    private void showStatus() {
        System.out.println("\n=== 客户端状态 ===");
        System.out.println("服务器地址: " + host + ":" + port);
        System.out.println("网关编号: " + gatewayNum);
        System.out.println("连接状态: " + (connected.get() ? "已连接" : "未连接"));
        System.out.println("登录状态: " + (loggedIn.get() ? "已登录" : "未登录"));
        System.out.println("当前时间: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        System.out.println();
    }

    /**
     * 请求时钟同步
     */
    private void requestTimeSync() {
        try {
            JSONObject message = new JSONObject();
            message.set("is_uplink", true);
            message.set("is_response", false);
            message.set("code", 3);

            JSONObject data = new JSONObject();
            data.set("time", System.currentTimeMillis() / 1000);
            message.set("data", data);

            sendMessage(message);
            System.out.println("已发送时钟同步请求");

        } catch (Exception e) {
            System.out.println("发送时钟同步请求失败: " + e.getMessage());
        }
    }

    /**
     * 上报数据
     */
    private void reportData(String format) {
        try {
            JSONObject message = new JSONObject();
            message.set("is_uplink", true);
            message.set("is_response", false);
            message.set("code", 2);

            JSONObject data = new JSONObject();
            data.set("format", format);
            data.set("key", "device_001");
            data.set("name", "测试设备");
            data.set("time", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

            if ("json".equals(format)) {
                // JSON格式数据
                JSONObject parameter = new JSONObject();
                parameter.set("temperature", 25.5);
                parameter.set("humidity", 60.2);
                parameter.set("pressure", 1013.25);
                data.set("parameter", parameter);
            } else if ("bin".equals(format)) {
                // 二进制格式数据
                data.set("total", 1);
                data.set("num", 1);
                data.set("bin", "VGVzdCBiaW5hcnkgZGF0YQ=="); // "Test binary data" 的 base64
            }

            message.set("data", data);

            sendMessage(message);
            System.out.println("已发送" + format + "格式数据上报");

        } catch (Exception e) {
            System.out.println("发送数据上报失败: " + e.getMessage());
        }
    }

    /**
     * 发送消息
     */
    private void sendMessage(JSONObject message) {
        if (!connected.get()) {
            log.warn("连接未建立，无法发送消息");
            return;
        }

        if (socket == null || socket.isClosed()) {
            log.warn("Socket连接已关闭，无法发送消息");
            return;
        }

        String messageStr = JSONUtil.toJsonStr(message);

        // 判断是否为心跳消息，如果是则不输出日志
        boolean isHeartbeat = message.getInt("code") != null && message.getInt("code") == 0;

        if (!isHeartbeat) {
            log.debug("准备发送消息: {}", messageStr);
        }

        writer.println(messageStr);
        writer.flush(); // 确保消息立即发送

        if (writer.checkError()) {
            log.error("发送消息时发生错误");
        } else if (!isHeartbeat) {
            log.debug("消息发送成功: {}", messageStr);
        }
    }

    /**
     * 发送成功响应
     */
    private void sendSuccessResponse(JSONObject originalMessage) {
        sendResponse(originalMessage, 0);
    }

    /**
     * 发送错误响应
     */
    private void sendErrorResponse(JSONObject originalMessage, int errorCode) {
        sendResponse(originalMessage, errorCode);
    }

    /**
     * 发送响应
     */
    private void sendResponse(JSONObject originalMessage, int retCode) {
        JSONObject response = createResponse(originalMessage, retCode);
        sendMessage(response);
    }

    /**
     * 创建响应消息
     */
    private JSONObject createResponse(JSONObject originalMessage, int retCode) {
        JSONObject response = new JSONObject();
        response.set("is_uplink", true);
        response.set("is_response", true);
        response.set("code", originalMessage.getInt("code"));
        response.set("ret_code", retCode);
        return response;
    }
}