<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fx.tdengine.mapper.WeatherMapper">

    <resultMap id="BaseResultMap" type="com.fx.tdengine.api.domain.Weather">
        <id column="ts" jdbcType="TIMESTAMP" property="ts"/>
        <result column="temperature" jdbcType="FLOAT" property="temperature"/>
        <result column="humidity" jdbcType="FLOAT" property="humidity"/>
    </resultMap>

    <select id="lastOne" resultType="java.util.Map">
        select last_row(*), location, groupid
        from fxlinks.weather
    </select>

    <update id="dropDB">
        drop
        database if exists fxlinks
    </update>

    <update id="createDB">
        create
        database if not exists fxlinks
    </update>

    <update id="createSuperTable">
        create table if not exists fxlinks.weather
        (
            ts
            timestamp,
            temperature
            float,
            humidity
            float,
            note
            binary
        (
            64
        )) tags
        (
            location nchar
        (
            64
        ), groupId int)
    </update>

    <update id="createTable" parameterType="com.fx.tdengine.api.domain.Weather">
        create table if not exists fxlinks.t#{groupId} using fx-links.weather tags
        (
            #{location},
            #{groupId}
        )
    </update>

    <select id="select" resultMap="BaseResultMap">
        select * from fxlinks.weather order by ts desc
        <if test="limit != null">
            limit #{limit,jdbcType=BIGINT}
        </if>
        <if test="offset != null">
            offset #{offset,jdbcType=BIGINT}
        </if>
    </select>

    <insert id="insert" parameterType="com.fx.tdengine.api.domain.Weather">
        insert into fxlinks.t#{groupId} (ts, temperature, humidity, note)
        values (#{ts}, ${temperature}, ${humidity}, #{note})
    </insert>

    <select id="getSubTables" resultType="String">
        select tbname
        from fxlinks.weather
    </select>

    <select id="count" resultType="int">
        select count(*)
        from fxlinks.weather
    </select>

    <resultMap id="avgResultSet" type="com.fx.tdengine.api.domain.Weather">
        <id column="ts" jdbcType="TIMESTAMP" property="ts"/>
        <result column="avg(temperature)" jdbcType="FLOAT" property="temperature"/>
        <result column="avg(humidity)" jdbcType="FLOAT" property="humidity"/>
    </resultMap>

    <select id="avg" resultMap="avgResultSet">
        select avg(temperature), avg(humidity)
        from fxlinks.weather interval(1m)
    </select>

</mapper>