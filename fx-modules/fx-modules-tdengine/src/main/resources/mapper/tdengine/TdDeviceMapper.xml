<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fx.tdengine.mapper.TdDeviceMapper">

    <insert id="saveData">
        insert into #{tableName}
        <foreach item="schemaField" collection="schemaFieldList" separator=","
                 open="(" close=")" index="">
            #{schemaField}
        </foreach>
        using #{superTableName}
        tags
        <foreach item="tagsValue" collection="tagsValueList" separator=","
                 open="(" close=")" index="">
            #{tagsValue}
        </foreach>
        values
        <foreach item="schemaValue" collection="schemaValueList" separator=","
                 open="(" close=")" index="">
            #{schemaValue}
        </foreach>
    </insert>

</mapper>
