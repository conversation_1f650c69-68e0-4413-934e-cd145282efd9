<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fx.tdengine.mapper.ThingModelMessageMapper">

    <insert id="saveThingModelMessage">
        insert into #{tdTableName} (time,mid,product_key,device_name,uid,type,identifier,code,data,report_time)
        USING thing_model_message
        TAGS (#{tags})
        values(#{time},#{mid},#{productKey},#{deviceName},#{uid},#{type},#{identifier},#{code},#{data},#{reportTime})
    </insert>

    <select id="queryRuleLogs" resultType="com.fx.common.core.domain.ThingModelMessage">
        select time,mid,product_key,device_name,type,identifier,code,data,report_time
        from thing_model_message
        <where>
            <if test="device_key != null">
                and device_key=#{device_key}
            </if>
            <if test="type != null">
                and `type`=#{type}
            </if>
            <if test="identifier != null">
                and identifier=#{identifier}
            </if>
        </where>
        order by time desc
    </select>

    <select id="getDeviceMessageStatsWithUid" resultType="com.fx.tdengine.dm.TimeData">
        SELECT TIMETRUNCATE(FIRST(time),1h) time,COUNT(*) as data
        FROM thing_model_message
        <where>
            <if test="start != null">
                and `time` &gt; #{start}
            </if>
            <if test="end != null">
                and `time` &lt; #{end}
            </if>
            <if test="uid != null">
                and uid=#{uid}
            </if>
        </where>
        INTERVAL(1h)
    </select>

    <!-- 权限控制 -->
    <select id="queryCount" resultType="java.lang.Long">
        select count(*) from thing_model_message
        <where>
            <if test="startTime != null">
                and `time` &gt; #{startTime}
            </if>
            <if test="endTime != null">
                and `time` &lt; #{endTime}
            </if>
            <if test="orgIds != null">
                and uid in
                <foreach collection="orgIds" item="item" index="index"
                         separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <!-- 权限控制 -->
    <select id="getDeviceMessage" resultType="com.fx.tdengine.api.domain.MessageCountVo" parameterType="com.fx.tdengine.api.domain.SelectDto">
        SELECT TIMETRUNCATE(FIRST(time),1h) time,COUNT(*) as data
        FROM thing_model_message tmm
        <where>
            <if test="startTime != null">
                and tmm.`time` &gt; #{startTime}
            </if>
            <if test="endTime != null">
                and tmm.`time` &lt; #{endTime}
            </if>
            <if test="orgIds != null">
                and uid in
                <foreach collection="orgIds" item="item" index="index"
                         separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        INTERVAL(1${type})
    </select>

</mapper>
