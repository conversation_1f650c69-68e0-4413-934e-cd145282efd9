<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fx.tdengine.mapper.IotDeviceMapper">

    <resultMap type="com.fx.tdengine.api.domain.DeviceDataVo" id="DeviceInfoResult">
        <result property="deviceId" column="device_id"/>
        <result property="lastTime" column="last_time"/>
    </resultMap>

    <select id="getDeviceHistoryData" resultType="com.fx.tdengine.dm.TimeData"
            parameterType="com.fx.tdengine.api.domain.visual.SelectVisualDto">
        SELECT time, #{fieldName} as data
        FROM #{dataBaseName}.#{tableName}
        WHERE time BETWEEN #{startTime}
            AND #{endTime}
          AND ${fieldName} IS NOT NULL
        ORDER BY time
                DESC
        LIMIT #{params.rows} offset #{params.page}
    </select>

    <select id="getDeviceHistoryCount" resultType="java.lang.Integer">
        SELECT count(time)
        FROM #{dataBaseName}.#{tableName}
        WHERE time BETWEEN #{startTime}
            AND #{endTime}
          AND ${fieldName} IS NOT NULL
    </select>

    <select id="getDeviceInfo" resultType="java.util.Map">
        select * from #{dataBaseName}.#{tableName}
        <where>
            time = #{lastTime} and device_id =#{deviceId}
        </where>
    </select>

    <select id="getDeviceData" resultMap="DeviceInfoResult">
        SELECT last(time) as last_time,device_id FROM #{dataBaseName}.#{tableName}
        <where>
            ${sql}
        </where>
        group by device_id
    </select>

    <select id="getDevicePropertyHistoryData" resultType="com.fx.tdengine.dm.TimeData">
        SELECT time, #{fieldName} as data
        FROM #{dataBaseName}.#{tableName}
        WHERE time BETWEEN #{startTime}
            AND #{endTime}
          AND ${fieldName} IS NOT NULL
        ORDER BY time
    </select>

    <select id="getDevicePropertiesHistoryData" resultType="java.util.Map">
        select *
        from #{dataBaseName}.#{tableName}
        WHERE time BETWEEN #{startTime}
                  AND #{endTime}
        ORDER BY time
    </select>

    <select id="getDevicePropertyLatestData" resultType="java.util.Map">
        select LAST_ROW(*)
        from #{dataBaseName}.#{tableName}
    </select>


</mapper>
