<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fx.tdengine.mapper.SystemInfoMapper">
    <insert id="saveSystemInfo">
        insert into sys_info (time,cpu_num,cpu_total,cpu_sys,cpu_used,cpu_wait,cpu_free,jvm_total,jvm_max,jvm_free,jvm_usage)
        values(NOW(),#{cpuNum},#{cpuTotal},#{cpuSys},#{cpuUsed},#{cpuWait},#{cpuFree},#{jvmTotal},#{jvmMax},#{jvmFree},#{jvmUsage})
    </insert>
    <select id="findSysInfo" resultType="com.fx.tdengine.api.domain.SysInfoVo" parameterType="com.fx.tdengine.api.domain.SelectDto">
         SELECT TIMETRUNCATE(FIRST(time),1h) time, cpu_num as cpuNum,cpu_total as cpuTotal,cpu_sys as cpuSys,cpu_used as cpuUsed,cpu_wait as cpuWait,cpu_free as cpuFree,jvm_total as jvmTotal,jvm_max as jvmMax,jvm_free as jvmFree,jvm_usage as jvmUsage
         FROM fxlinks.sys_info
         <where>
            <if test="startTime != null">
                and `time` &gt; #{startTime}
            </if>
            <if test="endTime != null">
                and `time` &lt; #{endTime}
            </if>
        </where>
        INTERVAL(1h)
    </select>
</mapper>
