package com.fx.tdengine.service.impl;


import com.fx.tdengine.api.domain.DeviceDataVo;
import com.fx.tdengine.api.domain.visual.SelectVisualDto;
import com.fx.tdengine.mapper.IotDeviceMapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.CollectionUtils;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest
public class TdTest {
    @Autowired
    private IotDeviceMapper iotDeviceMapper;

    @Test
    public void test() throws ParseException {
//        List<HashMap<String, Object>> hashMaps = new ArrayList<>();
//        SelectVisualDto selectVisualDto = new SelectVisualDto();
//        selectVisualDto.setDataBaseName("fxlinks");
//        selectVisualDto.setTableName("subdevice_jcxttmhrayz3k6q2");
//        selectVisualDto.setSql("device_id in ('pmmscRhjBGSNEd6T')");
//        List<DeviceDataVo> deviceData1 = iotDeviceMapper.getDeviceData(selectVisualDto);
//        if (!CollectionUtils.isEmpty(deviceData1)) {
//            for (DeviceDataVo selectVisuaVo : deviceData1) {
//                selectVisualDto.setDeviceId(selectVisuaVo.getDeviceId());
//                selectVisualDto.setLastTime(selectVisuaVo.getLastTime());
//                List<HashMap<String, Object>> deviceInfo = iotDeviceMapper.getDeviceInfo(selectVisualDto);
//                if (!CollectionUtils.isEmpty(deviceInfo)) {
//                    hashMaps.add(deviceInfo.get(0));
//                }
//            }
//
//        }


    }
}
