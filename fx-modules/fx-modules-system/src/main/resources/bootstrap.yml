# Tomcat
server:
  port: 19303

# Spring
spring: 
  application:
    # 应用名称
    name: fx-system
  profiles:
    # 环境配置
    active: dev
  main:
    allow-circular-references: true
    allow-bean-definition-overriding: true
---
#　开发环境
spring:
  cloud:
    nacos:
      username: nacos
      password: N68DYG2JtSwH7knh
      discovery:
        # 服务注册地址
        server-addr: 192.168.9.183:8848
        namespace: e5a60688-aa9b-43fd-a9ce-efa846147949 # 通用
        #        namespace: c6556a3c-29f3-4f69-b1c3-f4950a87e240 # 大同晋控
      config:
        # 配置中心地址
        server-addr: 192.168.9.183:8848
        namespace: e5a60688-aa9b-43fd-a9ce-efa846147949 # 通用
        #        namespace: c6556a3c-29f3-4f69-b1c3-f4950a87e240 # 大同晋控
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
  config:
    activate:
      on-profile: dev
---
#　测试环境
spring:
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: 10.1.1.28:8848
        namespace: 63cebd06-0b1c-4863-ad5d-4319291c3c8d
      config:
        # 配置中心地址
        server-addr: 10.1.1.28:8848
        namespace: 63cebd06-0b1c-4863-ad5d-4319291c3c8d
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
  config:
    activate:
      on-profile: test
---
# 生产环境
spring:
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: 10.1.1.107:8848
        namespace: 63cebd06-0b1c-4863-ad5d-4319291c3c8d
      config:
        # 配置中心地址
        server-addr: 10.1.1.107:8848
        namespace: 63cebd06-0b1c-4863-ad5d-4319291c3c8d
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
  config:
    activate:
      on-profile: prod

---
#　达梦环境
spring:
  cloud:
    nacos:
      username: nacos
      password: snJly7L3ZxoVILYz
      discovery:
        # 服务注册地址
        server-addr: 192.168.18.201:8848
        namespace: 2928e7d4-a4ce-4c04-9ee0-38aa54e4963f
      config:
        # 配置中心地址
        server-addr: 192.168.18.201:8848
        namespace: 2928e7d4-a4ce-4c04-9ee0-38aa54e4963f
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
  config:
    activate:
      on-profile: dm

---
#　湖北环境
spring:
  cloud:
    nacos:
      username: nacos
      password: snJly7L3ZxoVILYz
      discovery:
        # 服务注册地址
        server-addr: 172.17.208.68:8848
        namespace: 2928e7d4-a4ce-4c04-9ee0-38aa54e4963f
      config:
        # 配置中心地址
        server-addr: 172.17.208.68:8848
        namespace: 2928e7d4-a4ce-4c04-9ee0-38aa54e4963f
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
  config:
    activate:
      on-profile: hubei

---
#　湖北宜化
spring:
  cloud:
    nacos:
      username: nacos
      password: QTGktfk4j54EtwEP
      discovery:
        # 服务注册地址
        server-addr: 10.222.20.215:8848
        namespace: b349b43c-237d-4940-b7ad-e1253ceec1a1
      config:
        # 配置中心地址
        server-addr: 10.222.20.215:8848
        namespace: b349b43c-237d-4940-b7ad-e1253ceec1a1
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
  config:
    activate:
      on-profile: yihua