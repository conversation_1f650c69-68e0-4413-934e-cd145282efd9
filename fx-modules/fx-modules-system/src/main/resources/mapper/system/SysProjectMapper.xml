<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fx.system.mapper.SysProjectMapper">

	<resultMap type="com.fx.system.api.domain.SysProject" id="SysProjectResult">
		<id     property="projectId"     column="project_id"     />
		<result property="projectName"   column="project_name"   />
		<result property="deptId" column="dept_id"/>
		<result property="remark"     column="remark"   />
		<result property="createBy"   column="create_by"   />
		<result property="createTime" column="create_time" />
		<result property="updateBy"   column="update_by"   />
		<result property="updateTime" column="update_time" />
	</resultMap>
	
	<sql id="selectProjectVo">
		select d.project_id, d.project_name, d.dept_id, d.remark, d.del_flag, d.create_by, d.create_time
        from sys_project d
    </sql>

	<select id="selectProjectList" parameterType="com.fx.system.api.domain.SysProject" resultMap="SysProjectResult">
        <include refid="selectProjectVo"/>
        where d.del_flag = '0'
		<if test="projectId != null and projectId != 0">
			AND d.project_id = #{projectId}
		</if>
		<if test="projectName != null and projectName != ''">
			AND d.project_name like concat('%', #{projectName}, '%')
		</if>
		<!-- 数据范围过滤 -->
		${params.dataScope}
		order by d.create_time desc
    </select>

    <select id="selectProjectById" parameterType="Long" resultMap="SysProjectResult">
		<include refid="selectProjectVo"/>
		where project_id = #{projectId}
	</select>

	<select id="checkProjectNameUnique" resultMap="SysProjectResult">
	    <include refid="selectProjectVo"/>
		where project_name=#{projectName} and del_flag = '0' limit 1
	</select>


	<insert id="insertProject" parameterType="com.fx.system.api.domain.SysProject">
 		insert into sys_project(
 			<if test="projectId != null and projectId != 0">project_id,</if>
 			<if test="projectName != null and projectName != ''">project_name,</if>
			<if test="deptId != null and deptId != 0">dept_id,</if>
 			<if test="remark != null and remark != ''">remark,</if>
 			<if test="createBy != null and createBy != ''">create_by,</if>
 			create_time
 		)values(
 			<if test="projectId != null and projectId != 0">#{projectId},</if>
 			<if test="projectName != null and projectName != ''">#{projectName},</if>
			<if test="deptId != null and deptId != 0">#{deptId},</if>
 			<if test="remark != null and remark != ''">#{remark},</if>
 			<if test="createBy != null and createBy != ''">#{createBy},</if>
 			sysdate()
 		)
	</insert>

	<update id="updateProject" parameterType="com.fx.system.api.domain.SysProject">
 		update sys_project
 		<set>
 			<if test="projectName != null and projectName != ''">project_name = #{projectName},</if>
			<if test="deptId != null and deptId != 0">dept_id = #{deptId},</if>
 			<if test="remark != null and remark != ''">remark = #{remark},</if>
 			<if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
 			update_time = sysdate()
 		</set>
 		where project_id = #{projectId}
	</update>
	
	<delete id="deleteProjectById" parameterType="Long">
		update sys_project set del_flag = '2',update_time = sysdate() where project_id = #{projectId}
	</delete>

	<select id="selectProjectListByIds" resultMap="SysProjectResult">
		<include refid="selectProjectVo"/>
		where id in
		<foreach item="projectId" collection="ids" open="(" separator="," close=")">
			#{projectId}
		</foreach>
	</select>

</mapper>