package com.fx.system.domain;

import com.fx.common.core.annotation.Excel;
import com.fx.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.File;
import java.io.IOException;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.ArrayList;
import java.util.List;

/**
 * 岗位表 sys_post
 *
 * <AUTHOR>
 */
public class SysPost extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 岗位序号
     */
    @Excel(name = "岗位序号" , cellType = Excel.ColumnType.NUMERIC)
    private Long postId;

    /**
     * 岗位编码
     */
    @Excel(name = "岗位编码")
    private String postCode;

    /**
     * 岗位名称
     */
    @Excel(name = "岗位名称")
    private String postName;

    /**
     * 岗位排序
     */
    @Excel(name = "岗位排序")
    private String postSort;

    /**
     * 状态（0正常 1停用）
     */
    @Excel(name = "状态" , readConverterExp = "0=正常,1=停用")
    private String status;

    /**
     * 用户是否存在此岗位标识 默认不存在
     */
    private boolean flag = false;

    public static void main(String[] args) throws IOException {
        System.out.println("开始修改...");
        // 源文件
        String path = "E:\\fx-links";

//        String dirName = "fxlinks";
        String dirName = "mqttsnet";
        String newDirName = "fx";
        File folder = new File(path);
        List<String> ignoreDirs = new ArrayList<>();
        ignoreDirs.add(".idea");
        ignoreDirs.add("bin");
        ignoreDirs.add("doc");
        ignoreDirs.add("logs");
        ignoreDirs.add("sql");
        fileDelete(dirName, folder, ignoreDirs);
//        fileRename(path, dirName, newDirName, folder,ignoreDirs);
        System.out.println("结束修改...");

    }

    private static void fileRename(String path, String dirName, String newDirName, File folder, List<String> ignoreDirs) throws IOException {
        if (folder.exists()) {
            File[] files = folder.listFiles();
            if (files == null || files.length == 0) {
                System.out.println("文件夹是空的");
            } else {
                for (File file : files) {
                    String name = file.getName();
                    if (file.isDirectory()) {
                        if (ignoreDirs.contains(file.getName())) {
                            continue;
                        }
                        if (name.contains(dirName)) {
                            System.out.println("文件：" + name);
                            name = name.replace(dirName, newDirName);
                            String newPath = path + File.separator + name;
                            File file1 = new File(newPath);
                            file.renameTo(file1);
                        } else {
                            String absolutePath = file.getAbsolutePath();
                            fileRename(absolutePath, dirName, newDirName, file, ignoreDirs);
                        }
                    } else {
                        if (name.contains(".iml")) {
                            deleteFile(file);
                            continue;
                        }
                        if (name.contains(dirName)) {
                            name = name.replace(dirName, newDirName);
                            String newPath = file.getParent() + File.separator + name;
                            File file2 = new File(newPath);
                            file.renameTo(file2);
                        } else {
                            String captureDirName = captureName(dirName);
                            String captureNewDirName = captureName(newDirName);
                            if (name.contains(captureDirName)) {
                                name = name.replace(captureDirName, captureNewDirName);
                                String newPath = file.getParent() + File.separator + name;
                                File file2 = new File(newPath);
                                file.renameTo(file2);
                            }
                        }
                    }
                }
            }
        }
    }

    private static void fileDelete(String dirName, File folder, List<String> ignoreDirs) throws IOException {
        if (folder.exists()) {
            File[] files = folder.listFiles();
            if (files == null || files.length == 0) {
                System.out.println("文件夹是空的");
            } else {
                for (File file : files) {
                    if (file.isDirectory()) {
                        if (ignoreDirs.contains(file.getName())) {
                            continue;
                        }
                        String name = file.getName();
                        if (name.equals(dirName)) {
                            copy2Dir(file, folder);
                            deleteFile(file);
                        } else {
                            fileDelete(dirName, file, ignoreDirs);
                        }
                    } else {
                        if (file.getName().contains(".iml")) {
                            deleteFile(file);
                        }
                    }
                }
            }
        }
    }

    public static void copy2Dir(File file, File file1) throws IOException {
        if (!file1.exists()) {
            file1.mkdirs();
        }
        if (file.isDirectory()) {
            File[] files = file.listFiles();
            for (File file2 : files) {
                if (file2.isDirectory()) {
                    copy2Dir(file2, new File(file1.getPath(), file2.getName()));
                } else {
                    Files.copy(file2.toPath(), Paths.get(file1.getPath() + "\\" + file2.getName()));
                }
            }
        }
    }

    public static void deleteFile(File file) throws IOException {
        Files.walkFileTree(file.toPath(),
                new SimpleFileVisitor<Path>() {
                    // 先去遍历删除文件
                    @Override
                    public FileVisitResult visitFile(Path file,
                                                     BasicFileAttributes attrs) throws IOException {
                        Files.delete(file);
                        System.out.printf("文件被删除 : %s%n" , file);
                        return FileVisitResult.CONTINUE;
                    }

                    // 再去遍历删除目录
                    @Override
                    public FileVisitResult postVisitDirectory(Path dir,
                                                              IOException exc) throws IOException {
                        Files.delete(dir);
                        System.out.printf("文件夹被删除: %s%n" , dir);
                        return FileVisitResult.CONTINUE;
                    }

                }
        );
    }

    /**
     * 将字符串的首字母转大写
     *
     * @param str 需要转换的字符串
     * @return
     */
    private static String captureName(String str) {
        // 进行字母的ascii编码前移，效率要高于截取字符串进行转换的操作
        char[] cs = str.toCharArray();
        cs[0] -= 32;
        return String.valueOf(cs);
    }

    public Long getPostId() {
        return postId;
    }

    public void setPostId(Long postId) {
        this.postId = postId;
    }

    @NotBlank(message = "岗位编码不能为空")
    @Size(min = 0, max = 64, message = "岗位编码长度不能超过64个字符")
    public String getPostCode() {
        return postCode;
    }

    public void setPostCode(String postCode) {
        this.postCode = postCode;
    }

    @NotBlank(message = "岗位名称不能为空")
    @Size(min = 0, max = 50, message = "岗位名称长度不能超过50个字符")
    public String getPostName() {
        return postName;
    }

    public void setPostName(String postName) {
        this.postName = postName;
    }

    @NotBlank(message = "显示顺序不能为空")
    public String getPostSort() {
        return postSort;
    }

    public void setPostSort(String postSort) {
        this.postSort = postSort;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    //文件的拷贝

    public boolean isFlag() {
        return flag;
    }

    public void setFlag(boolean flag) {
        this.flag = flag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("postId" , getPostId())
                .append("postCode" , getPostCode())
                .append("postName" , getPostName())
                .append("postSort" , getPostSort())
                .append("status" , getStatus())
                .append("createBy" , getCreateBy())
                .append("createTime" , getCreateTime())
                .append("updateBy" , getUpdateBy())
                .append("updateTime" , getUpdateTime())
                .append("remark" , getRemark())
                .toString();
    }

}
