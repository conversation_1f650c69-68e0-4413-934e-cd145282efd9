package com.fx.system.controller;

import com.fx.common.core.constant.UserConstants;
import com.fx.common.core.domain.R;
import com.fx.common.core.utils.SecurityUtils;
import com.fx.common.core.web.controller.BaseController;
import com.fx.common.core.web.domain.AjaxResult;
import com.fx.common.core.web.page.TableDataInfo;
import com.fx.common.log.annotation.Log;
import com.fx.common.log.enums.BusinessType;
import com.fx.common.security.annotation.InnerAuth;
import com.fx.common.security.annotation.PreAuthorize;
import com.fx.system.api.domain.SysProject;
import com.fx.system.service.ISysProjectService;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 项目信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/project")
public class SysProjectController extends BaseController {

    @Resource
    private ISysProjectService projectService;

    /**
     * 获取项目列表
     */
    @PreAuthorize(hasPermi = "system:project:list")
    @GetMapping("/list")
    public TableDataInfo list(SysProject project) {
        startPage();
        List<SysProject> projects = projectService.selectProjectList(project);
        return getDataTable(projects);
    }

    /**
     * 根据编号获取详细信息
     */
    @PreAuthorize(hasPermi = "system:project:query")
    @GetMapping(value = "/{projectId}")
    public AjaxResult getInfo(@PathVariable Long projectId) {
        return AjaxResult.success(projectService.selectProjectById(projectId));
    }

    /**
     * 新增项目
     */
    @PreAuthorize(hasPermi = "system:project:add")
    @Log(title = "项目管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SysProject project) {
        project.setCreateBy(SecurityUtils.getUsername());
        if (UserConstants.NOT_UNIQUE.equals(projectService.checkProjectNameUnique(project))) {
            return AjaxResult.error("新增项目'" + project.getProjectName() + "'失败，项目名称已存在");
        }
        return toAjax(projectService.insertProject(project));
    }

    /**
     * 修改项目
     */
    @PreAuthorize(hasPermi = "system:project:edit")
    @Log(title = "项目管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SysProject project) {
        project.setUpdateBy(SecurityUtils.getUsername());
        if (UserConstants.NOT_UNIQUE.equals(projectService.checkProjectNameUnique(project))) {
            return AjaxResult.error("修改项目'" + project.getProjectName() + "'失败，项目名称已存在");
        }
        return toAjax(projectService.updateProject(project));
    }

    /**
     * 删除项目
     */
    @PreAuthorize(hasPermi = "system:project:remove")
    @Log(title = "项目管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{projectId}")
    public AjaxResult remove(@PathVariable Long projectId) {
        return toAjax(projectService.deleteProjectById(projectId));
    }

    @GetMapping("/list-all-simple")
    @ApiOperation("获取所有项目-下拉框")
    public AjaxResult listAll() {
        return AjaxResult.success(projectService.selectProjectList(new SysProject()));
    }

    // ================= 内部调用 ====================
    /**
     * 内部调用-获取所有项目
     */
    @GetMapping("/list-all")
    @InnerAuth
    @ApiOperation("获取所有项目-内部调用")
    public R<List<SysProject>> getProjectList() {
        return R.ok(projectService.selectProjectList(new SysProject()));
    }
}
