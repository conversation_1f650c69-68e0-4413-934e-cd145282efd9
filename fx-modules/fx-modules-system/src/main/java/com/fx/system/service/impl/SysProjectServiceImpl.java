package com.fx.system.service.impl;

import com.fx.common.core.constant.Constants;
import com.fx.common.core.constant.UserConstants;
import com.fx.common.core.domain.R;
import com.fx.common.core.exception.ServiceException;
import com.fx.common.core.utils.StringUtils;
import com.fx.common.datascope.annotation.DataScope;
import com.fx.common.security.service.TokenService;
import com.fx.link.api.RemoteDeviceService;
import com.fx.link.api.domain.device.entity.Device;
import com.fx.system.api.domain.SysProject;
import com.fx.system.api.model.LoginUser;
import com.fx.system.mapper.SysProjectMapper;
import com.fx.system.service.ISysProjectService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 项目管理 服务实现
 *
 * <AUTHOR>
 */
@Service
public class SysProjectServiceImpl implements ISysProjectService {

    @Resource
    private SysProjectMapper sysProjectMapper;
    @Resource
    private RemoteDeviceService remoteDeviceService;
    @Resource
    private TokenService tokenService;

    /**
     * 查询项目管理数据
     *
     * @param project 项目信息
     * @return 项目信息集合
     */
    @Override
    @DataScope(deptAlias = "d")
    public List<SysProject> selectProjectList(SysProject project) {
        return sysProjectMapper.selectProjectList(project);
    }


    /**
     * 根据项目ID查询信息
     *
     * @param projectId 项目ID
     * @return 项目信息
     */
    @Override
    public SysProject selectProjectById(Long projectId) {
        return sysProjectMapper.selectProjectById(projectId);
    }

    /**
     * 校验项目名称是否唯一
     *
     * @param project 项目信息
     * @return 结果
     */
    @Override
    public String checkProjectNameUnique(SysProject project) {
        long projectId = StringUtils.isNull(project.getProjectId()) ? -1L : project.getProjectId();
        SysProject info = sysProjectMapper.checkProjectNameUnique(project.getProjectName());
        if (StringUtils.isNotNull(info) && info.getProjectId() != projectId) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 新增保存项目信息
     *
     * @param project 项目信息
     * @return 结果
     */
    @Override
    public int insertProject(SysProject project) {
        // 插入部门
        LoginUser loginUser = tokenService.getLoginUser();
        project.setDeptId(loginUser.getSysUser().getDeptId());
        return sysProjectMapper.insertProject(project);
    }

    /**
     * 修改保存项目信息
     *
     * @param project 项目信息
     * @return 结果
     */
    @Override
    public int updateProject(SysProject project) {
        return sysProjectMapper.updateProject(project);
    }

    /**
     * 删除项目信息
     *
     * @param projectId 项目ID
     * @return 结果
     */
    @Override
    public int deleteProjectById(Long projectId) {
        R<List<Device>> listR = remoteDeviceService.selectDeviceListByProjectId(projectId);
        if (listR.getCode() == Constants.SUCCESS) {
            List<Device> deviceList = listR.getData();
            if (!deviceList.isEmpty()) {
                throw new ServiceException("该项目下存在设备，无法删除");
            }
        } else {
            throw new ServiceException("查询设备失败");
        }
        return sysProjectMapper.deleteProjectById(projectId);
    }

}
