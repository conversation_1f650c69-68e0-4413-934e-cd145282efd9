package com.fx.system.mapper;

import com.fx.system.api.domain.SysProject;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 项目管理 数据层
 *
 * <AUTHOR>
 */
public interface SysProjectMapper {

    /**
     * 查询项目管理数据
     *
     * @param project 项目信息
     * @return 项目信息集合
     */
    List<SysProject> selectProjectList(SysProject project);

    /**
     * 根据项目ID查询信息
     *
     * @param projectId 项目ID
     * @return 项目信息
     */
    SysProject selectProjectById(Long projectId);

    /**
     * 校验项目名称是否唯一
     *
     * @param projectName 项目名称
     * @return 结果
     */
    SysProject checkProjectNameUnique(@Param("projectName") String projectName);

    /**
     * 新增项目信息
     *
     * @param project 项目信息
     * @return 结果
     */
    int insertProject(SysProject project);

    /**
     * 修改项目信息
     *
     * @param project 项目信息
     * @return 结果
     */
    int updateProject(SysProject project);

    /**
     * 删除项目管理信息
     *
     * @param projectId 项目ID
     * @return 结果
     */
    int deleteProjectById(Long projectId);
    /**
     * 根据id列表查询项目信息
     *
     * @param ids 项目id列表
     * @return 产品模型集合
     */
    List<SysProject> selectProjectListByIds(@Param("ids") List<Long> ids);
}
