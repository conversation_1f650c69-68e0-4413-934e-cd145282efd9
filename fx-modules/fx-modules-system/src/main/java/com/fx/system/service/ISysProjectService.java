package com.fx.system.service;

import com.fx.system.api.domain.SysProject;

import java.util.List;

/**
 * 项目管理 服务层
 *
 * <AUTHOR>
 */
public interface ISysProjectService {
    /**
     * 查询项目管理数据
     *
     * @param project 项目信息
     * @return 项目信息集合
     */
    List<SysProject> selectProjectList(SysProject project);

    /**
     * 根据项目ID查询信息
     *
     * @param projectId 项目ID
     * @return 项目信息
     */
    SysProject selectProjectById(Long projectId);

    /**
     * 校验项目名称是否唯一
     *
     * @param project 项目信息
     * @return 结果
     */
    String checkProjectNameUnique(SysProject project);

    /**
     * 新增保存项目信息
     *
     * @param project 项目信息
     * @return 结果
     */
    int insertProject(SysProject project);

    /**
     * 修改保存项目信息
     *
     * @param project 项目信息
     * @return 结果
     */
    int updateProject(SysProject project);

    /**
     * 删除项目管理信息
     *
     * @param projectId 项目ID
     * @return 结果
     */
    int deleteProjectById(Long projectId);
}
