# spring配置
spring: 
  redis:
    database: 1
    host: *************
    port: 6379
    password: i4uBRP4TvZUdnA78
    timeout: 30000
    jedis:
      pool:
      # 连接池中的最大空闲连接
        max-idle: 20
      # 连接池中的最小空闲连接
        min-idle: 0
      # 连接池最大连接数（使用负值表示没有限制）
        max-active: -1
      # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://*************:3306/fx-links?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8
    username: root
    password: JdN8tZaYSwMpG%b=

# mybatis配置
mybatis:
    # 搜索指定包别名
    typeAliasesPackage: com.fx.job
    # 配置mapper的扫描，找到所有的mapper.xml映射文件
    mapperLocations: classpath:mapper/**/*.xml
# rocketmq地址
rocketmq:
  name-server: *************:9876
# 默认的消息组
  producer: 
    group: fxJob

# swagger配置
swagger:
  title: 定时任务接口文档
  description: 定时任务API接口文档
  version: "1.0.0"
  license: Powered By fx
  licenseUrl: https://doc.fx.com
  basePackage: com.fx.job.controller
