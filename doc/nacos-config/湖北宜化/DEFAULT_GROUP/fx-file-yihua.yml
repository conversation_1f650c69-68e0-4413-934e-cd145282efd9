# 本地文件上传    
file:
    domain: http://127.0.0.1:19300
    path: /Users/<USER>/data/upload/fxlinks/uploadPath
    prefix: /statics

# FastDFS配置
fdfs:
  domain: http://************
  soTimeout: 3000
  connectTimeout: 2000
  trackerList: ************:22122

# Minio配置
minio:
  url: http://*************:39181
  accessKey: ULBwxOasR3n1cMCZOulN
  secretKey: AthLuunZ2SCSEQvIaS0tSIeXVPO6RwIAOVD7BuH5
  bucketName: fxlinks

# swagger配置
swagger:
  title: 文件服务接口文档
  description: 文件服务API接口文档
  version: "1.0.0"
  license: Powered By fx
  licenseUrl: https://doc.fx.com
  basePackage: com.fx.file.controller