# spring配置
spring: 
  redis:
    database: 1
    host: *********
    port: 6379
    password: r9Sr37MNNDTUXEyG
    timeout: 30000
    jedis:
      pool:
      # 连接池中的最大空闲连接
        max-idle: 20
      # 连接池中的最小空闲连接
        min-idle: 0
      # 连接池最大连接数（使用负值表示没有限制）
        max-active: -1
      # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1
  datasource: 
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **************************************************************************************************************************************************
    username: root
    password: bWkJgCg46pNPbbQ4

# mybatis配置
mybatis:
    # 搜索指定包别名
    typeAliasesPackage: com.fx.gen.domain
    # 配置mapper的扫描，找到所有的mapper.xml映射文件
    mapperLocations: classpath:mapper/**/*.xml

# swagger配置
swagger:
  enabled: false
  title: 代码生成接口文档
  description: 代码生成API接口文档
  version: "2.0.0"
  license: Powered By fx
  licenseUrl: https://doc.fx.com
  basePackage: com.fx.gen.controller

# 代码生成
gen: 
  # 作者
  author: thinglinks
  # 默认生成包路径 system 需改成自己的模块名称 如 system monitor tool
  packageName: com.fx.link
  # 自动去除表前缀，默认是false
  autoRemovePre: false
  # 表前缀（生成类名不会包含表前缀，多个用逗号分隔）
  tablePrefix: fx_
