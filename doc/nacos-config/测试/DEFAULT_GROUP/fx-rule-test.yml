# spring配置
spring: 
  redis:
    database: 1
    host: *********
    port: 6379
    password: r9Sr37MNNDTUXEyG
    timeout: 30000
    jedis:
      pool:
      # 连接池中的最大空闲连接
        max-idle: 20
      # 连接池中的最小空闲连接
        min-idle: 0
      # 连接池最大连接数（使用负值表示没有限制）
        max-active: -1
      # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1
  datasource:
    druid:
      stat-view-servlet:
        enabled: true
        loginUsername: admin
        loginPassword: 123456
    dynamic:
      # 开启seata代理，开启后默认每个数据源都代理，如果某个不需要代理可单独关闭
      seata: false
      druid:
        initial-size: 10
        min-idle: 10
        maxActive: 50
        maxWait: 60000
        timeBetweenEvictionRunsMillis: 60000
        minEvictableIdleTimeMillis: 300000
        validationQuery: SELECT 1 FROM DUAL
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        poolPreparedStatements: true
        maxPoolPreparedStatementPerConnectionSize: 50
        filters: stat,slf4j
        connectionProperties: druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000
      datasource:
        # 主库数据源
        master:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: **************************************************************************************************************************************************
          username: root
          password: bWkJgCg46pNPbbQ4
          # 从库数据源
          # slave:
          # username: 
          # password: 
          # url: 
          # driver-class-name: 

# rocketmq地址
rocketmq:
  name-server: *********:9876
  # 默认的消息组
  producer:
    group: fxlinks-rule

# seata配置
seata:
  enabled: false
  # Seata 应用编号，默认为 ${spring.application.name}
  application-id: fxlinks-rule
  # Seata 事务组编号，用于 TC 集群名
  tx-service-group: fxlinks-rule-group
  # 关闭自动代理
  enable-auto-data-source-proxy: false
  # 服务配置项
  service:
    # 虚拟组和分组的映射
    vgroup-mapping:
      fx-link-group: default
  config:
    type: nacos
    nacos:
      serverAddr: 127.0.0.1:8848
      group: SEATA_GROUP
      namespace:
  registry:
    type: nacos
    nacos:
      application: seata-server
      server-addr: 127.0.0.1:8848
      namespace:

# mybatis配置
mybatis:
  # 搜索指定包别名
  typeAliasesPackage: com.fx.rule
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath:mapper/**/*.xml

# swagger配置
swagger:
  enabled: false
  title: 规则引擎接口文档
  description: 规则引擎API接口文档
  version: "2.0.0"
  license: Powered By fx
  licenseUrl: https://doc.fx.com
  basePackage: com.fx.rule.controller
