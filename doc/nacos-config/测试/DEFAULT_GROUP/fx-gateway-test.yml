# spring配置
spring: 
  redis:
    database: 1
    host: *********
    port: 6379
    password: r9Sr37MNNDTUXEyG
    timeout: 30000
    jedis:
      pool:
      # 连接池中的最大空闲连接
        max-idle: 20
      # 连接池中的最小空闲连接
        min-idle: 0
      # 连接池最大连接数（使用负值表示没有限制）
        max-active: -1
      # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1
  cloud:
    gateway:
      discovery:
        locator:
          lowerCaseServiceId: true
          enabled: true
      # 全局CORS跨域配置
      globalcors:
        cors-configurations:
          '[/**]':
            allowedOriginPatterns: "*"
            allowedMethods: "*"
            allowedHeaders: "*"
            allowCredentials: true
            maxAge: 3600
      routes:
        # 系统模块
        - id: fx-system
          uri: lb://fx-system
          predicates:
            - Path=/system/v2/api-docs,/system/**
          filters:
            - StripPrefix=1

        # 认证中心
        - id: fx-auth
          uri: lb://fx-auth
          predicates:
            - Path=/auth/v2/api-docs,/auth/**
          filters:
            # 验证码处理
            - CacheRequestFilter
            - ValidateCodeFilter
            - StripPrefix=1

        # 代码生成
        - id: fx-gen
          uri: lb://fx-gen
          predicates:
            - Path=/code/**,/code/v2/api-docs
          filters:
            - StripPrefix=1
            - RewritePath=/code/v2/api-docs, /v2/api-docs
        # 定时任务
        - id: fx-job
          uri: lb://fx-job
          predicates:
            - Path=/schedule/**,/schedule/v2/api-docs
          filters:
            - StripPrefix=1
            - RewritePath=/schedule/v2/api-docs, /v2/api-docs
        # 文件服务
        - id: fx-file
          uri: lb://fx-file
          predicates:
            - Path=/file/**,/file/v2/api-docs
          filters:
            - StripPrefix=1
            - RewritePath=/file/v2/api-docs, /v2/api-docs
        # TDengine服务
        - id: fx-tdengine
          uri: lb://fx-tdengine
          predicates:
            - Path=/tdengine/**,/tdengine/v2/api-docs
          filters:
            - StripPrefix=1
            - RewritePath=/tdengine/v2/api-docs, /v2/api-docs
        # Link服务
        - id: fx-link
          uri: lb://fx-link
          predicates:
            - Path=/link/**,/link/v2/api-docs
          filters:
            - StripPrefix=1
            - RewritePath=/link/v2/api-docs, /v2/api-docs
        # Broker服务
        - id: fx-broker
          uri: lb://fx-broker
          predicates:
            - Path=/broker/**,/broker/v2/api-docs
          filters:
            - StripPrefix=1
            - RemoveRequestHeader=Sec-WebSocket-Protocol
            - RewritePath=/broker/v2/api-docs, /v2/api-docs
        # ProtocolAnalysis服务
        - id: fx-protocolAnalysis
          uri: lb://fx-protocolAnalysis
          predicates:
            - Path=/protocolAnalysis/**,/protocolAnalysis/v2/api-docs
          filters:
            - StripPrefix=1
            - RewritePath=/protocolAnalysis/v2/api-docs, /v2/api-docs
        # Rule服务
        - id: fx-rule
          uri: lb://fx-rule
          predicates:
            - Path=/rule/**,/rule/v2/api-docs
          filters:
            - StripPrefix=1
            - RewritePath=/rule/v2/api-docs, /v2/api-docs
        # Rule-HTTP服务
        - id: fx-rule-http
          uri: lb://fx-rule-http
          predicates:
            - Path=/rule-http/**,/rule-http/v2/api-docs
          filters:
            - StripPrefix=1
            - RewritePath=/rule-http/v2/api-docs, /v2/api-docs
        # 监控服务
        - id: fx-monitor
          uri: lb://fx-monitor
          predicates:
            - Path=/monitor/**,/monitor/v2/api-docs
          filters:
            - StripPrefix=1
            - RewritePath=/monitor/v2/api-docs, /v2/api-docs
        # 采集服务
        - id: fx-collection
          uri: lb://fx-collection
          predicates:
            - Path=/collection/**,/collection/v2/api-docs
          filters:
            - StripPrefix=1
            - RewritePath=/collection/v2/api-docs, /v2/api-docs

# Knife4j配置
knife4j:
  gateway:
    enabled: true
    strategy: discover
    discover:
      enabled: true
      version: swagger2
      excluded-services:
        - fx-gateway
      service-config:
        fx-system:
          group-name: 系统管理
          order: 1
          context-path: /system
        fx-auth:
          group-name: 认证服务
          order: 2
          context-path: /auth
        fx-link:
          group-name: 设备管理
          order: 3
          context-path: /link
        fx-broker:
          group-name: 消息服务
          order: 4
          context-path: /broker
        fx-rule:
          group-name: 规则引擎
          order: 5
          context-path: /rule
        fx-rule-http:
          group-name: HTTP规则引擎
          order: 6
          context-path: /rule-http
        fx-tdengine:
          group-name: 时序数据库
          order: 7
          context-path: /tdengine
        fx-file:
          group-name: 文件服务
          order: 8
          context-path: /file
        fx-job:
          group-name: 定时任务
          order: 9
          context-path: /schedule
        fx-gen:
          group-name: 代码生成
          order: 10
          context-path: /code
        fx-protocolAnalysis:
          group-name: 协议解析
          order: 11
          context-path: /protocolAnalysis
        fx-monitor:
          group-name: 系统监控
          order: 12
          context-path: /monitor
        fx-collection:
          group-name: 数据采集
          order: 13
          context-path: /collection

# swagger配置
swagger:
  enabled: true

# 安全配置
security:
  # 验证码
  captcha:
    enabled: false
    type: char
  # 防止XSS攻击
  xss:
    enabled: true
    excludeUrls:
      - /system/notice
      - /link/converter
  # 不校验白名单
  ignore:
    whites:
      - /auth/logout
      - /auth/login
      - /auth/register
      - /auth/licenseUpload
      - /auth/findMacAddress
      - /doc.html
      - /webjars/**
      - /swagger-resources/**
      - /swagger-resources
      - /*/v2/api-docs
      - /v2/api-docs/**
      - /v2/api-docs-ext/**
      - /csrf
      # MQTT消息推送接口
      - /broker/publish/sendMessage
      # Broker WebSocket推送日志
      - /broker/websocket/logging
      # 客户端身份认证接口
      - /link/device/clientAuthentication
      - /link/tool/**
      - /link/ruleInfo/test
      - /link/open/**
      - /tdengine/openApi/**