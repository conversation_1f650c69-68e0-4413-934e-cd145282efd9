# Spring
spring:
  redis:
    database: 1
    host: ********8
    port: 6379
    password: r9Sr37MNNDTUXEyG
    timeout: 30000
    jedis:
      pool:
        max-idle: 20
        min-idle: 0
        max-active: -1
        max-wait: -1
  datasource:
    druid:
      stat-view-servlet:
        enabled: true
        loginUsername: admin
        loginPassword: 123456
    dynamic:
      seata: false
      druid:
        initial-size: 5
        min-idle: 5
        maxActive: 20
        maxWait: 60000
        timeBetweenEvictionRunsMillis: 60000
        minEvictableIdleTimeMillis: 300000
        validationQuery: SELECT 1 FROM DUAL
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        poolPreparedStatements: true
        maxPoolPreparedStatementPerConnectionSize: 20
        filters: stat,slf4j
        connectionProperties: druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000
      datasource:
        master:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: **************************************************************************************************************************************************
          username: root
          password: bWkJgCg46pNPbbQ4

# RocketMQ 配置
rocketmq:
  name-server: ********8:9876
  producer:
    group: fxlinks-tcp
    send-timeout: 3000
    retry-times: 3
  consumer:
    group: fxlinks-tcp-consumer

# seata配置
seata:
  enabled: false
  application-id: ${spring.application.name}
  tx-service-group: ${spring.application.name}-group
  enable-auto-data-source-proxy: false
  service:
    vgroup-mapping:
      fx-broker-tcp-group: default
  config:
    type: nacos
    nacos:
      serverAddr: 127.0.0.1:8848
      group: SEATA_GROUP
      namespace: 1e1aff6c-da73-43e2-9e5f-8e0b890189d9
  registry:
    type: nacos
    nacos:
      application: seata-server
      server-addr: 127.0.0.1:8848
      namespace: 1e1aff6c-da73-43e2-9e5f-8e0b890189d9

# TCP服务器配置
tcp-server:
  # 基础服务器配置
  host: 0.0.0.0
  port: 9999
  max-connections: 5000
  timeout: 60000                        # 统一超时时间（连接、读取、写入）
  buffer-size: 16384
  
  # SSL配置
  ssl-enabled: false
  ssl:
    cert-path: /etc/ssl/certs/server.crt
    key-path: /etc/ssl/private/server.key
  
  # Vert.x配置
  vertx:
    worker-pool-size: 20
    event-loop-size: 8
  
  # 心跳配置（被动模式）
  heartbeat:
    enabled: true                       # 是否启用心跳检测
    interval: 30000                     # 心跳检测间隔（毫秒）
    timeout: 120000                     # 心跳超时时间（毫秒）
    mark-as-timeout: true               # 是否将超时连接标记为超时状态而不是删除

# 监控端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
      base-path: /actuator
  endpoint:
    health:
      show-details: always
      show-components: always
    metrics:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true

# 指令服务配置
command:
  service:
    max-retry: 3
    timeout: 300000
    max-pending: 100
    cleanup-interval: 3600000

# 会话存储配置
session:
  redis:
    key-prefix: fxlinks:tcp:session
    gateway-prefix: fxlinks:tcp:gateway
    ttl: 1800      # 会话TTL时间（秒），默认30分钟

# 二进制数据缓存配置
binary:
  cache:
    ttl: 3600  # 缓存过期时间（秒），默认1小时
    key-prefix: "fxlinks:tcp:binary:"  # 缓存key前缀
  file:
    extension: "jpg"  # 文件扩展名                   

# 线程池配置
threadBus:
  pool:
    core-pool-size: 8
    max-pool-size: 16
    keep-alive-time: 60
    queue-capacity: 1000
    thread-name-prefix: fxlinksAsync-

# 日志配置
logging:
  level:
    com.fx.broker.tcp.controller: DEBUG
    com.fx.broker.tcp.server: DEBUG
    com.fx.broker.tcp.protocol: DEBUG
    org.springframework.web: INFO
  pattern:
    console: "%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(%5p) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n%wEx"

# API文档配置
swagger:
  enabled: false
  title: TCP服务器监控API
  description: TCP服务器监控和管理接口文档
  version: 2.0.0
  basePackage: com.fx.broker.tcp.controller
