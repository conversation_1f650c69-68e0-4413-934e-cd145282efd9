# 文件存储类型配置 (local/minio/fastdfs/s3)
file:
  storage:
    type: s3  # 可选值: local, minio, fastdfs, s3
  # 本地文件存储配置（当 type: local 时使用）
  local:
    domain: http://127.0.0.1:19300
    path: C:\\data\\code\\fx\\fx-links\\upload
    prefix: /statics
  # Minio 对象存储配置（当 type: minio 时使用）
  minio:
    url: http://127.0.0.1:9000
    accessKey: minioadmin
    secretKey: minioadmin
    bucketName: fxlinks
  # FastDFS 分布式文件系统配置（当 type: fastdfs 时使用）
  fastdfs:
    domain: http://************
    soTimeout: 3000
    connectTimeout: 2000
    trackerList: ************:22122
  # S3 兼容存储配置（当 type: s3 时使用）
  s3:
    endpoint: https://oss-cn-zhangjiakou.aliyuncs.com
    accessKeyId: LTAI5tSiWWXVzXbZfQuxJWuH
    secretAccessKey: ******************************
    bucketName: fx-links
    region: cn-zhangjiakou
    accessUrl: https://fx-links.oss-cn-zhangjiakou.aliyuncs.com
    forcePathStyle: false  # 阿里云 OSS 必须使用 Virtual Hosted Style

# swagger配置
swagger:
  title: 文件服务接口文档
  description: 文件服务API接口文档
  version: "1.0.0"
  license: Powered By fx
  licenseUrl: https://doc.fx.com
  basePackage: com.fx.file.controller