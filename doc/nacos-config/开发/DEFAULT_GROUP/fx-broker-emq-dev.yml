# spring配置
spring: 
  redis:
    database: 1
    host: *************
    port: 6379
    # password: jhkdjhkjdhsIUTYURTU_z7Et5C
    timeout: 30000
    jedis:
      pool:
      # 连接池中的最大空闲连接
        max-idle: 20
      # 连接池中的最小空闲连接
        min-idle: 0
      # 连接池最大连接数（使用负值表示没有限制）
        max-active: -1
      # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1
  # redis:
  #   cluster:
  #     nodes:
  #       - *********:6379
  #       - *********:6380
  #       - *********:6379
  #       - *********:6380
  #       - *********:6379
  #       - *********:6380
  #     max-redirects: 3
  #   password: Fxck872161
  #   timeout: 30000
  #   jedis:
  #     pool:
  #       max-idle: 20
  #       min-idle: 0
  #       max-active: -1
  #       max-wait: -1
  datasource:
    druid:
      stat-view-servlet:
        enabled: true
        loginUsername: admin
        loginPassword: 123456
    dynamic:
      # 开启seata代理，开启后默认每个数据源都代理，如果某个不需要代理可单独关闭
      seata: false
      druid:
        initial-size: 5
        min-idle: 5
        maxActive: 20
        maxWait: 60000
        timeBetweenEvictionRunsMillis: 60000
        minEvictableIdleTimeMillis: 300000
        validationQuery: SELECT 1 FROM DUAL
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        poolPreparedStatements: true
        maxPoolPreparedStatementPerConnectionSize: 20
        filters: stat,slf4j
        connectionProperties: druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000
      datasource:
        # 主库数据源
        master:
          driver-class-name: com.mysql.cj.jdbc.Driver
          jdbc-url: jdbc:mysql://*************:3306/fx-links?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=false&serverTimezone=GMT%2B8
          username: root
          password: fengxing
  emq:
    # 账号
    username: fengxing     
    # 密码                
    password: fx@123456
    hostUrl: tcp://*************:1883
    # hostUrl: tcp://chaojiniu.top:1883
    # hostUrl: tcp://************:1883
    # 客户端Id，不能相同，采用随机数 ${random.value}
    client-id: ${random.int}    
    # 默认主题               
    default-topic: test     
    # 保持连接                   
    keepalive: 60   
    # 清除会话(设置为false,断开连接，重连后使用原来的会话 保留订阅的主题，能接收离线期间的消息)                           
    clearSession: true                        


# rocketmq地址
rocketmq:
  name-server: *************:9876
  # 默认的消息组
  producer:
    group: fxlinks-emq

# seata配置
seata:
  # 默认关闭，如需启用spring.datasource.dynami.seata需要同时开启
  enabled: false
  # Seata 应用编号，默认为 ${spring.application.name}
  application-id: ${spring.application.name}
  # Seata 事务组编号，用于 TC 集群名
  tx-service-group: ${spring.application.name}-group
  # 关闭自动代理
  enable-auto-data-source-proxy: false
  # 服务配置项
  service:
    # 虚拟组和分组的映射
    vgroup-mapping:
      fx-broker-group: default
  config:
    type: nacos
    nacos:
      serverAddr: 127.0.0.1:8848
      group: SEATA_GROUP
      namespace: 1e1aff6c-da73-43e2-9e5f-8e0b890189d9
  registry:
    type: nacos
    nacos:
      application: seata-server
      server-addr: 127.0.0.1:8848
      namespace: 1e1aff6c-da73-43e2-9e5f-8e0b890189d9
      
# 线程池总线资源配置
threadBus:
  pool:
    #核心线程数（默认线程数）
    core-pool-size: 8
    #最大线程数
    max-pool-size: 16
    #允许线程空闲时间（单位：默认为秒）
    keep-alive-time: 60
    #缓冲队列大小
    queue-capacity: 1000
    #线程池名前缀
    thread-name-prefix: fxlinksAsync-

# swagger配置
swagger:
  enabled: true
  title: EMQ消息服务接口文档
  description: EMQ消息服务API接口文档
  version: "2.0.0"
  license: Powered By fx
  licenseUrl: https://doc.fx.com
  basePackage: com.fx.broker.emq.controller
