# spring配置
spring: 
  redis:
    database: 1
    host: *************
    port: 6379
    # password: jhkdjhkjdhsIUTYURTU_z7Et5C
    timeout: 30000
    jedis:
      pool:
      # 连接池中的最大空闲连接
        max-idle: 20
      # 连接池中的最小空闲连接
        min-idle: 0
      # 连接池最大连接数（使用负值表示没有限制）
        max-active: -1
      # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1
  # redis:
  #   cluster:
  #     nodes:
  #       - *********:6379
  #       - *********:6380
  #       - *********:6379
  #       - *********:6380
  #       - *********:6379
  #       - *********:6380
  #     max-redirects: 3
  #   password: Fxck872161
  #   timeout: 30000
  #   jedis:
  #     pool:
  #       max-idle: 20
  #       min-idle: 0
  #       max-active: -1
  #       max-wait: -1\
  
# swagger配置
swagger:
  enabled: true
  title: Auth模块接口文档
  description: 认证授权中心API接口文档
  version: "2.0.0"
  license: Powered By fx
  licenseUrl: https://doc.fx.com
  basePackage: com.fx.auth.controller