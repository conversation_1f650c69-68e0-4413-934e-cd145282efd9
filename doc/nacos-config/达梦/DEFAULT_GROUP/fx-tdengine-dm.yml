spring: 
  redis:
    database: 1
    host: *************
    port: 6379
    password: Z4RaPTOcUfo2ON5b
    timeout: 30000
    jedis:
      pool:
      # 连接池中的最大空闲连接
        max-idle: 20
      # 连接池中的最小空闲连接
        min-idle: 0
      # 连接池最大连接数（使用负值表示没有限制）
        max-active: -1
      # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1
  datasource:
    druid:
      stat-view-servlet:
        enabled: true
        loginUsername: admin
        loginPassword: 123456
    dynamic:
      # 开启seata代理，开启后默认每个数据源都代理，如果某个不需要代理可单独关闭
      seata: false
      druid:
        initial-size: 50
        min-idle: 50
        maxActive: 2000
        maxWait: 60000
        timeBetweenEvictionRunsMillis: 60000
        minEvictableIdleTimeMillis: 300000
        validationQuery: SELECT SERVER_STATUS();
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        poolPreparedStatements: true
        maxPoolPreparedStatementPerConnectionSize: 100
        filters: stat,slf4j
        connectionProperties: druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000
      datasource:
        # 主库数据源
        master:
          driver-class-name: com.taosdata.jdbc.rs.RestfulDriver
          url: jdbc:TAOS-RS://*************:6041/${spring.datasource.dynamic.datasource.master.dbName}?user=${spring.datasource.dynamic.datasource.master.username}&password=${spring.datasource.dynamic.datasource.master.password}&timezone=GMT%2b8
          dbName: fxlinks
          username: root
          password: I7550FP7jwkr0mnc
        # 从库数据源
        # slave:
          # username: 
          # password: 
          # url: 
          # driver-class-name: 
  aop: 
    auto: true
    proxy-target-class: true
mybatis: 
  mapper-locations: classpath:mapper/**/*.xml

# seata配置
seata:
  enabled: false
  # Seata 应用编号，默认为 ${spring.application.name}
  application-id: fx-tdengine
  # Seata 事务组编号，用于 TC 集群名
  tx-service-group: fx-tdengine-group
  # 关闭自动代理
  enable-auto-data-source-proxy: false
  # 服务配置项
  service:
    # 虚拟组和分组的映射
    vgroup-mapping:
      fx-tdengine-group: default
  config:
    type: nacos
    nacos:
      serverAddr: *************:8848
      group: SEATA_GROUP
      namespace:
  registry:
    type: nacos
    nacos:
      application: seata-server
      server-addr: *************:8848
      namespace:

# rocketmq地址
rocketmq: 
  name-server: *************:9876
# 默认的消息组
  producer: 
    group: fxlinks

    
logging:  
  level: 
    com: 
      taosdata: 
        jdbc: 
          springbootdemo: 
            dao: debug