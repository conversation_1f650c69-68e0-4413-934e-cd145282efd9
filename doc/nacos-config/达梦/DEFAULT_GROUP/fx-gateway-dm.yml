spring:
  redis:
    database: 1
    host: *************
    port: 6379
    password: Z4RaPTOcUfo2ON5b
    timeout: 30000
    jedis:
      pool:
      # 连接池中的最大空闲连接
        max-idle: 20
      # 连接池中的最小空闲连接
        min-idle: 0
      # 连接池最大连接数（使用负值表示没有限制）
        max-active: -1
      # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1
  cloud:
    gateway:
      discovery:
        locator:
          lowerCaseServiceId: true
          enabled: true
      routes:
        # 认证中心
        - id: fx-auth
          uri: lb://fx-auth
          predicates:
            - Path=/auth/**
          filters:
            # 验证码处理
            - CacheRequestFilter
            - ValidateCodeFilter
            - StripPrefix=1
        # 代码生成
        - id: fx-gen
          uri: lb://fx-gen
          predicates:
            - Path=/code/**
          filters:
            - StripPrefix=1
        # 定时任务
        - id: fx-job
          uri: lb://fx-job
          predicates:
            - Path=/schedule/**
          filters:
            - StripPrefix=1
        # 系统模块
        - id: fx-system
          uri: lb://fx-system
          predicates:
            - Path=/system/**
          filters:
            - StripPrefix=1
        # 文件服务
        - id: fx-file
          uri: lb://fx-file
          predicates:
            - Path=/file/**
          filters:
            - StripPrefix=1
        # TDengine服务
        - id: fx-tdengine
          uri: lb://fx-tdengine
          predicates:
            - Path=/tdengine/**
          filters:
            - StripPrefix=1
        # Link服务
        - id: fx-link
          uri: lb://fx-link
          predicates:
            - Path=/link/**
          filters:
            - StripPrefix=1
       # 系统模块
        - id: fx-broker-emq
          uri: lb://fx-broker-emq
          predicates:
            - Path=/emq/**
          filters:
            - StripPrefix=1
        # ProtocolAnalysis服务
        - id: fx-protocolAnalysis
          uri: lb://fx-protocolAnalysis
          predicates:
            - Path=/protocolAnalysis/**
          filters:
            - StripPrefix=1
      # rule服务
        - id: fx-rule
          uri: lb://fx-rule
          predicates:
            - Path=/rule/**
          filters:
            - StripPrefix=1

# 安全配置
security:
  # 验证码
  captcha:
    enabled: false
    type: char
  # 防止XSS攻击
  xss:
    enabled: true
    excludeUrls:
      - /system/notice
  # 不校验白名单
  ignore:
    whites:
      - /auth/logout
      - /auth/login
      - /auth/licenseUpload
      - /auth/findMacAddress
      - /auth/register
      - /*/v2/api-docs
      - /csrf
      #MQTT消息推送接口
      - /broker/publish/sendMessage
      #Broker WebSocket推送日志
      - /broker/websocket/logging
      #客户端身份认证接口
      - /link/device/clientAuthentication
      - /link/tool/**
      - /link/ruleInfo/test
      - /link/open/**
 
