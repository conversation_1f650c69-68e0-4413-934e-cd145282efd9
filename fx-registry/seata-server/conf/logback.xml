<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~  Copyright 1999-2019 Seata.io Group.
  ~
  ~  Licensed under the Apache License, Version 2.0 (the "License");
  ~  you may not use this file except in compliance with the License.
  ~  You may obtain a copy of the License at
  ~
  ~       http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~  Unless required by applicable law or agreed to in writing, software
  ~  distributed under the License is distributed on an "AS IS" BASIS,
  ~  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~  See the License for the specific language governing permissions and
  ~  limitations under the License.
  -->

<configuration scan="true" scanPeriod="60 seconds" debug="false">
    <!-- Context listeners -->
    <contextListener class="io.seata.server.logging.listener.SystemPropertyLoggerContextListener"/>

    <!-- Copied from spring-boot.jar -->
    <conversionRule conversionWord="clr" converterClass="io.seata.server.logging.logback.ColorConverter"/>
    <conversionRule conversionWord="wex" converterClass="io.seata.server.logging.logback.WhitespaceThrowableProxyConverter"/>
    <conversionRule conversionWord="wEx" converterClass="io.seata.server.logging.logback.ExtendedWhitespaceThrowableProxyConverter"/>

    <!-- common properties -->
    <property name="APPLICATION_NAME" value="seata-server"/>


    <!-- console-appender -->
    <include resource="logback/console-appender.xml"/>

    <!-- file-appender -->
    <include resource="logback/file-appender.xml"/>

    <!-- logstash-appender: off by default -->
    <!--<include resource="logback/logstash-appender.xml"/>-->

    <!-- kafka-appender: off by default -->
    <!--<include resource="logback/kafka-appender.xml"/>-->

    <root level="INFO">
        <!-- console-appender -->
        <appender-ref ref="CONSOLE"/>

        <!-- file-appender -->
        <appender-ref ref="FILE_ALL"/>
        <appender-ref ref="FILE_WARN"/>
        <appender-ref ref="FILE_ERROR"/>

        <!-- logstash-appender: off by default -->
        <!--<appender-ref ref="LOGSTASH"/>-->

        <!-- kafka-appender: off by default -->
        <!--<appender-ref ref="KAFKA"/>-->
    </root>
</configuration>
