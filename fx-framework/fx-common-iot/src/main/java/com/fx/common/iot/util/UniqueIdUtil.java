package com.fx.common.iot.util;

import org.apache.commons.lang3.RandomUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * UniqueIdUtil 是一个工具类，用于生成唯一的ID。
 * 它使用了一个原子整数 SEQUENCE 和一个随机生成的机器ID MACHINE_ID 来确保生成的ID是唯一的。
 * 它提供了两个公共方法：newRequestId 和 newUniqueId，用于生成不同前缀的唯一ID。
 *
 * <AUTHOR>
 * @since 2021
 */
public final class UniqueIdUtil {

    // 生成一个10到99之间的随机数作为机器ID
    private static final int MACHINE_ID = RandomUtils.nextInt(10, 99);

    // 创建一个原子整数SEQUENCE，初始值为1000
    private static final AtomicInteger SEQUENCE = new AtomicInteger(1000);

    /**
     * 生成一个以"RID"为前缀的唯一ID。
     *
     * @return 生成的唯一ID
     */
    public static String newRequestId() {
        return newUniqueId("RID");
    }

    /**
     * 生成一个以指定前缀的唯一ID。
     * ID的格式为：前缀 + 当前时间（年月日时分秒） + SEQUENCE的当前值 + 机器ID
     * 如果SEQUENCE的值大于或等于5000，那么将SEQUENCE的值重置为1000。
     *
     * @param prefix ID的前缀
     * @return 生成的唯一ID
     */
    public static String newUniqueId(String prefix) {
        int id = SEQUENCE.getAndIncrement();
        if (id >= 5000) {
            SEQUENCE.set(1000);
        }

        return prefix + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + id + MACHINE_ID;
    }

}