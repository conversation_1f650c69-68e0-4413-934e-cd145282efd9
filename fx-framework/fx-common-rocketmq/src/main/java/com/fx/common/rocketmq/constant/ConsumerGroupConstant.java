package com.fx.common.rocketmq.constant;

/**
 * @Description: 消费者组常量
 * @Author: fx
 * @Website: http://www.sxfxck.com
 * @CreateDate: 2022/4/15$ 15:53$
 * @UpdateUser: fx
 * @UpdateDate: 2022/4/15$ 15:53$
 * @UpdateRemark: 修改内容
 * @Version: V1.0
 */
public class ConsumerGroupConstant {


    /**
     * broker-consumer-emq-group
     */
    public static final String FX_BROKER_EMQ_GROUP = "fx-broker-emq-group";

    /**
     * default-consumer-group
     */
    public static final String FXLINKS_GROUP = "fxlinks-group";

    /**
     * broker-consumer-group
     */
    public static final String FX_BROKER_GROUP = "fx-broker-group";

    /**
     * link-consumer-group
     */
    public static final String FX_LINK_GROUP = "fx-link-group";

    /**
     * tdengine-consumer-group
     */
    public static final String FX_TDENGINE_GROUP = "fx-tdengine-group";

    /**
     * tdengine-consumer-group_create_tb
     */
    public static final String TDENGINE_CONSUMER_GROUP_CREATE_TB = "tdengine-consumer-group-create-tb";
    /**
     * tdengine-consumer-group_update_tb
     */
    public static final String TDENGINE_CONSUMER_GROUP_UPDATE_TB = "tdengine-consumer-group-update-tb";

    /**
     * tdengine-add-thing-model-group
     */
    public static final String TDENGINE_ADD_THING_MODEL_GROUP = "tdengine-add-thing-model-group";

    /**
     * rule-thing-model-group
     */
    public static final String RULE_THING_MODEL_DATA_GROUP = "rule-thing-model-data-group";

    /**
     * link-alarm-model-group
     */
    public static final String LINK_ALARM_MODEL_GROUP = "link-alarm-model-group";


    /**
     * rule-send-http-group
     */
    public static final String RULE_SEND_HTTP_GROUP = "rule-send-http-group";
    /**
     * rule-send-mqtt-group
     */
    public static final String RULE_SEND_MQTT_GROUP = "rule-send-mqtt-group";
    /**
     * rule-send-mysql-group
     */
    public static final String RULE_SEND_MYSQL_GROUP = "rule-send-mysql-group";
    /**
     * rule-send-http-retry-group
     */
    public static final String RULE_SEND_HTTP_RETRY_GROUP = "rule-send-http-retry-group";
    /**
     * rule-send-mqtt-retry-group
     */
    public static final String RULE_SEND_MQTT_RETRY_GROUP = "rule-send-mqtt-retry-group";
    /**
     * rule-send-mysql-retry-group
     */
    public static final String RULE_SEND_MYSQL_RETRY_GROUP = "rule-send-mysql-retry-group";

    /**
     * fx-link-device-consumer-shadow
     */
    public static final String FX_LINK_DEVICE_CONSUMER_SHADOW = "fx-link-device-consumer-shadow";

    /**
     * 设备影子消费者
     * fx-emq-device-shadow-consumer
     */
    public static final String FX_EMQ_DEVICE_SHADOW_CONSUMER = "fx-emq-device-shadow-consumer";

    /**
     * 设备影子上报主题响应
     * fx-emq-device-shadow-reply-consumer
     */
    public static final String FX_EMQ_DEVICE_SHADOW_REPLY_CONSUMER = "fx-emq-device-shadow-reply-consumer";
    /**
     * OTA升级包上报主题响应
     * fx-emq-device-shadow-reply-consumer
     */
    public static final String FX_EMQ_DEVICE_OTA_REPLY_CONSUMER = "fx-emq-device-ota-reply-consumer";
    /**
     * 设备消息回调
     * fx-emq-device-shadow-consumer
     */
    public static final String FX_EMQ_DEVICE_POST_REPLY_CONSUMER = "fx-emq-device-post-reply-consumer";
    /**
     * tdengine-consumer-group-ewa-data-save
     */
    public static final String TDENGINE_CONSUMER_GROUP_EWA_DATA_SAVE = "tdengine-consumer-group-ewa-data-save";

    /**
     * fx-link-device-alarm-log
     */
    public static final String DEVICE_ALARM_LOG = "device-alarm-log";

}
