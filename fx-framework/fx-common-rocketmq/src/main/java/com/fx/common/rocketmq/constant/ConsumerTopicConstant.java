package com.fx.common.rocketmq.constant;

import lombok.Data;

/**
 * @Description: 消费者主题常量
 * @Author: fx
 * @Website: http://www.sxfxck.com
 * @CreateDate: 2022/4/15$ 15:53$
 * @UpdateUser: fx
 * @UpdateDate: 2022/4/15$ 15:53$
 * @UpdateRemark: 修改内容
 * @Version: V1.0
 */
@Data
public class ConsumerTopicConstant {

    /**
     * 监听物模型上报主题
     */
    public static final String FX_THING_MODEL_MSG = "fx-thing-model-msg";

    /**
     * 监听设备上报主题
     */
    public static final String FX_EMQ_TOPIC_MSG = "fx-emq-topic-msg";

    /**
     * MQTT设备消息监听主题
     */
    public static final String FX_LINKS_MQTT_MSG = "fxlinks-link-mqttMsg";

    /**
     * TDengine超级表创键修改动作监听主题
     */
    public static final String PRODUCT_SUPER_TABLE_CREATE = "product-super-table-create";

    /**
     * TDengine超级表创键修改动作监听主题
     */
    public static final String PRODUCT_SUPER_TABLE_UPDATE = "product-super-table-update";

    /**
     * 系统指标数据采集动作监听主题
     */
    public static final String FX_LINKS_COLLECTION_SYSTEM = "fxlinks-collection-system";

    /**
     * 设备状态监听主题
     */
    public static final String FX_LINKS_DEVICE_STATE = "fxlinks-device-state";

    /**
     * 监听设备影子上报主题
     */
    public static final String FX_LINKS_DEVICE_SHADOW = "fx-links-device-shadow";

    /**
     * 监听获取设备影子主题响应和监听上报设备影子主题响应
     */
    public static final String FX_LINKS_DEVICE_SHADOW_REPLY = "fx-links-device-shadow-reply";

    /**
     * 监听获取ota升级包主题响应和监听上报ota升级包主题响应
     */
    public static final String FX_LINKS_DEVICE_OTA_REPLY = "fx-links-device-ota-reply";

    /**
     * 设备消息回调
     */
    public static final String FX_LINKS_DEVICE_POST_REPLY = "fx-links-device-post-reply";

    /**
     * TDengine 保存裔娲抄表数据
     */
    public static final String EA_METER_RECORD_SAVE = "ea_meter_record_save";

    /**
     * 规则引擎监听物模型上报主题
     */
    public static final String RULE_THING_MODEL_MSG = "rule-thing-model-msg";

    /**
     * 告警信息
     */
    public static final String FX_LINKS_DEVICE_ALARM = "fx-links-device-alarm";
    /**
     * http发送
     */
    public static final String FX_LINKS_DEVICE_HTTP = "fx-links-device-http";
    /**
     * mqtt发送
     */
    public static final String FX_LINKS_DEVICE_MQTT = "fx-links-device-mqtt";
    /**
     * mysql发送
     */
    public static final String FX_LINKS_DEVICE_MYSQl = "fx-links-device-mysql";

    /**
     * tcp指令下发
     */
    public static final String FX_TCP_COMMAND = "fx-tcp-command";

    /**
     * tcp指令下发响应
     */
    public static final String FX_TCP_COMMAND_REPLY = "fx-tcp-command-reply";
}
