package com.fx.common.log.aspect;

import com.alibaba.fastjson.JSON;
import com.fx.common.core.utils.ServletUtils;
import com.fx.common.core.utils.StringUtils;
import com.fx.common.log.config.LogProperties;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.*;

/**
 * API 访问日志切面
 * <p>
 * 目的：在非生产环境时，打印 Controller 请求和响应日志到控制台中。
 *
 * <AUTHOR>
 */
@Slf4j
@Aspect
@Component
public class ApiAccessLogAspect {

    @Resource
    private Environment environment;

    @Resource
    private LogProperties logProperties;

    /**
     * 定义切点：拦截所有 Controller 类的方法
     * 包括 @RestController 和 @Controller 注解的类
     */
    @Pointcut("@within(org.springframework.web.bind.annotation.RestController) || " +
            "@within(org.springframework.stereotype.Controller)")
    public void controllerPointcut() {
    }

    /**
     * 定义切点：拦截带有 Web 映射注解的方法
     */
    @Pointcut("@annotation(org.springframework.web.bind.annotation.RequestMapping) || " +
            "@annotation(org.springframework.web.bind.annotation.GetMapping) || " +
            "@annotation(org.springframework.web.bind.annotation.PostMapping) || " +
            "@annotation(org.springframework.web.bind.annotation.PutMapping) || " +
            "@annotation(org.springframework.web.bind.annotation.DeleteMapping) || " +
            "@annotation(org.springframework.web.bind.annotation.PatchMapping)")
    public void mappingPointcut() {
    }

    /**
     * 环绕通知：记录请求和响应日志
     */
    @Around("controllerPointcut() && mappingPointcut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        // 检查是否启用访问日志
        if (!isAccessLogEnabled()) {
            return joinPoint.proceed();
        }

        // 只在非生产环境记录详细日志
        if (isProdEnvironment()) {
            return joinPoint.proceed();
        }

        StopWatch stopWatch = new StopWatch();
        HttpServletRequest request = ServletUtils.getRequest();

        try {
            // 打印请求日志
            printRequestLog(joinPoint, request);

            // 开始计时
            stopWatch.start();

            // 执行目标方法
            Object result = joinPoint.proceed();

            // 停止计时
            stopWatch.stop();

            // 打印响应日志
            printResponseLog(request, stopWatch.getTotalTimeMillis(), null);

            return result;

        } catch (Exception e) {
            // 停止计时
            if (stopWatch.isRunning()) {
                stopWatch.stop();
            }

            // 打印异常日志
            printResponseLog(request, stopWatch.getTotalTimeMillis(), e);

            throw e;
        }
    }

    /**
     * 打印请求日志
     */
    private void printRequestLog(JoinPoint joinPoint, HttpServletRequest request) {
        if (request == null) {
            return;
        }

        try {
            String uri = request.getRequestURI();
            String queryString = request.getQueryString();

            // 获取请求参数
            Map<String, String> paramMap = getParameterMap(request);

            // 获取方法参数
            Object[] args = joinPoint.getArgs();
            String argsStr = getMethodArgs(args);

            // 构建完整URL
            String fullUrl = uri;
            if (StringUtils.isNotEmpty(queryString)) {
                fullUrl = uri + "?" + queryString;
            }

            // 判断是否有参数
            boolean hasParams = !paramMap.isEmpty() || StringUtils.isNotEmpty(argsStr);

            if (!hasParams) {
                log.info("[preHandle][开始请求 URL({}) 无参数]", fullUrl);
            } else {
                // 合并所有参数
                StringBuilder paramBuilder = new StringBuilder();
                if (!paramMap.isEmpty()) {
                    paramBuilder.append(JSON.toJSONString(paramMap));
                }
                if (StringUtils.isNotEmpty(argsStr)) {
                    if (paramBuilder.length() > 0) {
                        paramBuilder.append(" ");
                    }
                    paramBuilder.append(argsStr);
                }
                log.info("[preHandle][开始请求 URL({}) 参数({})]", fullUrl, paramBuilder.toString());
            }

            // 打印 Controller 方法信息
            printControllerInfo(joinPoint);

        } catch (Exception e) {
            log.debug("打印请求日志异常: {}", e.getMessage());
        }
    }

    /**
     * 打印响应日志
     */
    private void printResponseLog(HttpServletRequest request,
                                  long duration, Exception exception) {
        if (request == null) {
            return;
        }

        try {
            String uri = request.getRequestURI();
            String queryString = request.getQueryString();

            // 构建完整URL
            String fullUrl = uri;
            if (StringUtils.isNotEmpty(queryString)) {
                fullUrl = uri + "?" + queryString;
            }

            if (exception != null) {
                log.error("[afterCompletion][完成请求 URL({}) 耗时({} ms) 异常: {}]",
                        fullUrl, duration, exception.getMessage(), exception);
            } else {
                log.info("[afterCompletion][完成请求 URL({}) 耗时({} ms)]", fullUrl, duration);
            }

        } catch (Exception e) {
            log.debug("打印响应日志异常: {}", e.getMessage());
        }
    }

    /**
     * 获取请求参数Map
     */
    private Map<String, String> getParameterMap(HttpServletRequest request) {
        Map<String, String> paramMap = new HashMap<>();
        try {
            Enumeration<String> paramNames = request.getParameterNames();
            while (paramNames.hasMoreElements()) {
                String paramName = paramNames.nextElement();
                String paramValue = request.getParameter(paramName);
                paramMap.put(paramName, paramValue);
            }
        } catch (Exception e) {
            log.debug("获取请求参数异常: {}", e.getMessage());
        }
        return paramMap;
    }

    /**
     * 获取方法参数字符串
     */
    private String getMethodArgs(Object[] args) {
        if (args == null || args.length == 0) {
            return "";
        }

        try {
            // 过滤掉 HttpServletRequest、HttpServletResponse 等对象
            Object[] filteredArgs = Arrays.stream(args)
                    .filter(Objects::nonNull)
                    .filter(arg -> !(arg instanceof HttpServletRequest))
                    .filter(arg -> !(arg instanceof javax.servlet.http.HttpServletResponse))
                    .filter(arg -> !arg.getClass().getName().contains("BindingResult"))
                    .toArray();

            if (filteredArgs.length == 0) {
                return "";
            }

            String argsStr = JSON.toJSONString(filteredArgs);
            // 限制参数长度
            if (argsStr.length() > 1000) {
                argsStr = argsStr.substring(0, 1000) + "...";
            }
            return argsStr;

        } catch (Exception e) {
            log.debug("获取方法参数异常: {}", e.getMessage());
            return "";
        }
    }

    /**
     * 打印 Controller 方法信息
     */
    private void printControllerInfo(JoinPoint joinPoint) {
        try {
            Method method = ((org.aspectj.lang.reflect.MethodSignature) joinPoint.getSignature()).getMethod();
            Class<?> clazz = method.getDeclaringClass();

            log.info("    → Controller方法: {}.{}()", clazz.getSimpleName(), method.getName());

        } catch (Exception e) {
            log.debug("打印Controller方法信息异常: {}", e.getMessage());
        }
    }

    /**
     * 判断是否启用访问日志
     */
    private boolean isAccessLogEnabled() {
        return logProperties != null
                && logProperties.getAccess() != null
                && Boolean.TRUE.equals(logProperties.getAccess().getEnabled());
    }

    /**
     * 判断是否为生产环境
     */
    private boolean isProdEnvironment() {
        if (environment == null) {
            return false;
        }
        String[] activeProfiles = environment.getActiveProfiles();
        for (String profile : activeProfiles) {
            if ("prod".equalsIgnoreCase(profile) || "production".equalsIgnoreCase(profile)) {
                return true;
            }
        }
        return false;
    }
}
