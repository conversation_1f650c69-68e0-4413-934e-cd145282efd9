package com.fx.common.log.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * 日志配置属性类
 *
 * <AUTHOR>
 */
@Slf4j
@Data
@Component
@ConfigurationProperties(prefix = "fx.log")
public class LogProperties {

    /**
     * 访问日志配置
     */
    private Access access = new Access();

    @PostConstruct
    public void init() {
        log.debug("LogProperties initialized, fx.log.access.enabled = {}", access.getEnabled());
    }

    /**
     * 访问日志配置
     */
    @Data
    public static class Access {
        /**
         * 是否启用 API 访问日志
         */
        private Boolean enabled = false;

    }
}
