// 获取2007协议数据标识描述
function getDataIdentifierDescription2007(mark) {
    var descriptions = {
        // 基础电能类数据标识
        '00010000': '正向有功总电能',
        '00010100': '正向有功费率1电能',
        '00010200': '正向有功费率2电能',
        '00010300': '正向有功费率3电能',
        '00010400': '正向有功费率4电能',
        '00020000': '反向有功总电能',
        '00030000': '正向无功总电能',
        '00040000': '反向无功总电能',

        // 最大需量类数据标识
        '01010000': '正向有功总最大需量及发生时间',
        '0101ff00': '正向有功最大需量及发生时间数据块',

        // 电能数据块标识
        '0001ff00': '正向有功电能数据块',
        '0002ff00': '反向有功电能数据块',
        '0003ff00': '组合无功1电能数据块',
        '0004ff00': '组合无功2电能数据块',

        // 实时数据块标识
        '0201ff00': '电压数据块',
        '0202ff00': '电流数据块',
        '0203ff00': '瞬时有功功率数据块',
        '0204ff00': '瞬时无功功率数据块',
        '0205ff00': '瞬时视在功率数据块',
        '0206ff00': '功率因数数据块',

        // 特殊数据标识
        '02800001': '零线电流'
    };
    return descriptions[mark] || '未知数据标识(' + mark + ')';
}

// 获取1997协议数据标识描述
function getDataIdentifierDescription1997(mark) {
    var descriptions = {
        '9010': '正向有功总电能',
        '9011': '正向有功费率1电能',
        '9012': '正向有功费率2电能',
        '9013': '正向有功费率3电能',
        '9014': '正向有功费率4电能',
        '901f': '正向有功总电能数据块',
        '9020': '反向有功总电能',
        '902f': '反向有功总电能数据块',
        '9110': '正向无功总电能',
        '911f': '正向无功总电能数据块',
        '9120': '反向无功总电能',
        '912f': '反向无功总电能数据块',
        'b611': 'A相电压',
        'b612': 'B相电压',
        'b613': 'C相电压',
        'b621': 'A相电流',
        'b622': 'B相电流',
        'b623': 'C相电流',
        'b630': '瞬时有功功率',
        'b631': 'A相有功功率',
        'b632': 'B相有功功率',
        'b633': 'C相有功功率',
        'b640': '瞬时无功功率',
        'b641': 'A相无功功率',
        'b642': 'B相无功功率',
        'b643': 'C相无功功率',
        'b650': '总功率因数',
        'b651': 'A相功率因数',
        'b652': 'B相功率因数',
        'b653': 'C相功率因数',
        'a010': '正向有功总最大需量',
        'a01f': '有功最大需量数据块',
        'b010': '正向有功总最大需量发生时间',
        'b01f': '正向有功最大需量发生时间数据块'
    };
    return descriptions[mark] || '未知数据标识(' + mark + ')';
}

// 从每个字节中减去 0x33，并反转顺序
function parseData(datas, start, size) {
    var dataBytes = [];
    for (var i = start + size - 2; i >= start; i -= 2) {
        var value = parseInt(datas.slice(i, i + 2), 16) - 0x33;
        if (value < 0) {
            value = 0xFF;
        }
        // 补零
        var s = value.toString(16);
        while (s.length < 2) {
            s = '0' + s
        }
        dataBytes.push(s);
        // dataBytes.push(padZero(value.toString(16), 2));
    }
    return dataBytes.join('');
}

// 从末尾到开头反转每两个字节
function parseDataReverse(datas, start, size) {
    var dataBytes = [];
    for (var i = start + size - 2; i >= start; i -= 2) {
        dataBytes.push(datas.slice(i, i + 2));
    }
    return dataBytes.join('');
}

// 通过切片和格式化解析电能数据  point 从前到后的小数点
function parseEnergyDataBySlice(datas, start, size, point) {
    var dataValue = parseData(datas, start, size);
    var integerPart = dataValue.slice(0, point);
    var decimalPart = dataValue.slice(point);
    var value = parseFloat(integerPart + '.' + decimalPart);
    // 根据第一个十六进制数字检查负值
    if (parseInt(dataValue[0], 16) >= 8) {
        value = -parseFloat(dataValue.slice(1, point) + '.' + decimalPart);
    }
    return value.toString();
}

// 安全乘法
function safeMultiply(value, multiplier) {
    return value ? (parseFloat((parseFloat(value) * multiplier).toFixed(3))).toString() : null;
}

// 设置倍率
function setRate(data, deviceKey) {
    if (!data || !deviceKey) {
        return;
    }

    var ct = 1;
    var pt = 1;

    var deviceRates = {
        "000002230297_016589": { "ct": "600/5", "pt": "6000/100" }, // 四老沟2400波特率
        "000002230356_016589": { "ct": "600/5", "pt": "35000/100" },
        "000002230362_016589": { "ct": "600/5", "pt": "35000/100" },
        "000002230384_016589": { "ct": "600/5", "pt": "35000/100" },
        "000002230386_016589": { "ct": "600/5", "pt": "35000/100" },
        "000002232170_016589": { "ct": "600/5", "pt": "110000/100" },
        "000002232178_016589": { "ct": "600/5", "pt": "110000/100" },
        "000002232334_016589": { "ct": "75/5", "pt": "6000/100" },
        "000002232376_016589": { "ct": "150/5", "pt": "6000/100" },
        "000000250001_017728": { "ct": "1250/5", "pt": "10000/100" }, // 南深井1200波特率
        "000000250006_017728": { "ct": "1250/5", "pt": "10000/100" },
        "000000973186_017728": { "ct": "600/5", "pt": "10000/100" },
        "000000973190_017728": { "ct": "400/5", "pt": "10000/100" },
        "000000915675_017728": { "ct": "800/5", "pt": "10000/100" },
        "000000973185_017728": { "ct": "400/5", "pt": "10000/100" },
        "000000915676_017728": { "ct": "200/5", "pt": "10000/100" },
        "000000962014_017728": { "ct": "100/5", "pt": "10000/100" },
        "000000802044_017728": { "ct": "100/5", "pt": "10000/100" },
        "000000915699_017728": { "ct": "600/5", "pt": "10000/100" },
        "000000973193_017728": { "ct": "400/5", "pt": "10000/100" },
        "000000973184_017728": { "ct": "600/5", "pt": "10000/100" },
        "000000973183_017728": { "ct": "400/5", "pt": "10000/100" },
        "000000973189_017728": { "ct": "200/5", "pt": "10000/100" },
        "000001961973_017728": { "ct": "400/5", "pt": "10000/100" },
        "000001530229_017728": { "ct": "800/5", "pt": "10000/100" },
        "000001561919_017728": { "ct": "200/5", "pt": "10000/100" },
        "000001561887_017728": { "ct": "400/5", "pt": "10000/100" },
        "000001561886_017728": { "ct": "400/5", "pt": "10000/100" },
        "000001561885_017728": { "ct": "400/5", "pt": "10000/100" },
        "000000973187_017728": { "ct": "400/5", "pt": "35000/100" },
        "000000973188_017728": { "ct": "400/5", "pt": "35000/100" },
        "000001561910_017728": { "ct": "400/5", "pt": "10000/100" },
        "000000000531_019734": { "ct": "1500/5", "pt": "6000/100" }, // 煤峪口1200波特率
        "000000000479_019734": { "ct": "1500/5", "pt": "6000/100" },
        "000000000073_019734": { "ct": "200/5", "pt": "6000/100" },
        "000000000078_019734": { "ct": "400/5", "pt": "6000/100" },
        "000000000074_019734": { "ct": "400/5", "pt": "6000/100" },
        "000000000069_019734": { "ct": "200/5", "pt": "6000/100" },
        "000000804182_019734": { "ct": "200/5", "pt": "6000/100" },
        "000000000024_019734": { "ct": "300/5", "pt": "6000/100" },
        "000000000023_019734": { "ct": "600/5", "pt": "6000/100" },
        "000000000077_019734": { "ct": "600/5", "pt": "6000/100" },
        "000000000076_019734": { "ct": "200/5", "pt": "6000/100" },
        "000000000075_019734": { "ct": "400/5", "pt": "6000/100" },
        "000000000067_019734": { "ct": "200/5", "pt": "6000/100" },
        "000000804213_019734": { "ct": "50/5", "pt": "6000/100" },
        "000000804204_019734": { "ct": "50/5", "pt": "6000/100" },
        "000000000966_019734": { "ct": "300/5", "pt": "6000/100" },
        "000000000070_019734": { "ct": "200/5", "pt": "6000/100" },
        "000000000017_019734": { "ct": "400/5", "pt": "6000/100" },
        "000000000013_019734": { "ct": "600/5", "pt": "6000/100" },
        "000000000014_019734": { "ct": "300/5", "pt": "6000/100" },
        "000000804174_019734": { "ct": "200/5", "pt": "6000/100" },
        "000000010068_019734": { "ct": "200/5", "pt": "6000/100" },
        "000000010962_019734": { "ct": "200/5", "pt": "6000/100" },
        "000000915677_019734": { "ct": "600/5", "pt": "35000/100" },
        "000001162866_019734": { "ct": "400/5", "pt": "35000/100" },
        "000000000008_019734": { "ct": "400/5", "pt": "6000/100" },
        "000000000095_019734": { "ct": "400/5", "pt": "35000/100" },
        "000000804156_017923": { "ct": "10/5", "pt": "6000/100" }, // 马脊梁1200波特率
        "000000804195_017923": { "ct": "300/5", "pt": "6000/100" },
        "000000803919_017923": { "ct": "200/5", "pt": "6000/100" },
        "000000803929_017923": { "ct": "300/5", "pt": "6000/100" },
        "000000803933_017923": { "ct": "600/5", "pt": "6000/100" },
        "000000803950_017923": { "ct": "150/5", "pt": "6000/100" },
        "000000803905_017923": { "ct": "600/5", "pt": "6000/100" },
        "000000010967_017923": { "ct": "150/5", "pt": "6000/100" },
        "000000803962_017923": { "ct": "200/5", "pt": "6000/100" },
        "000000803907_017923": { "ct": "600/5", "pt": "6000/100" },
        "000000010969_017923": { "ct": "150/5", "pt": "6000/100" },
        "000000000230_017923": { "ct": "300/5", "pt": "6000/100" },
        "000000803913_017923": { "ct": "300/5", "pt": "6000/100" },
        "000000803968_017923": { "ct": "300/5", "pt": "6000/100" },
        "000000804198_017923": { "ct": "300/5", "pt": "6000/100" },
        "000000803967_017923": { "ct": "600/5", "pt": "6000/100" },
        "000000803915_017923": { "ct": "200/5", "pt": "6000/100" },
        "000000000086_017923": { "ct": "400/5", "pt": "6000/100" },
        "000000000076_017923": { "ct": "400/5", "pt": "6000/100" },
        "000000000078_017923": { "ct": "200/5", "pt": "6000/100" },
        "000000000087_017923": { "ct": "200/5", "pt": "6000/100" },
        "000000801682_027895": { "ct": "600/5", "pt": "6000/100" }, // 机厂1200波特率
        "000000801694_027895": { "ct": "400/5", "pt": "6000/100" },
        "000000802178_027895": { "ct": "100/5", "pt": "6000/100" },
        "000000801713_027895": { "ct": "400/5", "pt": "6000/100" },
        "000000803922_027895": { "ct": "150/5", "pt": "6000/100" },
        "000000803944_027895": { "ct": "150/5", "pt": "6000/100" },
        "000000802207_027895": { "ct": "400/5", "pt": "6000/100" },
        "000000801725_027895": { "ct": "600/5", "pt": "6000/100" },
        "000000803914_027895": { "ct": "100/5", "pt": "6000/100" },
        "000000801690_027895": { "ct": "400/5", "pt": "6000/100" },
        "000000801695_027895": { "ct": "400/5", "pt": "6000/100" },
        "000000973191_027895": { "ct": "400/5", "pt": "6000/100" },
        "000000801723_027895": { "ct": "600/5", "pt": "6000/100" },
        "000000803952_027895": { "ct": "600/5", "pt": "6000/100" },
        "000000801700_027895": { "ct": "400/5", "pt": "6000/100" },
        "000000803938_027895": { "ct": "400/5", "pt": "6000/100" },
        "000000801719_027895": { "ct": "400/5", "pt": "6000/100" },
        "000000801728_027895": { "ct": "400/5", "pt": "6000/100" },
        "000000802210_027895": { "ct": "300/5", "pt": "6000/100" },
        "000000801683_027895": { "ct": "200/5", "pt": "6000/100" },
        "000000801721_027895": { "ct": "100/5", "pt": "6000/100" },
        "000000802019_027895": { "ct": "150/5", "pt": "6000/100" },
        "000000801696_027895": { "ct": "600/5", "pt": "6000/100" },
        "000000801692_027895": { "ct": "400/5", "pt": "6000/100" },
        "000000801729_027895": { "ct": "400/5", "pt": "6000/100" },
        "000000801717_027895": { "ct": "300/5", "pt": "6000/100" },
        "000001162861_027895": { "ct": "300/5", "pt": "6000/100" },
        "000000010489_015917": { "ct": "2000/5", "pt": "6000/100" }, // 平旺站1200波特率
        "000000010488_015917": { "ct": "2000/5", "pt": "6000/100" },
        "000000802057_015917": { "ct": "600/5", "pt": "6000/100" },
        "000000802194_015917": { "ct": "600/5", "pt": "6000/100" },
        "000000804003_015917": { "ct": "300/5", "pt": "6000/100" },
        "000001530263_015917": { "ct": "200/5", "pt": "6000/100" },
        "000000802042_015917": { "ct": "600/5", "pt": "6000/100" },
        "000000802020_015917": { "ct": "600/5", "pt": "6000/100" },
        "000000802028_015917": { "ct": "400/5", "pt": "6000/100" },
        "000000802027_015917": { "ct": "300/5", "pt": "6000/100" },
        "000000802056_015917": { "ct": "400/5", "pt": "6000/100" },
        "000000802052_015917": { "ct": "75/5", "pt": "6000/100" },
        "000000802021_015917": { "ct": "200/5", "pt": "6000/100" },
        "000000802026_015917": { "ct": "200/5", "pt": "6000/100" },
        "000000010985_015917": { "ct": "100/5", "pt": "6000/100" },
        "000000804041_015917": { "ct": "300/5", "pt": "6000/100" },
        "000000802037_015917": { "ct": "100/5", "pt": "6000/100" },
        "000000802029_015917": { "ct": "400/5", "pt": "6000/100" },
        "000001162871_015917": { "ct": "300/5", "pt": "6000/100" },
        "000000802049_015917": { "ct": "200/5", "pt": "6000/100" },
        "000000802034_015917": { "ct": "200/5", "pt": "6000/100" },
        "000000000245_015917": { "ct": "100/5", "pt": "6000/100" },
        "000000802169_015917": { "ct": "400/5", "pt": "6000/100" },
        "000002232415_015917": { "ct": "200/5", "pt": "6000/100" },
        "000000802058_015917": { "ct": "200/5", "pt": "6000/100" },
        "000000802183_015917": { "ct": "100/5", "pt": "6000/100" },
        "000000802046_015917": { "ct": "100/5", "pt": "6000/100" },
        "000000802023_015917": { "ct": "100/5", "pt": "6000/100" },
        "000000802045_015917": { "ct": "75/5", "pt": "6000/100" },
        "000000802013_015917": { "ct": "500/5", "pt": "6000/100" },
        "000000802043_015917": { "ct": "600/5", "pt": "6000/100" },
        "000000010987_015917": { "ct": "600/5", "pt": "6000/100" },
        "000000802036_015917": { "ct": "600/5", "pt": "6000/100" },
        "000000801701_015917": { "ct": "200/5", "pt": "6000/100" },
        "000001162844_015917": { "ct": "200/5", "pt": "6000/100" },
        "000000802180_015917": { "ct": "200/5", "pt": "6000/100" },
        "000001530272_016747": { "ct": "400/5", "pt": "10000/100" }, // 榆林1200波特率
        "000001530262_016747": { "ct": "800/5", "pt": "10000/100" },
        "000001530244_016747": { "ct": "800/5", "pt": "10000/100" },
        "000001530259_016747": { "ct": "1/1", "pt": "10000/100" },
        "000001530239_016747": { "ct": "400/5", "pt": "10000/100" },
        "000001530267_016747": { "ct": "800/5", "pt": "10000/100" },
        "000001530248_016747": { "ct": "400/5", "pt": "10000/100" },
        "000001530270_016747": { "ct": "1/1", "pt": "10000/100" },
        "000001530250_016747": { "ct": "1/1", "pt": "10000/100" },
        "000001530252_016747": { "ct": "1/1", "pt": "10000/100" },
        "000001530265_016747": { "ct": "400/5", "pt": "10000/100" },
        "000001530246_016747": { "ct": "1/1", "pt": "10000/100" },
        "000001530271_016747": { "ct": "400/5", "pt": "10000/100" },
        "000001530251_016747": { "ct": "800/5", "pt": "10000/100" },
        "000001530249_016747": { "ct": "800/5", "pt": "10000/100" },
        "000001530223_016747": { "ct": "800/5", "pt": "10000/100" },
        "000001530226_016747": { "ct": "400/5", "pt": "10000/100" },
        "000001530269_016747": { "ct": "1/1", "pt": "10000/100" },
        "000001530247_016747": { "ct": "1/1", "pt": "10000/100" },
        "000001530255_016747": { "ct": "1/1", "pt": "10000/100" },
        "000001162854_016747": { "ct": "400/5", "pt": "35000/100" },
        "000001162870_016747": { "ct": "400/5", "pt": "35000/100" },
        "000000000134_016747": { "ct": "1200/5", "pt": "10000/100" },
        "000000000010_016747": { "ct": "1200/5", "pt": "10000/100" },
        "000000000139_016747": { "ct": "1200/5", "pt": "10000/100" },
        "000000340031_020017": { "ct": "2000/5", "pt": "10000/100" }, // 窑子坡1200波特率
        "000000340066_020017": { "ct": "2000/5", "pt": "10000/100" },
        "000000804153_020017": { "ct": "400/5", "pt": "10000/100" },
        "000000804192_020017": { "ct": "600/5", "pt": "10000/100" },
        "000000804190_020017": { "ct": "200/5", "pt": "10000/100" },
        "000000804158_020017": { "ct": "800/5", "pt": "10000/100" },
        "000000804201_020017": { "ct": "400/5", "pt": "10000/100" },
        "000000804200_020017": { "ct": "600/5", "pt": "10000/100" },
        "000000804169_020017": { "ct": "300/5", "pt": "10000/100" },
        "000000804175_020017": { "ct": "300/5", "pt": "10000/100" },
        "000000804186_020017": { "ct": "400/5", "pt": "10000/100" },
        "000000804415_020017": { "ct": "800/5", "pt": "10000/100" },
        "000000804417_020017": { "ct": "600/5", "pt": "10000/100" },
        "000000804416_020017": { "ct": "400/5", "pt": "10000/100" },
        "000000804172_020017": { "ct": "600/5", "pt": "10000/100" },
        "000000804170_020017": { "ct": "400/5", "pt": "10000/100" },
        "000000803926_020017": { "ct": "600/5", "pt": "10000/100" },
        "000000803941_020017": { "ct": "400/5", "pt": "10000/100" },
        "000001131337_020017": { "ct": "400/5", "pt": "10000/100" },
        "000000010540_020017": { "ct": "500/5", "pt": "35000/100" },
        "000000010528_020017": { "ct": "500/5", "pt": "35000/100" },
        "000000000540_020017": { "ct": "500/5", "pt": "35000/100" },
        "000000000528_020017": { "ct": "500/5", "pt": "35000/100" },
        "000000804159_020017": { "ct": "250/5", "pt": "10000/100" },
        "000000804171_020017": { "ct": "250/5", "pt": "10000/100" },
        "000000000013_019891": { "ct": "2000/5", "pt": "10000/100" }, // 韩家窑1200波特率
        "000000000043_019891": { "ct": "2000/5", "pt": "10000/100" },
        "000000000154_019891": { "ct": "300/5", "pt": "10000/100" },
        "000000000125_019891": { "ct": "300/5", "pt": "10000/100" },
        "000000000130_019891": { "ct": "100/5", "pt": "10000/100" },
        "000000000120_019891": { "ct": "300/5", "pt": "10000/100" },
        "000000000126_019891": { "ct": "600/5", "pt": "10000/100" },
        "000000000153_019891": { "ct": "100/5", "pt": "10000/100" },
        "000000000147_019891": { "ct": "300/5", "pt": "10000/100" },
        "000000000155_019891": { "ct": "300/5", "pt": "10000/100" },
        "000000000127_019891": { "ct": "100/5", "pt": "10000/100" },
        "000000000119_019891": { "ct": "600/5", "pt": "10000/100" },
        "000000000156_019891": { "ct": "300/5", "pt": "10000/100" },
        "000000000099_019891": { "ct": "100/5", "pt": "10000/100" },
        "000000000185_019891": { "ct": "300/5", "pt": "10000/100" },
        "000000000131_019891": { "ct": "800/5", "pt": "10000/100" },
        "000000000254_019891": { "ct": "800/5", "pt": "10000/100" },
        "000000000157_019891": { "ct": "800/5", "pt": "10000/100" },
        "000000000150_019891": { "ct": "800/5", "pt": "10000/100" },
        "000000000256_019891": { "ct": "300/5", "pt": "10000/100" },
        "000000000097_019891": { "ct": "200/5", "pt": "10000/100" },
        "000000000255_019891": { "ct": "800/5", "pt": "10000/100" },
        "000000000098_019891": { "ct": "800/5", "pt": "10000/100" },
        "000000000159_019891": { "ct": "800/5", "pt": "10000/100" },
        "000000000188_019891": { "ct": "800/5", "pt": "10000/100" },
        "000000000148_019891": { "ct": "300/5", "pt": "10000/100" },
        "000000000253_019891": { "ct": "200/5", "pt": "10000/100" },
        "000000000117_019891": { "ct": "500/5", "pt": "10000/100" },
        "000000000187_019891": { "ct": "500/5", "pt": "10000/100" },
        "000001162864_019891": { "ct": "600/5", "pt": "35000/100" },
        "000001162856_019891": { "ct": "600/5", "pt": "35000/100" },
        "000000340068_020278": { "ct": "2000/5", "pt": "10000/100" }, // 同家梁1200波特率
        "000000340033_020278": { "ct": "2000/5", "pt": "10000/100" },
        "000000730159_020278": { "ct": "250/5", "pt": "10000/100" },
        "000000730166_020278": { "ct": "250/5", "pt": "10000/100" },
        "000000730162_020278": { "ct": "800/5", "pt": "10000/100" },
        "000000730161_020278": { "ct": "800/5", "pt": "10000/100" },
        "000000730189_020278": { "ct": "800/5", "pt": "10000/100" },
        "000000730168_020278": { "ct": "800/5", "pt": "10000/100" },
        "000000730157_020278": { "ct": "250/5", "pt": "10000/100" },
        "000000730167_020278": { "ct": "250/5", "pt": "10000/100" },
        "000000730188_020278": { "ct": "250/5", "pt": "10000/100" },
        "000000730160_020278": { "ct": "500/5", "pt": "10000/100" },
        "000000730158_020278": { "ct": "500/5", "pt": "10000/100" },
        "000000730164_020278": { "ct": "250/5", "pt": "10000/100" },
        "000000730181_020278": { "ct": "250/5", "pt": "10000/100" },
        "000000730163_020278": { "ct": "250/5", "pt": "10000/100" },
        "000000730187_020278": { "ct": "250/5", "pt": "10000/100" },
        "000000730185_020278": { "ct": "250/5", "pt": "10000/100" },
        "000000730192_020278": { "ct": "800/5", "pt": "10000/100" },
        "000000730183_020278": { "ct": "800/5", "pt": "10000/100" },
        "000000730165_020278": { "ct": "800/5", "pt": "10000/100" },
        "000000730184_020278": { "ct": "800/5", "pt": "10000/100" },
        "000000010008_017836": { "ct": "600/5", "pt": "6000/100" }, // 姜家湾1200波特率
        "000000730191_017836": { "ct": "600/5", "pt": "6000/100" },
        "000000010002_017836": { "ct": "200/5", "pt": "6000/100" },
        "000000010010_017836": { "ct": "400/5", "pt": "6000/100" },
        "000000010027_017836": { "ct": "400/5", "pt": "6000/100" },
        "000000010085_017836": { "ct": "200/5", "pt": "6000/100" },
        "000000010029_017836": { "ct": "400/5", "pt": "6000/100" },
        "000000730186_017836": { "ct": "400/5", "pt": "6000/100" },
        "000000730182_017836": { "ct": "200/5", "pt": "6000/100" },
        "000000010001_017836": { "ct": "600/5", "pt": "6000/100" },
        "000000010030_017836": { "ct": "1000/5", "pt": "6000/100" },
        "000000010026_017836": { "ct": "400/5", "pt": "6000/100" },
        "000000010028_017836": { "ct": "600/5", "pt": "6000/100" },
        "000000010025_017836": { "ct": "400/5", "pt": "6000/100" },
        "000000010971_017836": { "ct": "800/5", "pt": "6000/100" },
        "000000010007_017836": { "ct": "400/5", "pt": "6000/100" },
        "000000010009_017836": { "ct": "800/5", "pt": "6000/100" },
        "000000010012_017836": { "ct": "400/5", "pt": "6000/100" },
        "000000010011_017836": { "ct": "400/5", "pt": "6000/100" },
        "000000010064_017836": { "ct": "200/5", "pt": "6000/100" },
        "000000010129_017836": { "ct": "400/5", "pt": "6000/100" },
        "000000010066_017836": { "ct": "400/5", "pt": "6000/100" },
        "000000010063_017836": { "ct": "400/5", "pt": "6000/100" },
        "000000010006_017836": { "ct": "600/5", "pt": "6000/100" },
        "000000010058_017836": { "ct": "400/5", "pt": "6000/100" },
        "000000010065_017836": { "ct": "800/5", "pt": "6000/100" },
        "000000010127_017836": { "ct": "600/5", "pt": "6000/100" },
        "000000010003_017836": { "ct": "600/5", "pt": "6000/100" },
        "000000010059_017836": { "ct": "600/5", "pt": "6000/100" },
        "000000010132_017836": { "ct": "400/5", "pt": "6000/100" },
        "000000010055_017836": { "ct": "400/5", "pt": "6000/100" },
        "000000010005_017836": { "ct": "400/5", "pt": "6000/100" },
        "000000010089_017836": { "ct": "200/5", "pt": "6000/100" },
        "000000010057_017836": { "ct": "200/5", "pt": "6000/100" },
        "000000010060_017836": { "ct": "200/5", "pt": "6000/100" },
        "000000010130_017836": { "ct": "200/5", "pt": "6000/100" },
        "000000730190_017836": { "ct": "800/5", "pt": "6000/100" },
        "000000010004_017836": { "ct": "50/5", "pt": "6000/100" },
        "000000010056_017836": { "ct": "200/5", "pt": "6000/100" },
        "000000010131_017836": { "ct": "600/5", "pt": "6000/100" },
        "000000010095_017836": { "ct": "400/5", "pt": "6000/100" },
        "000000730194_017836": { "ct": "200/5", "pt": "6000/100" },
        "000000010034_017836": { "ct": "200/5", "pt": "6000/100" },
        "000000730134_017836": { "ct": "200/5", "pt": "6000/100" },
        "000000000022_017836": { "ct": "200/5", "pt": "6000/100" },
        "000000000009_017836": { "ct": "800/5", "pt": "6000/100" },
        "000000000971_017836": { "ct": "800/5", "pt": "6000/100" },
        "000000000003_017836": { "ct": "600/5", "pt": "6000/100" },
        "000000000191_017836": { "ct": "600/5", "pt": "6000/100" },
        "000000000190_017836": { "ct": "800/5", "pt": "6000/100" },
        "000000000004_017836": { "ct": "50/5", "pt": "6000/100" },
        "000000010490_023493": { "ct": "2000/5", "pt": "6000/100" }, // 榆涧1200波特率
        "000000010502_023493": { "ct": "2000/5", "pt": "6000/100" },
        "000000010148_023493": { "ct": "600/5", "pt": "6000/100" },
        "000000010149_023493": { "ct": "600/5", "pt": "6000/100" },
        "000000010147_023493": { "ct": "100/5", "pt": "6000/100" },
        "000000000020_023493": { "ct": "600/5", "pt": "6000/100" },
        "000000040070_023493": { "ct": "100/5", "pt": "6000/100" },
        "000000010983_023493": { "ct": "100/5", "pt": "6000/100" },
        "000000010018_023493": { "ct": "600/5", "pt": "6000/100" },
        "000000010145_023493": { "ct": "100/5", "pt": "6000/100" },
        "000000010126_023493": { "ct": "100/5", "pt": "6000/100" },
        "000000010099_023493": { "ct": "600/5", "pt": "6000/100" },
        "000000010036_023493": { "ct": "600/5", "pt": "6000/100" },
        "000001561920_023493": { "ct": "200/5", "pt": "6000/100" },
        "000000010046_023493": { "ct": "600/5", "pt": "6000/100" },
        "000000040063_023493": { "ct": "100/5", "pt": "6000/100" },
        "000000010031_023493": { "ct": "600/5", "pt": "6000/100" },
        "000000000071_023493": { "ct": "100/5", "pt": "6000/100" },
        "000000010003_023493": { "ct": "100/5", "pt": "6000/100" },
        "000000010964_023493": { "ct": "100/5", "pt": "6000/100" },
        "000000730153_023493": { "ct": "200/5", "pt": "6000/100" },
        "000001530193_023493": { "ct": "600/5", "pt": "6000/100" },
        "000000003132_023493": { "ct": "600/5", "pt": "35000/100" },
        "000000003131_023493": { "ct": "600/5", "pt": "35000/100" },
        "000000003135_023493": { "ct": "400/5", "pt": "35000/100" },
        "000000000502_023493": { "ct": "2000/5", "pt": "6000/100" },
        "000000000490_023493": { "ct": "2000/5", "pt": "6000/100" },
        "000000000034_023493": { "ct": "600/5", "pt": "35000/100" },
        "000000000080_023493": { "ct": "600/5", "pt": "35000/100" },
        "000000804033_018889": { "ct": "400/5", "pt": "6000/100" }, // 晋华宫1200波特率
        "000000804049_018889": { "ct": "300/5", "pt": "6000/100" },
        "000000804022_018889": { "ct": "400/5", "pt": "6000/100" },
        "000000804011_018889": { "ct": "200/5", "pt": "6000/100" },
        "000000804021_018889": { "ct": "600/5", "pt": "6000/100" },
        "000002232313_018889": { "ct": "400/5", "pt": "6000/100" },
        "000000804028_018889": { "ct": "400/5", "pt": "6000/100" },
        "000000804012_018889": { "ct": "200/5", "pt": "6000/100" },
        "000000804046_018889": { "ct": "400/5", "pt": "6000/100" },
        "000000804035_018889": { "ct": "600/5", "pt": "6000/100" },
        "000000804037_018889": { "ct": "300/5", "pt": "6000/100" },
        "000000803999_018889": { "ct": "400/5", "pt": "6000/100" },
        "000000804006_018889": { "ct": "600∕5", "pt": "6000/100" },
        "000000804025_018889": { "ct": "50/5", "pt": "6000/100" },
        "000000803996_018889": { "ct": "200/5", "pt": "6000/100" },
        "000000803985_018889": { "ct": "600/5", "pt": "6000/100" },
        "000000804027_018889": { "ct": "400/5", "pt": "6000/100" },
        "000000804002_018889": { "ct": "300/5", "pt": "6000/100" },
        "000000804016_018889": { "ct": "200/5", "pt": "6000/100" },
        "000000000135_018889": { "ct": "150/5", "pt": "6000/100" },
        "000001530205_018889": { "ct": "400/5", "pt": "6000/100" },
        "000000010130_018889": { "ct": "800/5", "pt": "6000/100" },
        "000000804045_018889": { "ct": "400/5", "pt": "6000/100" },
        "000000803994_018889": { "ct": "400/5", "pt": "6000/100" },
        "000000804050_018889": { "ct": "400/5", "pt": "6000/100" },
        "000000804023_018889": { "ct": "200/5", "pt": "6000/100" },
        "000000803992_018889": { "ct": "600/5", "pt": "6000/100" },
        "000000803988_018889": { "ct": "300/5", "pt": "6000/100" },
        "000000804176_018889": { "ct": "75/5", "pt": "6000/100" },
        "000000340032_018889": { "ct": "400/5", "pt": "35000/100" },
        "000000010492_018889": { "ct": "400/5", "pt": "35000/100" },
        "000000000501_018889": { "ct": "2000/5", "pt": "6000/100" },
        "000000000059_018889": { "ct": "2000/5", "pt": "6000/100" },
        "000000000027_018889": { "ct": "600/5", "pt": "6000/100" },
        "000000801726_020194": { "ct": "400/5", "pt": "6000/100" }, // 四老沟1200波特率
        "000000010129_020194": { "ct": "200/5", "pt": "6000/100" },
        "000000801699_020194": { "ct": "400/5", "pt": "6000/100" },
        "000001561882_020194": { "ct": "150/5", "pt": "6000/100" },
        "000000804167_020194": { "ct": "50/5", "pt": "6000/100" },
        "000000801706_020194": { "ct": "750/5", "pt": "6000/100" },
        "000000801685_020194": { "ct": "400/5", "pt": "6000/100" },
        "000001561897_020194": { "ct": "600/5", "pt": "6000/100" },
        "000001561913_020194": { "ct": "200/5", "pt": "6000/100" },
        "000001162847_020194": { "ct": "200/5", "pt": "6000/100" },
        "000000801710_020194": { "ct": "400/5", "pt": "6000/100" },
        "000000801680_020194": { "ct": "400/5", "pt": "6000/100" },
        "000001162855_020194": { "ct": "800/5", "pt": "6000/100" },
        "000000801727_020194": { "ct": "800/5", "pt": "6000/100" },
        "000001162877_020194": { "ct": "600/5", "pt": "6000/100" },
        "000001162862_020194": { "ct": "750/5", "pt": "6000/100" },
        "000001561889_020194": { "ct": "800/5", "pt": "6000/100" },
        "000000801698_020194": { "ct": "800/5", "pt": "6000/100" },
        "000000801688_020194": { "ct": "600/5", "pt": "6000/100" },
        "000000801720_020194": { "ct": "100/5", "pt": "6000/100" },
        "000000000129_020194": { "ct": "200/5", "pt": "6000/100" },
        "000000630021_019259": { "ct": "2000/5", "pt": "10000/100" }, // 双井沟1200波特率
        "000000630022_019259": { "ct": "2000/5", "pt": "10000/100" },
        "000000010118_019259": { "ct": "300/5", "pt": "10000/100" },
        "000000010058_019259": { "ct": "100/5", "pt": "10000/100" },
        "000000010120_019259": { "ct": "500/5", "pt": "10000/100" },
        "000000010116_019259": { "ct": "300/5", "pt": "10000/100" },
        "000000010479_019259": { "ct": "400/5", "pt": "10000/100" },
        "000000010487_019259": { "ct": "400/5", "pt": "10000/100" },
        "000000010115_019259": { "ct": "200/5", "pt": "10000/100" },
        "000000010045_019259": { "ct": "600/5", "pt": "10000/100" },
        "000000010482_019259": { "ct": "100/5", "pt": "10000/100" },
        "000000010481_019259": { "ct": "100/5", "pt": "10000/100" },
        "000000010130_019259": { "ct": "300/5", "pt": "10000/100" },
        "000000010117_019259": { "ct": "100/5", "pt": "10000/100" },
        "000000010119_019259": { "ct": "300/5", "pt": "10000/100" },
        "000000010485_019259": { "ct": "300/5", "pt": "10000/100" },
        "000000010025_019259": { "ct": "400/5", "pt": "10000/100" },
        "000000010047_019259": { "ct": "400/5", "pt": "10000/100" },
        "000000804203_019259": { "ct": "100/5", "pt": "10000/100" },
        "000000010048_019259": { "ct": "600/5", "pt": "10000/100" },
        "000000010046_019259": { "ct": "200/5", "pt": "10000/100" },
        "000000010043_019259": { "ct": "100/5", "pt": "10000/100" },
        "000000000077_019259": { "ct": "600/5", "pt": "10000/100" },
        "000000000075_019259": { "ct": "600/5", "pt": "10000/100" },
        "000000010480_019259": { "ct": "100/5", "pt": "10000/100" },
        "000000010489_019259": { "ct": "100/5", "pt": "10000/100" },
        "000000802017_019259": { "ct": "500/5", "pt": "35000/100" },
        "000000802181_019259": { "ct": "500/5", "pt": "35000/100" },
        "000000250004_019259": { "ct": "500/5", "pt": "35000/100" },
        "000000250005_019259": { "ct": "500/5", "pt": "35000/100" },
        "000000010496_019400": { "ct": "2000/5", "pt": "10000/100" }, // 盘道1200波特率
        "000000010509_019400": { "ct": "2000/5", "pt": "10000/100" },
        "000000803989_019400": { "ct": "400/5", "pt": "10000/100" },
        "000000010961_019400": { "ct": "200/5", "pt": "10000/100" },
        "000000804029_019400": { "ct": "400/5", "pt": "10000/100" },
        "000000804020_019400": { "ct": "400/5", "pt": "10000/100" },
        "000000803930_019400": { "ct": "200/5", "pt": "10000/100" },
        "000000804039_019400": { "ct": "400/5", "pt": "10000/100" },
        "000000804048_019400": { "ct": "400/5", "pt": "10000/100" },
        "000000803998_019400": { "ct": "400/5", "pt": "10000/100" },
        "000000804030_019400": { "ct": "600/5", "pt": "10000/100" },
        "000000804001_019400": { "ct": "600/5", "pt": "10000/100" },
        "000000804004_019400": { "ct": "400/5", "pt": "10000/100" },
        "000000804013_019400": { "ct": "400/5", "pt": "10000/100" },
        "000000803991_019400": { "ct": "400/5", "pt": "10000/100" },
        "000000804036_019400": { "ct": "400/5", "pt": "10000/100" },
        "000000804047_019400": { "ct": "400/5", "pt": "10000/100" },
        "000000804038_019400": { "ct": "200/5", "pt": "10000/100" },
        "000000803986_019400": { "ct": "100/5", "pt": "10000/100" },
        "000000803954_019400": { "ct": "400/5", "pt": "10000/100" },
        "000000804154_019400": { "ct": "400/5", "pt": "10000/100" },
        "000000804160_019400": { "ct": "400/5", "pt": "10000/100" },
        "000000340035_019400": { "ct": "400/5", "pt": "35000/100" },
        "000000340067_019400": { "ct": "400/5", "pt": "35000/100" },
        "000000802187_019400": { "ct": "400/5", "pt": "35000/100" },
        "000000802174_019400": { "ct": "800/5", "pt": "35000/100" },
        "000000000035_019400": { "ct": "400/5", "pt": "35000/100" },
        "000000000067_019400": { "ct": "400/5", "pt": "35000/100" },
        "000001162875_022314": { "ct": "1500/5", "pt": "10000/100" }, // 秦家山1200波特率
        "000001162878_022314": { "ct": "1500/5", "pt": "10000/100" },
        "000001530196_022314": { "ct": "1/1", "pt": "10000/100" },
        "000001530200_022314": { "ct": "200/5", "pt": "10000/100" },
        "000001530240_022314": { "ct": "500/5", "pt": "10000/100" },
        "000001530232_022314": { "ct": "150/5", "pt": "10000/100" },
        "000001530242_022314": { "ct": "150/5", "pt": "10000/100" },
        "000001530230_022314": { "ct": "500/5", "pt": "10000/100" },
        "000001530241_022314": { "ct": "400/5", "pt": "10000/100" },
        "000001530204_022314": { "ct": "200/5", "pt": "10000/100" },
        "000001530192_022314": { "ct": "200/5", "pt": "10000/100" },
        "000001530231_022314": { "ct": "400/5", "pt": "10000/100" },
        "000001530189_022314": { "ct": "1/1", "pt": "10000/100" },
        "000001530195_022314": { "ct": "600/5", "pt": "10000/100" },
        "000001530234_022314": { "ct": "500/5", "pt": "10000/100" },
        "000001530233_022314": { "ct": "400/5", "pt": "10000/100" },
        "000001530191_022314": { "ct": "200/5", "pt": "10000/100" },
        "000001530228_022314": { "ct": "600/5", "pt": "10000/100" },
        "000000000001_022314": { "ct": "1/1", "pt": "10000/100" },
        "000000000004_022314": { "ct": "1/1", "pt": "10000/100" },
        "000000000008_022314": { "ct": "1/1", "pt": "10000/100" },
        "000001162851_022314": { "ct": "1/1", "pt": "35000/100" },
        "000001162860_022314": { "ct": "500/5", "pt": "35000/100" },
        "276636000003_022314": { "ct": "1/1", "pt": "35000/100" },
        "000001530264_022314": { "ct": "400/5", "pt": "10000/100" },
        "000000010065_018774": { "ct": "400/5", "pt": "6000/100" }, // 南羊路1200波特率
        "000000010073_018774": { "ct": "200/5", "pt": "6000/100" },
        "000000010075_018774": { "ct": "400/5", "pt": "6000/100" },
        "000000010074_018774": { "ct": "400/5", "pt": "6000/100" },
        "000000010076_018774": { "ct": "200/5", "pt": "6000/100" },
        "000000010134_018774": { "ct": "200/5", "pt": "6000/100" },
        "000000010062_018774": { "ct": "400/5", "pt": "6000/100" },
        "000000010064_018774": { "ct": "400/5", "pt": "6000/100" },
        "000000040097_018774": { "ct": "200/5", "pt": "6000/100" },
        "000000010143_018774": { "ct": "400/5", "pt": "6000/100" },
        "000000010144_018774": { "ct": "400/5", "pt": "6000/100" },
        "000000010141_018774": { "ct": "500/5", "pt": "6000/100" },
        "000000010506_018774": { "ct": "2000/5", "pt": "6000/100" },
        "000000010510_018774": { "ct": "2000/5", "pt": "6000/100" },
        "000000010024_018774": { "ct": "200/5", "pt": "6000/100" },
        "000000010019_018774": { "ct": "400/5", "pt": "6000/100" },
        "000000010139_018774": { "ct": "400/5", "pt": "6000/100" },
        "000000802201_018774": { "ct": "300/5", "pt": "35000/100" },
        "000001561884_018447": { "ct": "200/5", "pt": "10000/100" }, // 银塘沟1200波特率
        "000001665204_018447": { "ct": "200/5", "pt": "10000/100" },
        "000001561895_018447": { "ct": "150/5", "pt": "10000/100" },
        "000001561899_018447": { "ct": "600/5", "pt": "10000/100" },
        "000001561907_018447": { "ct": "500/5", "pt": "10000/100" },
        "000001665215_018447": { "ct": "200/5", "pt": "10000/100" },
        "000001561891_018447": { "ct": "200/5", "pt": "10000/100" },
        "000001561883_018447": { "ct": "150/5", "pt": "10000/100" },
        "000001665221_018447": { "ct": "400/5", "pt": "10000/100" },
        "000001665218_018447": { "ct": "200/5", "pt": "10000/100" },
        "000001665208_018447": { "ct": "500/5", "pt": "10000/100" },
        "000001665212_018447": { "ct": "150/5", "pt": "10000/100" },
        "000001665214_018447": { "ct": "200/5", "pt": "10000/100" },
        "000001665207_018447": { "ct": "600/5", "pt": "10000/100" },
        "000001665205_018447": { "ct": "400/5", "pt": "10000/100" },
        "000001665210_018447": { "ct": "200/5", "pt": "10000/100" },
        "000001665209_018447": { "ct": "200/5", "pt": "10000/100" },
        "000001665203_018447": { "ct": "600/5", "pt": "10000/100" },
        "000001665206_018447": { "ct": "500/5", "pt": "10000/100" },
        "000001561908_018447": { "ct": "150/5", "pt": "10000/100" },
        "000000000006_018447": { "ct": "1/1", "pt": "35000/100" },
        "000000000010_018447": { "ct": "1/1", "pt": "35000/100" },
        "000000000011_018447": { "ct": "1/1", "pt": "35000/100" },
        "000000000012_018447": { "ct": "1/1", "pt": "35000/100" },
        "000000000005_018447": { "ct": "500/5", "pt": "35000/100" },
        "000000000013_018447": { "ct": "1500/5", "pt": "10000/100" },
        "000000000009_018447": { "ct": "1500/5", "pt": "10000/100" },
        "000000000447_018323": { "ct": "1000/5", "pt": "10000/100" }, // 辛庄1200波特率
        "000000000452_018323": { "ct": "600/5", "pt": "10000/100" },
        "000000000474_018323": { "ct": "100/5", "pt": "10000/100" },
        "000000000459_018323": { "ct": "75/5", "pt": "10000/100" },
        "000000000456_018323": { "ct": "300/5", "pt": "10000/100" },
        "000000000460_018323": { "ct": "200/5", "pt": "10000/100" },
        "000000000448_018323": { "ct": "1000/5", "pt": "10000/100" },
        "000000000445_018323": { "ct": "600/5", "pt": "10000/100" },
        "000000000471_018323": { "ct": "200/5", "pt": "10000/100" },
        "000000000458_018323": { "ct": "200/5", "pt": "10000/100" },
        "000000000449_018323": { "ct": "100/5", "pt": "10000/100" },
        "000000000469_018323": { "ct": "200/5", "pt": "10000/100" },
        "000000000032_018323": { "ct": "300/5", "pt": "10000/100" },
        "000000000466_018323": { "ct": "300/5", "pt": "10000/100" },
        "000000000475_018323": { "ct": "100/5", "pt": "10000/100" },
        "000000000465_018323": { "ct": "75/5", "pt": "10000/100" },
        "000000000454_018323": { "ct": "600/5", "pt": "10000/100" },
        "000000000432_018323": { "ct": "300/5", "pt": "10000/100" },
        "000000000468_018323": { "ct": "200/5", "pt": "10000/100" },
        "000000000029_018323": { "ct": "600/5", "pt": "10000/100" },
        "000000000457_018323": { "ct": "1000/5", "pt": "10000/100" },
        "000000000446_018323": { "ct": "200/5", "pt": "10000/100" },
        "000000000472_018323": { "ct": "200/5", "pt": "10000/100" },
        "000000000467_018323": { "ct": "200/5", "pt": "10000/100" },
        "000000000455_018323": { "ct": "1000/5", "pt": "10000/100" },
        "000000000476_018323": { "ct": "200/5", "pt": "10000/100" },
        "000000000473_018323": { "ct": "1/1", "pt": "10000/100" },
        "000000000493_018323": { "ct": "600/5", "pt": "10000/100" },
        "000000000003_018323": { "ct": "1/1", "pt": "10000/100" },
        "000000000004_018323": { "ct": "1/1", "pt": "10000/100" },
        "000000000002_018323": { "ct": "1/1", "pt": "10000/100" },
        "000000000005_018323": { "ct": "1/1", "pt": "10000/100" },
        "000000000006_018323": { "ct": "1/1", "pt": "10000/100" },
        "000000000001_018323": { "ct": "600/5", "pt": "35000/100" },
        "000000000470_018323": { "ct": "3000/5", "pt": "10000/100" },
        "000000000453_018323": { "ct": "3000/5", "pt": "10000/100" },
        "000000010535_018270": { "ct": "2000/5", "pt": "6000/100" }, // 忻州窑1200波特率
        "000000010498_018270": { "ct": "2000/5", "pt": "6000/100" },
        "000000010039_018270": { "ct": "150/5", "pt": "6000/100" },
        "000000010040_018270": { "ct": "100/5", "pt": "6000/100" },
        "000000010042_018270": { "ct": "400/5", "pt": "6000/100" },
        "000000010105_018270": { "ct": "600/5", "pt": "6000/100" },
        "000000010108_018270": { "ct": "300/5", "pt": "6000/100" },
        "000000010103_018270": { "ct": "200/5", "pt": "6000/100" },
        "000000010049_018270": { "ct": "150/5", "pt": "6000/100" },
        "000000010104_018270": { "ct": "600/5", "pt": "6000/100" },
        "000000010107_018270": { "ct": "200/5", "pt": "6000/100" },
        "000000010112_018270": { "ct": "200/5", "pt": "6000/100" },
        "000000010041_018270": { "ct": "200/5", "pt": "6000/100" },
        "000000010038_018270": { "ct": "150/5", "pt": "6000/100" },
        "000000010113_018270": { "ct": "600/5", "pt": "6000/100" },
        "000000010109_018270": { "ct": "400/5", "pt": "6000/100" },
        "000000010111_018270": { "ct": "800/5", "pt": "6000/100" },
        "000000010114_018270": { "ct": "400/5", "pt": "6000/100" },
        "000000010037_018270": { "ct": "600/5", "pt": "6000/100" },
        "000000000001_018270": { "ct": "200/5", "pt": "6000/100" },
        "000000000010_018270": { "ct": "200/5", "pt": "6000/100" },
        "000000000111_018270": { "ct": "800/5", "pt": "6000/100" },
        "000000000049_018270": { "ct": "150/5", "pt": "6000/100" },
        "000000000498_018270": { "ct": "1200/5", "pt": "10000/100" },
        "000000000535_018270": { "ct": "1200/5", "pt": "10000/100" },
        "000000000082_018270": { "ct": "400/5", "pt": "35000/100" },
        "000000010499_019368": { "ct": "2000/5", "pt": "6000/100" }, // 辛村1200波特率
        "000000010500_019368": { "ct": "2000/5", "pt": "6000/100" },
        "000000010094_019368": { "ct": "400/5", "pt": "6000/100" },
        "000000010079_019368": { "ct": "400/5", "pt": "6000/100" },
        "000000010082_019368": { "ct": "400/5", "pt": "6000/100" },
        "000000010084_019368": { "ct": "400/5", "pt": "6000/100" },
        "000000010081_019368": { "ct": "400/5", "pt": "6000/100" },
        "000000010083_019368": { "ct": "400/5", "pt": "6000/100" },
        "000000010004_019368": { "ct": "600/5", "pt": "6000/100" },
        "000000010096_019368": { "ct": "600/5", "pt": "6000/100" },
        "000000010093_019368": { "ct": "200/5", "pt": "6000/100" },
        "000000040066_019368": { "ct": "400/5", "pt": "6000/100" },
        "000000000000_019368": { "ct": "1/1", "pt": "6000/100" },
        "000000010128_019368": { "ct": "600/5", "pt": "6000/100" },
        "000000010103_019368": { "ct": "400/5", "pt": "6000/100" },
        "000000010107_019368": { "ct": "400/5", "pt": "6000/100" },
        "000000010108_019368": { "ct": "400/5", "pt": "6000/100" },
        "000000010106_019368": { "ct": "400/5", "pt": "6000/100" },
        "000000010105_019368": { "ct": "300/5", "pt": "6000/100" },
        "000001530224_019368": { "ct": "400/5", "pt": "6000/100" },
        "000000730151_019368": { "ct": "150/5", "pt": "6000/100" },
        "000000010068_019368": { "ct": "150/5", "pt": "6000/100" },
        "000000010042_019368": { "ct": "400/5", "pt": "6000/100" },
        "000000000975_019368": { "ct": "90/10", "pt": "1000/100" },
        "000000000040_019368": { "ct": "90/10", "pt": "1000/100" },
        "000000000091_019368": { "ct": "400/5", "pt": "6000/100" },
        "000000000039_019368": { "ct": "400/5", "pt": "6000/100" },
        "000000010959_019368": { "ct": "400/5", "pt": "6000/100" },
        "000000010080_019368": { "ct": "200/5", "pt": "6000/100" },
        "000000010041_019368": { "ct": "200/5", "pt": "6000/100" },
        "000000000037_019368": { "ct": "600/5", "pt": "6000/100" },
        "000000000092_019368": { "ct": "600/5", "pt": "6000/100" },
        "000000000104_019368": { "ct": "200/5", "pt": "6000/100" },
        "000000000038_019368": { "ct": "400/5", "pt": "6000/100" },
        "000000010087_024110": { "ct": "300/5", "pt": "6000/100" }, // 罗家新窑1200波特率
        "000000010061_024110": { "ct": "150/5", "pt": "6000/100" },
        "000000010086_024110": { "ct": "600/5", "pt": "6000/100" },
        "000000010091_024110": { "ct": "1000/5", "pt": "6000/100" },
        "000000010094_024110": { "ct": "300/5", "pt": "6000/100" },
        "000000010096_024110": { "ct": "300/5", "pt": "6000/100" },
        "000000010090_024110": { "ct": "600/5", "pt": "6000/100" },
        "000000010088_024110": { "ct": "150/5", "pt": "6000/100" },
        "000000010092_024110": { "ct": "600/5", "pt": "6000/100" },
        "000000010093_024110": { "ct": "300/5", "pt": "6000/100" },
        "000000000185_024110": { "ct": "100/5", "pt": "6000/100" },
        "000000000065_024110": { "ct": "1000/5", "pt": "6000/100" },
        "000000000002_024110": { "ct": "200/5", "pt": "6000/100" },
        "000000000003_024110": { "ct": "400/5", "pt": "6000/100" },
        "000000000006_024110": { "ct": "200/5", "pt": "6000/100" },
        "000000000189_024110": { "ct": "1000/5", "pt": "6000/100" },
        "000000000187_024110": { "ct": "100/5", "pt": "6000/100" },
        "000000000044_024110": { "ct": "400/5", "pt": "6000/100" },
        "000000000004_024110": { "ct": "500/5", "pt": "6000/100" },
        "000000000001_024110": { "ct": "200/5", "pt": "6000/100" },
        "000000000048_024110": { "ct": "100/5", "pt": "6000/100" },
        "000000000005_024110": { "ct": "100/5", "pt": "6000/100" },
        "000000000524_024110": { "ct": "2000/5", "pt": "6000/100" },
        "000000000487_024110": { "ct": "2000/5", "pt": "6000/100" },
        "000000804043_019667": { "ct": "400/5", "pt": "6000/100" }, // 四台1200波特率
        "000000804032_019667": { "ct": "400/5", "pt": "6000/100" },
        "000000804017_019667": { "ct": "300/5", "pt": "6000/100" },
        "000000804040_019667": { "ct": "400/5", "pt": "6000/100" },
        "000000803990_019667": { "ct": "400/5", "pt": "6000/100" },
        "000000804173_019667": { "ct": "400/5", "pt": "6000/100" },
        "000000803932_019667": { "ct": "600/5", "pt": "6000/100" },
        "000000010974_019667": { "ct": "300/5", "pt": "6000/100" },
        "000000804026_019667": { "ct": "1000/5", "pt": "6000/100" },
        "000000804179_019667": { "ct": "200/5", "pt": "6000/100" },
        "000000804188_019667": { "ct": "400/5", "pt": "6000/100" },
        "000000804053_019667": { "ct": "200/5", "pt": "6000/100" },
        "000000804018_019667": { "ct": "400/5", "pt": "6000/100" },
        "000000803995_019667": { "ct": "200/5", "pt": "6000/100" },
        "000000010970_019667": { "ct": "400/5", "pt": "6000/100" },
        "000000804031_019667": { "ct": "200/5", "pt": "6000/100" },
        "000000804054_019667": { "ct": "400/5", "pt": "6000/100" },
        "000000803993_019667": { "ct": "400/5", "pt": "6000/100" },
        "000000804007_019667": { "ct": "400/5", "pt": "6000/100" },
        "000000804000_019667": { "ct": "400/5", "pt": "6000/100" },
        "000000804010_019667": { "ct": "600/5", "pt": "6000/100" },
        "000000804034_019667": { "ct": "1000/5", "pt": "6000/100" },
        "000000804163_019667": { "ct": "200/5", "pt": "6000/100" },
        "000000804185_019667": { "ct": "400/5", "pt": "6000/100" },
        "000000803987_019667": { "ct": "100/5", "pt": "6000/100" },
        "000000804014_019667": { "ct": "200/5", "pt": "6000/100" },
        "000000804008_019667": { "ct": "400/5", "pt": "6000/100" },
        "000000804019_019667": { "ct": "400/5", "pt": "6000/100" },
        "000000010960_019667": { "ct": "400/5", "pt": "6000/100" },
        "000000802060_019667": { "ct": "300/5", "pt": "35000/100" },
        "000000802054_019667": { "ct": "300/5", "pt": "35000/100" },
        "000000802014_019667": { "ct": "300/5", "pt": "35000/100" },
        "000000802039_019667": { "ct": "300/5", "pt": "35000/100" },
        "000000802168_019667": { "ct": "400/5", "pt": "35000/100" },
        "000000000972_019667": { "ct": "400/5", "pt": "6000/100" },
        "000000000477_019667": { "ct": "2000/5", "pt": "6000/100" },
        "000000000520_019667": { "ct": "2000/5", "pt": "6000/100" },
        "000000000072_019667": { "ct": "600/5", "pt": "6000/100" },
        "000000803945_026509": { "ct": "200/5", "pt": "6000/100" }, // 万家嘴1200波特率
        "000000803959_026509": { "ct": "300/5", "pt": "6000/100" },
        "000000803912_026509": { "ct": "200/5", "pt": "6000/100" },
        "000000803958_026509": { "ct": "500/5", "pt": "6000/100" },
        "000000804184_026509": { "ct": "300/5", "pt": "6000/100" },
        "000000804177_026509": { "ct": "300/5", "pt": "6000/100" },
        "000000803942_026509": { "ct": "500/5", "pt": "6000/100" },
        "000000803964_026509": { "ct": "200/5", "pt": "6000/100" },
        "000000803908_026509": { "ct": "300/5", "pt": "6000/100" },
        "000000804155_026509": { "ct": "600/5", "pt": "6000/100" },
        "000000804166_026509": { "ct": "200/5", "pt": "6000/100" },
        "000000804178_026509": { "ct": "600/5", "pt": "6000/100" },
        "000000804193_026509": { "ct": "600/5", "pt": "6000/100" },
        "000001330605_026509": { "ct": "400/5", "pt": "6000/100" },
        "000001330606_026509": { "ct": "400/5", "pt": "6000/100" },
        "000001330604_026509": { "ct": "150/5", "pt": "6000/100" },
        "000001330610_026509": { "ct": "100/5", "pt": "6000/100" },
        "000001330609_026509": { "ct": "100/5", "pt": "6000/100" },
        "000001330607_026509": { "ct": "400/5", "pt": "6000/100" },
        "000001330608_026509": { "ct": "400/5", "pt": "6000/100" },
        "000000000478_026509": { "ct": "2500/5", "pt": "6000/100" },
        "000000000523_026509": { "ct": "2500/5", "pt": "6000/100" },
        "000000802200_026509": { "ct": "400/5", "pt": "35000/100" },
        "000001131323_021838": { "ct": "150/5", "pt": "6000/100" }, // 集运站1200波特率
        "000000010968_021838": { "ct": "600/5", "pt": "6000/100" },
        "000000000047_021838": { "ct": "300/5", "pt": "6000/100" },
        "000000000036_021838": { "ct": "600/5", "pt": "6000/100" },
        "000000000035_021838": { "ct": "500/5", "pt": "6000/100" },
        "000000803953_021838": { "ct": "400/5", "pt": "6000/100" },
        "000000000095_021838": { "ct": "200/5", "pt": "6000/100" },
        "000001131338_021838": { "ct": "100/5", "pt": "6000/100" },
        "000000803909_021838": { "ct": "300/5", "pt": "6000/100" },
        "000000803911_021838": { "ct": "600/5", "pt": "6000/100" },
        "000000803946_021838": { "ct": "300/5", "pt": "6000/100" },
        "000000000045_021838": { "ct": "600/5", "pt": "6000/100" },
        "000000000046_021838": { "ct": "600/5", "pt": "6000/100" },
        "000000000048_021838": { "ct": "300/5", "pt": "6000/100" },
        "000000803948_021838": { "ct": "100/5", "pt": "6000/100" },
        "000000801703_021838": { "ct": "200/5", "pt": "6000/100" },
        "000000801711_021838": { "ct": "400/5", "pt": "6000/100" },
        "000000803943_021838": { "ct": "600/5", "pt": "6000/100" },
        "000000000005_021838": { "ct": "200/5", "pt": "6000/100" },
        "000000000003_021838": { "ct": "150/5", "pt": "6000/100" },
        "000000000004_021838": { "ct": "500/5", "pt": "6000/100" },
        "000000000002_021838": { "ct": "100/5", "pt": "6000/100" },
        "000000010525_021838": { "ct": "2500/5", "pt": "6000/100" },
        "000000010519_021838": { "ct": "2500/5", "pt": "6000/100" },
        "000000000040_021838": { "ct": "500/5", "pt": "6000/100" },
        "511001000005_021838": { "ct": "200/5", "pt": "6000/100" },
        "000000000525_021838": { "ct": "2500/5", "pt": "6000/100" },
        "000000000519_021838": { "ct": "2500/5", "pt": "6000/100" },
        "000001561905_024740": { "ct": "200/5", "pt": "6000/100" }, // 纸房头1200波特率
        "000001561893_024740": { "ct": "200/5", "pt": "6000/100" },
        "000001561890_024740": { "ct": "1/1", "pt": "6000/100" },
        "000001561916_024740": { "ct": "1/1", "pt": "6000/100" },
        "000001561909_024740": { "ct": "1/1", "pt": "6000/100" },
        "000001561896_024740": { "ct": "200/5", "pt": "6000/100" },
        "000001561888_024740": { "ct": "800/5", "pt": "6000/100" },
        "000001561921_024740": { "ct": "1/1", "pt": "6000/100" },
        "000001561918_024740": { "ct": "200/5", "pt": "6000/100" },
        "000001561904_024740": { "ct": "200/5", "pt": "6000/100" },
        "000001561903_024740": { "ct": "200/5", "pt": "6000/100" },
        "000001561901_024740": { "ct": "200/5", "pt": "6000/100" },
        "000001561914_024740": { "ct": "1/1", "pt": "6000/100" },
        "000001561911_024740": { "ct": "1/1", "pt": "6000/100" },
        "000001561898_024740": { "ct": "1/1", "pt": "6000/100" },
        "000001561915_024740": { "ct": "600/5", "pt": "6000/100" },
        "000001561902_024740": { "ct": "800/5", "pt": "6000/100" },
        "000001561917_024740": { "ct": "200/5", "pt": "6000/100" },
        "000001561892_024740": { "ct": "1/1", "pt": "6000/100" },
        "000001561900_024740": { "ct": "200/5", "pt": "6000/100" },
        "000000000087_024740": { "ct": "800/5", "pt": "35000/100" },
        "000000000198_024740": { "ct": "800/5", "pt": "35000/100" },
        "000001561912_024740": { "ct": "200/5", "pt": "6000/100" },
        "000001665220_024740": { "ct": "3000/5", "pt": "6000/100" },
        "000001665219_024740": { "ct": "3000/5", "pt": "6000/100" },
        "000000030252_022220": { "ct": "1200/5", "pt": "110000/100" }, // 夏家河1200波特率
        "000000020383_022220": { "ct": "1200/5", "pt": "110000/100" },
        "000002232202_022220": { "ct": "1/1", "pt": "110000/100" },
        "000000630020_022220": { "ct": "1200/5", "pt": "35000/100" },
        "000000630019_022220": { "ct": "1200/5", "pt": "35000/100" },
        "000000670702_022220": { "ct": "600/5", "pt": "35000/100" },
        "000000670697_022220": { "ct": "600/5", "pt": "35000/100" },
        "000000670689_022220": { "ct": "600/5", "pt": "35000/100" },
        "000000000015_022220": { "ct": "300/5", "pt": "35000/100" },
        "000000000418_022220": { "ct": "600/5", "pt": "35000/100" },
        "000000670700_022220": { "ct": "800/5", "pt": "10000/100" },
        "000000400004_022220": { "ct": "800/5", "pt": "10000/100" },
        "000000670707_022220": { "ct": "800/5", "pt": "10000/100" },
        "000000670701_022220": { "ct": "800/5", "pt": "10000/100" },
        "000000400001_022220": { "ct": "400/5", "pt": "10000/100" },
        "000000670690_022220": { "ct": "400/5", "pt": "10000/100" },
        "000000670703_022220": { "ct": "800/5", "pt": "10000/100" },
        "000000670741_022220": { "ct": "800/5", "pt": "10000/100" },
        "000000670704_022220": { "ct": "800/5", "pt": "10000/100" },
        "000000670688_022220": { "ct": "800/5", "pt": "10000/100" },
        "000000670706_022220": { "ct": "800/5", "pt": "10000/100" },
        "000000670699_022220": { "ct": "800/5", "pt": "10000/100" },
        "000000670686_022220": { "ct": "400/5", "pt": "10000/100" },
        "000000670708_022220": { "ct": "400/5", "pt": "10000/100" },
        "000000400003_022220": { "ct": "800/5", "pt": "10000/100" },
        "000000400002_022220": { "ct": "800/5", "pt": "10000/100" },
        "000000670740_022220": { "ct": "800/5", "pt": "10000/100" },
        "000000670668_022220": { "ct": "800/5", "pt": "10000/100" },
        "000000290346_022220": { "ct": "600/5", "pt": "10000/100" },
        "000000000018_022220": { "ct": "300/5", "pt": "10000/100" },
        "000000910016_022220": { "ct": "150/5", "pt": "10000/100" },
        "000000670669_022220": { "ct": "300/5", "pt": "10000/100" },
        "000000000009_022220": { "ct": "300/5", "pt": "10000/100" },
        "000000290327_022220": { "ct": "400/5", "pt": "10000/100" },
        "000000000958_022220": { "ct": "800/5", "pt": "10000/100" },
        "000000290316_022220": { "ct": "800/5", "pt": "10000/100" },
        "000000000017_022220": { "ct": "300/5", "pt": "10000/100" },
        "000000000041_022220": { "ct": "300/5", "pt": "10000/100" },
        "000000670724_022220": { "ct": "400/5", "pt": "10000/100" },
        "000000290365_022220": { "ct": "300/5", "pt": "10000/100" },
        "000000670667_022220": { "ct": "600/5", "pt": "10000/100" },
        "000000000008_022220": { "ct": "300/5", "pt": "10000/100" },
        "000000020382_022220": { "ct": "1200/5", "pt": "110000/100" },
        "000000030245_022220": { "ct": "1200/5", "pt": "110000/100" },
        "000000000014_022220": { "ct": "4000/5", "pt": "10000/100" },
        "000000000685_022220": { "ct": "4000/5", "pt": "10000/100" },
        "000000000434_022220": { "ct": "600/5", "pt": "35000/100" },
        "000000400016_028115": { "ct": "150/5", "pt": "10000/100" }, // 同发1200波特率
        "000000250002_028115": { "ct": "400/5", "pt": "10000/100" },
        "000000400007_028115": { "ct": "200/5", "pt": "10000/100" },
        "000000400020_028115": { "ct": "800/5", "pt": "10000/100" },
        "000000400019_028115": { "ct": "600/5", "pt": "10000/100" },
        "000000400008_028115": { "ct": "800/5", "pt": "10000/100" },
        "000000400004_028115": { "ct": "400/5", "pt": "10000/100" },
        "000000400013_028115": { "ct": "500/5", "pt": "10000/100" },
        "000000400011_028115": { "ct": "500/5", "pt": "10000/100" },
        "000000040082_028115": { "ct": "150/5", "pt": "10000/100" },
        "000000400018_028115": { "ct": "400/5", "pt": "10000/100" },
        "000000400005_028115": { "ct": "200/5", "pt": "10000/100" },
        "000000400010_028115": { "ct": "800/5", "pt": "10000/100" },
        "000000400021_028115": { "ct": "600/5", "pt": "10000/100" },
        "000000400014_028115": { "ct": "800/5", "pt": "10000/100" },
        "000001131307_028115": { "ct": "200/5", "pt": "10000/100" },
        "000000400001_028115": { "ct": "500/5", "pt": "10000/100" },
        "000000400017_028115": { "ct": "500/5", "pt": "10000/100" },
        "000000000045_028115": { "ct": "400/5", "pt": "10000/100" },
        "000000400006_028115": { "ct": "100/5", "pt": "10000/100" },
        "000000000143_028115": { "ct": "200/5", "pt": "10000/100" },
        "000000400015_028115": { "ct": "100/5", "pt": "10000/100" },
        "000000000083_028115": { "ct": "800/5", "pt": "10000/100" },
        "000000000003_028115": { "ct": "800/5", "pt": "10000/100" },
        "000000009362_028115": { "ct": "200/5", "pt": "10000/100" },
        "000000009363_028115": { "ct": "200/5", "pt": "10000/100" },
        "000000009361_028115": { "ct": "200/5", "pt": "10000/100" },
        "000000670698_028115": { "ct": "600/5", "pt": "35000/100" },
        "000000670705_028115": { "ct": "600/5", "pt": "35000/100" },
        "000000000687_028115": { "ct": "2500/5", "pt": "10000/100" },
        "000000000009_028115": { "ct": "2500/5", "pt": "10000/100" },
        "000000000981_021606": { "ct": "200/5", "pt": "6000/100" }, // 挖金湾1200波特率
        "000000010145_021606": { "ct": "200/5", "pt": "6000/100" },
        "000000010146_021606": { "ct": "400/5", "pt": "6000/100" },
        "000000000142_021606": { "ct": "300/5", "pt": "6000/100" },
        "000000010148_021606": { "ct": "200/5", "pt": "6000/100" },
        "000000010147_021606": { "ct": "600/5", "pt": "6000/100" },
        "000000010132_021606": { "ct": "100/5", "pt": "6000/100" },
        "000000010150_021606": { "ct": "600/5", "pt": "6000/100" },
        "000000010010_021606": { "ct": "400/5", "pt": "6000/100" },
        "000000010009_021606": { "ct": "200/5", "pt": "6000/100" },
        "000000010011_021606": { "ct": "75/5", "pt": "6000/100" },
        "000000010008_021606": { "ct": "300/5", "pt": "6000/100" },
        "000000010149_021606": { "ct": "200/5", "pt": "6000/100" },
        "000000000138_021606": { "ct": "200/5", "pt": "6000/100" },
        "000000000198_021606": { "ct": "600/5", "pt": "6000/100" },
        "000000000196_021606": { "ct": "50/5", "pt": "6000/100" },
        "000000000193_021606": { "ct": "50/5", "pt": "6000/100" },
        "000000000530_020649": { "ct": "1500/5", "pt": "6000/100" }, // 王村1200波特率
        "000000000507_020649": { "ct": "1500/5", "pt": "6000/100" },
        "000000000005_020649": { "ct": "400/5", "pt": "6000/100" },
        "000000000002_020649": { "ct": "400/5", "pt": "6000/100" },
        "000000000006_020649": { "ct": "200/5", "pt": "6000/100" },
        "000000000125_020649": { "ct": "300/5", "pt": "6000/100" },
        "000000000133_020649": { "ct": "75/5", "pt": "6000/100" },
        "000000000126_020649": { "ct": "400/5", "pt": "6000/100" },
        "000000000976_020649": { "ct": "100/5", "pt": "6000/100" },
        "000000000138_020649": { "ct": "400/5", "pt": "6000/100" },
        "000000000134_020649": { "ct": "600/5", "pt": "6000/100" },
        "000000000101_020649": { "ct": "50/5", "pt": "6000/100" },
        "000000000979_020649": { "ct": "200/5", "pt": "6000/100" },
        "000000000135_020649": { "ct": "600/5", "pt": "6000/100" },
        "000000000137_020649": { "ct": "100/5", "pt": "6000/100" },
        "000000000122_020649": { "ct": "400/5", "pt": "6000/100" },
        "000000000136_020649": { "ct": "75/5", "pt": "6000/100" },
        "000000000121_020649": { "ct": "300/5", "pt": "6000/100" },
        "000000000057_020649": { "ct": "30/5", "pt": "6000/100" },
        "000000000001_020649": { "ct": "400/5", "pt": "6000/100" },
        "000000000124_020649": { "ct": "400/5", "pt": "6000/100" },
        "000000000123_020649": { "ct": "400/5", "pt": "6000/100" },
        "000000000486_020649": { "ct": "200/5", "pt": "6000/100" },
        "000000804183_023210": { "ct": "600/5", "pt": "10000/100" }, // 金庄1200波特率
        "000001131313_023210": { "ct": "1/1", "pt": "10000/100" },
        "000001131319_023210": { "ct": "600/5", "pt": "10000/100" },
        "000001131330_023210": { "ct": "600/5", "pt": "10000/100" },
        "000001131327_023210": { "ct": "150/5", "pt": "10000/100" },
        "000001131328_023210": { "ct": "150/5", "pt": "10000/100" },
        "000001131331_023210": { "ct": "600/5", "pt": "10000/100" },
        "000001131324_023210": { "ct": "600/5", "pt": "10000/100" },
        "000001131316_023210": { "ct": "400/5", "pt": "10000/100" },
        "000001131322_023210": { "ct": "600/5", "pt": "10000/100" },
        "000001131317_023210": { "ct": "600/5", "pt": "10000/100" },
        "000001131325_023210": { "ct": "600/5", "pt": "10000/100" },
        "000001131339_023210": { "ct": "400/5", "pt": "10000/100" },
        "000001131321_023210": { "ct": "400/5", "pt": "10000/100" },
        "000001131329_023210": { "ct": "600/5", "pt": "10000/100" },
        "000001131340_023210": { "ct": "600/5", "pt": "10000/100" },
        "000000804009_023210": { "ct": "600/5", "pt": "10000/100" },
        "000001131332_023210": { "ct": "600/5", "pt": "10000/100" },
        "000000910013_023210": { "ct": "2500/5", "pt": "10000/100" },
        "000000440009_023210": { "ct": "2500/5", "pt": "10000/100" },
        "000001131311_023210": { "ct": "600/5", "pt": "10000/100" },
        "000001131312_023210": { "ct": "600/5", "pt": "10000/100" },
        "000001131320_023210": { "ct": "600/5", "pt": "10000/100" },
        "000001131314_023210": { "ct": "600/5", "pt": "10000/100" },
        "000001131326_023210": { "ct": "600/5", "pt": "10000/100" },
        "000000000019_023210": { "ct": "600/5", "pt": "10000/100" },
        "000001131333_023210": { "ct": "400/5", "pt": "10000/100" },
        "000001131335_023210": { "ct": "400/5", "pt": "10000/100" },
        "000000440030_023210": { "ct": "600/5", "pt": "35000/100" },
        "000000910017_023210": { "ct": "600/5", "pt": "35000/100" },
        "000000920055_023210": { "ct": "600/5", "pt": "35000/100" },
        "000000440011_023210": { "ct": "600/5", "pt": "35000/100" },
        "000000000040_023350": { "ct": "800/5", "pt": "10000/100" }, // 向阳寨1200波特率
        "000000000042_023350": { "ct": "800/5", "pt": "10000/100" },
        "000000000483_023350": { "ct": "800/5", "pt": "10000/100" },
        "000000000016_023350": { "ct": "1/1", "pt": "10000/100" },
        "000000000186_023350": { "ct": "1/1", "pt": "10000/100" },
        "000000000007_023350": { "ct": "1/1", "pt": "10000/100" },
        "000000910015_023350": { "ct": "1/1", "pt": "10000/100" },
        "000000000118_023350": { "ct": "1/1", "pt": "10000/100" },
        "000000000038_023350": { "ct": "1/1", "pt": "10000/100" },
        "000000000160_023350": { "ct": "1/1", "pt": "10000/100" },
        "000000000091_023350": { "ct": "1/1", "pt": "10000/100" },
        "000000000001_023350": { "ct": "1/1", "pt": "10000/100" },
        "000000000145_023350": { "ct": "1/1", "pt": "10000/100" },
        "000000000132_023350": { "ct": "1/1", "pt": "10000/100" },
        "000000000128_023350": { "ct": "1/1", "pt": "10000/100" },
        "000000000184_023350": { "ct": "800/5", "pt": "10000/100" },
        "000000000100_023350": { "ct": "800/5", "pt": "10000/100" },
        "000000000129_023350": { "ct": "800/5", "pt": "10000/100" },
        "000000000152_023350": { "ct": "800/5", "pt": "10000/100" },
        "000000000182_023350": { "ct": "1/1", "pt": "10000/100" },
        "000000000183_023350": { "ct": "1/1", "pt": "10000/100" },
        "000000000149_023350": { "ct": "1/1", "pt": "10000/100" },
        "000000290294_023350": { "ct": "1/1", "pt": "10000/100" },
        "000000000158_023350": { "ct": "800/5", "pt": "10000/100" },
        "000000131299_023350": { "ct": "1/1", "pt": "10000/100" },
        "000000131300_023350": { "ct": "1/1", "pt": "10000/100" },
        "000000290313_023350": { "ct": "4000/5", "pt": "10000/100" },
        "000000000011_023350": { "ct": "800/5", "pt": "10000/100" },
        "000000000044_023350": { "ct": "1/1", "pt": "10000/100" },
        "000000250003_023350": { "ct": "1/1", "pt": "10000/100" },
        "000000000048_023350": { "ct": "1/1", "pt": "10000/100" },
        "000000400002_023350": { "ct": "1/1", "pt": "10000/100" },
        "000000290293_023350": { "ct": "800/5", "pt": "10000/100" },
        "000000440029_023350": { "ct": "1/1", "pt": "35000/100" },
        "000000290291_023350": { "ct": "400/5", "pt": "35000/100" },
        "000000910014_023350": { "ct": "400/5", "pt": "35000/100" },
        "000001162850_023350": { "ct": "600/5", "pt": "35000/100" },
        "000001162874_023350": { "ct": "600/5", "pt": "35000/100" },
        "000000000006_023350": { "ct": "1200/5", "pt": "110000/100" },
        "000000000005_023350": { "ct": "1200/5", "pt": "110000/100" },
        "000000000010_023350": { "ct": "1200/5", "pt": "110000/100" },
        "000000000093_023350": { "ct": "1500/5", "pt": "35000/100" },
        "000000000092_023350": { "ct": "1500/5", "pt": "35000/100" },
        "000000000313_023350": { "ct": "4000/5", "pt": "10000/100" },
        "000000000094_023350": { "ct": "4000/5", "pt": "10000/100" },
        "000000000277_023350": { "ct": "1000/5", "pt": "10000/100" },
        "000000000181_023350": { "ct": "800/5", "pt": "10000/100" },
        "000000040058_026800": { "ct": "1000/5", "pt": "35000/100" }, // 新区站1200波特率
        "000000040081_026800": { "ct": "1000/5", "pt": "35000/100" },
        "000000802024_026800": { "ct": "600/5", "pt": "35000/100" },
        "000000802164_026800": { "ct": "600/5", "pt": "35000/100" },
        "000000010541_026800": { "ct": "2500/5", "pt": "6000/100" },
        "000000010086_026800": { "ct": "400/5", "pt": "6000/100" },
        "000000010109_026800": { "ct": "400/5", "pt": "6000/100" },
        "000000010085_026800": { "ct": "400/5", "pt": "6000/100" },
        "000000010090_026800": { "ct": "400/5", "pt": "6000/100" },
        "000000010111_026800": { "ct": "400/5", "pt": "6000/100" },
        "000000040072_026800": { "ct": "150/5", "pt": "6000/100" },
        "000000010114_026800": { "ct": "600/5", "pt": "6000/100" },
        "000000010131_026800": { "ct": "400/5", "pt": "6000/100" },
        "000000010055_026800": { "ct": "400/5", "pt": "6000/100" },
        "000000010060_026800": { "ct": "400/5", "pt": "6000/100" },
        "000000010056_026800": { "ct": "400/5", "pt": "6000/100" },
        "000000010110_026800": { "ct": "400/5", "pt": "6000/100" },
        "000000010112_026800": { "ct": "200/5", "pt": "6000/100" },
        "000000010089_026800": { "ct": "400/5", "pt": "6000/100" },
        "000000010098_026800": { "ct": "150/5", "pt": "6000/100" },
        "000000010029_026800": { "ct": "200/5", "pt": "6000/100" },
        "000000010113_026800": { "ct": "400/5", "pt": "6000/100" },
        "000000010088_026800": { "ct": "600/5", "pt": "6000/100" },
        "000000010982_026800": { "ct": "400/5", "pt": "6000/100" },
        "000000000029_026800": { "ct": "200/5", "pt": "6000/100" },
        "000000000982_026800": { "ct": "400/5", "pt": "6000/100" },
        "000000000058_026800": { "ct": "1000/5", "pt": "35000/100" },
        "000000000081_026800": { "ct": "1000/5", "pt": "35000/100" },
        "000000000343_026424": { "ct": "1000/5", "pt": "10000/100" }, // 金鼎站1200波特率
        "000000000259_026424": { "ct": "1000/5", "pt": "10000/100" },
        "000000802196_026424": { "ct": "150/5", "pt": "10000/100" },
        "000001162853_026424": { "ct": "150/5", "pt": "10000/100" },
        "000001162873_026424": { "ct": "150/5", "pt": "10000/100" },
        "000001162849_026424": { "ct": "150/5", "pt": "10000/100" },
        "000001162857_026424": { "ct": "150/5", "pt": "10000/100" },
        "3741302069230_026424": { "ct": "150/5", "pt": "10000/100" },
        "3741303071279_026424": { "ct": "150/5", "pt": "10000/100" },
        "3741302069234_026424": { "ct": "150/5", "pt": "10000/100" },
        "3741303071288_026424": { "ct": "150/5", "pt": "10000/100" },
        "3741303071289_026424": { "ct": "200/5", "pt": "10000/100" },
        "000001162869_026424": { "ct": "200/5", "pt": "10000/100" },
        "000001162845_026424": { "ct": "150/5", "pt": "10000/100" },
        "3741303071275_026424": { "ct": "150/5", "pt": "10000/100" },
        "3741303071287_026424": { "ct": "150/5", "pt": "10000/100" },
        "3741302069233_026424": { "ct": "150/5", "pt": "10000/100" },
        "3741303071290_026424": { "ct": "150/5", "pt": "10000/100" },
        "001213005498_026424": { "ct": "75/5", "pt": "10000/100" },
        "000001162868_026424": { "ct": "300/5", "pt": "35000/100" },
        "000001162865_026424": { "ct": "300/5", "pt": "35000/100" },
        "000000440095_026756": { "ct": "400/5", "pt": "110000/100" }, // 东肖河变电站1200波特率
        "000000440087_026756": { "ct": "300/5", "pt": "110000/100" },
        "000000440093_026756": { "ct": "300/5", "pt": "110000/100" },
        "000000000005_026756": { "ct": "3000/5", "pt": "10000/100" },
        "000000000004_026756": { "ct": "3000/5", "pt": "10000/100" },
        "000000000062_026756": { "ct": "100/5", "pt": "10000/100" },
        "000000000068_026756": { "ct": "100/5", "pt": "10000/100" },
        "000000000079_026756": { "ct": "200/5", "pt": "10000/100" },
        "000000000067_026756": { "ct": "800/5", "pt": "10000/100" },
        "000000000061_026756": { "ct": "50/5", "pt": "10000/100" },
        "000000000076_026756": { "ct": "150/5", "pt": "10000/100" },
        "000000000066_026756": { "ct": "500/5", "pt": "10000/100" },
        "000000000064_026756": { "ct": "600/5", "pt": "10000/100" },
        "000000000065_026756": { "ct": "1600/5", "pt": "10000/100" },
        "000000000077_026756": { "ct": "1000/5", "pt": "10000/100" },
        "000000000080_026756": { "ct": "100/5", "pt": "10000/100" },
        "000000000052_026756": { "ct": "150/5", "pt": "10000/100" },
        "000000000075_026756": { "ct": "200/5", "pt": "10000/100" },
        "000000000049_026756": { "ct": "100/5", "pt": "10000/100" },
        "000000000078_026756": { "ct": "150/5", "pt": "10000/100" },
        "000000000051_026756": { "ct": "500/5", "pt": "10000/100" },
        "000000000050_026756": { "ct": "600/5", "pt": "10000/100" },
        "000000000073_026756": { "ct": "800/5", "pt": "10000/100" },
        "000000000063_026756": { "ct": "1600/5", "pt": "10000/100" },
        "000000000074_026756": { "ct": "1000/5", "pt": "10000/100" },
        "000000000007_026756": { "ct": "400/5", "pt": "110000/100" },
        "000000000095_026756": { "ct": "400/5", "pt": "110000/100" },
        "000001961945_017659": { "ct": "4000/5", "pt": "10000/100" }, // 北羊路2400波特率
        "245384000007_017659": { "ct": "4000/5", "pt": "10000/100" },
        "250306000004_017659": { "ct": "4000/5", "pt": "10000/100" },
        "000002230389_017659": { "ct": "800/5", "pt": "35000/100" },
        "000002230391_017659": { "ct": "800/5", "pt": "35000/100" },
        "250313000004_017659": { "ct": "800/5", "pt": "35000/100" },
        "250306000003_017659": { "ct": "800/5", "pt": "35000/100" },
        "250306000001_017659": { "ct": "800/5", "pt": "35000/100" },
        "000002232183_017659": { "ct": "300/5", "pt": "110000/100" },
        "002104410903_017659": { "ct": "600/5", "pt": "110000/100" },
        "000002232337_017659": { "ct": "4000/5", "pt": "10000/100" },
        "000002233428_017659": { "ct": "4000/5", "pt": "10000/100" },
        "000002232389_017659": { "ct": "800/5", "pt": "35000/100" },
        "000002232303_017659": { "ct": "800/5", "pt": "35000/100" },
        "000002232464_017458": { "ct": "200/5", "pt": "6000/100" }, // 晋华宫2400波特率
        "000002232433_017458": { "ct": "300/5", "pt": "600/100" },
        "000002232367_017458": { "ct": "200/5", "pt": "6000/100" },
        "000002230318_017458": { "ct": "100/5", "pt": "6000/100" },
        "000002230357_017458": { "ct": "400/5", "pt": "6000/100" },
        "000002232385_014779": { "ct": "100/5", "pt": "6000/100" }, // 岩岭2400波特率
        "000002232436_014779": { "ct": "100/5", "pt": "6000/100" },
        "000002232370_014779": { "ct": "1000/5", "pt": "6000/100" },
        "000002230381_014779": { "ct": "1000/5", "pt": "6000/100" },
        "000002232463_014779": { "ct": "400/5", "pt": "6000/100" },
        "000002232314_014779": { "ct": "500/5", "pt": "6000/100" },
        "000002232406_014779": { "ct": "600/5", "pt": "6000/100" },
        "000002232438_014779": { "ct": "500/5", "pt": "6000/100" },
        "000002230316_014779": { "ct": "600/5", "pt": "6000/100" },
        "000002232395_014779": { "ct": "600/5", "pt": "6000/100" },
        "000002232327_014779": { "ct": "1000/5", "pt": "6000/100" },
        "000002232297_014779": { "ct": "1000/5", "pt": "6000/100" },
        "000002232398_014779": { "ct": "300/5", "pt": "6000/100" },
        "000002232347_014779": { "ct": "300/5", "pt": "6000/100" },
        "000002232325_014779": { "ct": "100/5", "pt": "6000/100" },
        "000002232429_014779": { "ct": "200/5", "pt": "6000/100" },
        "000002232341_014779": { "ct": "1000/5", "pt": "35000/100" },
        "000002232348_015180": { "ct": "100/5", "pt": "6000/100" }, // 平旺站2400波特率
        "000002232415_015180": { "ct": "200/5", "pt": "6000/100" },
        "000002230376_026357": { "ct": "300/5", "pt": "10000/100" }, // 金鼎站2400波特率
        "000002230306_026357": { "ct": "300/5", "pt": "10000/100" },
        "000002230312_026357": { "ct": "300/5", "pt": "10000/100" },
        "000002230337_017111": { "ct": "1000/5", "pt": "6000/100" }, // 马脊梁2400波特率
        "000002230322_017111": { "ct": "1000/5", "pt": "6000/100" },
        "000002232424_028097": { "ct": "400/5", "pt": "6000/100" }, // 机厂2400波特率
        "000002230292_028097": { "ct": "600/5", "pt": "35000/100" },
        "510767000046_028097": { "ct": "600/5", "pt": "35000/100" },
        "510767000041_028097": { "ct": "1000/5", "pt": "35000/100" },
        "000002230361_012564": { "ct": "600/5", "pt": "6000/100" }, // 煤峪口2400波特率
        "000000000965_018774": { "ct": "800/5", "pt": "6000/100" }, // 南羊路1200波特率
        "000000000022_018774": { "ct": "600/5", "pt": "6000/100" },
        "000000000061_018774": { "ct": "200/5", "pt": "6000/100" },
        "000000000142_018774": { "ct": "400/5", "pt": "6000/100" },
        "000000000021_018774": { "ct": "400/5", "pt": "6000/100" },
        "000000000063_018774": { "ct": "400/5", "pt": "6000/100" },
        "000000000023_018774": { "ct": "200/5", "pt": "6000/100" },
        "000000000012_018774": { "ct": "200/5", "pt": "6000/100" },
        "000000000140_018774": { "ct": "400/5", "pt": "6000/100" },
        "000000000020_018774": { "ct": "600/5", "pt": "6000/100" },
        "000000000077_018774": { "ct": "600/5", "pt": "6000/100" },
        "000000000066_018774": { "ct": "500/5", "pt": "6000/100" },
        "000002230366_018946": { "ct": "600/5", "pt": "6000/100" }, // 四台2400波特率
        "000002230365_018946": { "ct": "150/5", "pt": "6000/100" },
        "000002232377_024635": { "ct": "400/5", "pt": "35000/100" }, // 万家嘴2400波特率
        "000002230302_018510": { "ct": "400/5", "pt": "10000/100" }, // 榆林2400波特率
        "000002230311_018510": { "ct": "800/5", "pt": "10000/100" },
        "000002232390_023006": { "ct": "1000/5", "pt": "6000/100" }, // 挖金湾2400波特率
        "000002232343_023006": { "ct": "1000/5", "pt": "6000/100" },
        "000001961921_022419": { "ct": "1200/5", "pt": "10000/100" }, // 乔村2400波特率
        "000001961923_022419": { "ct": "1200/5", "pt": "10000/100" },
        "000001961915_022419": { "ct": "400/5", "pt": "10000/100" },
        "000001961903_022419": { "ct": "400/5", "pt": "10000/100" },
        "000001961904_022419": { "ct": "400/5", "pt": "10000/100" },
        "000001961916_022419": { "ct": "300/5", "pt": "10000/100" },
        "000002230319_017072": { "ct": "800/5", "pt": "6000/100" }, // 忻州窑2400波特率
        "000002232310_017072": { "ct": "150/5", "pt": "6000/100" },
        "000002232185_024056": { "ct": "3000/5", "pt": "6000/100" }, // 燕子山2400波特率
        "000002232167_024056": { "ct": "3000/5", "pt": "6000/100" },
        "000002232184_024056": { "ct": "3000/5", "pt": "6000/100" },
        "000001961949_024056": { "ct": "50/5", "pt": "6000/100" },
        "000001961979_024056": { "ct": "600/5", "pt": "6000/100" },
        "000002232199_024056": { "ct": "600/5", "pt": "110000/100" },
        "000002232197_024056": { "ct": "600/5", "pt": "110000/100" },
        "000002230371_024280": { "ct": "2000/5", "pt": "6000/100" }, // 姜家湾2400波特率
        "000002230348_024280": { "ct": "2000/5", "pt": "6000/100" },
        "000002230331_024280": { "ct": "2000/5", "pt": "6000/100" },
        "000002230328_024280": { "ct": "800/5", "pt": "6000/100" },
        "000002230305_024280": { "ct": "600/5", "pt": "35000/100" },
        "000002230364_024280": { "ct": "400/5", "pt": "35000/100" },
        "000002232460_023961": { "ct": "600/5", "pt": "10000/100" }, // 金庄2400波特率
        "000002232431_023961": { "ct": "800/5", "pt": "10000/100" },
        "000002230330_023702": { "ct": "400/5", "pt": "10000/100" }, // 夏家河2400波特率
        "000002230338_023702": { "ct": "1000/5", "pt": "6000/100" },
        "454557000027_022823": { "ct": "1500/5", "pt": "35000/100" }, // 向阳寨2400波特率
        "454557000026_022823": { "ct": "4000/5", "pt": "10000/100" },
    };

    if (deviceRates[deviceKey]) {
        var rates = deviceRates[deviceKey];
        ct = parseFloat(rates.ct.split('/')[0]) / parseFloat(rates.ct.split('/')[1]);
        pt = parseFloat(rates.pt.split('/')[0]) / parseFloat(rates.pt.split('/')[1]);
    } else {
        return;
    }

    // 正向有功总电能（需要乘以PT和CT的倍率）
    if (data.kwhp) data.kwhp = safeMultiply(data.kwhp, pt * ct);
    // 正向有功费率1电能（需要乘以PT和CT的倍率）
    if (data.kwhp1) data.kwhp1 = safeMultiply(data.kwhp1, pt * ct);
    // 正向有功费率2电能（需要乘以PT和CT的倍率）
    if (data.kwhp2) data.kwhp2 = safeMultiply(data.kwhp2, pt * ct);
    // 正向有功费率3电能（需要乘以PT和CT的倍率）
    if (data.kwhp3) data.kwhp3 = safeMultiply(data.kwhp3, pt * ct);
    // 正向有功费率4电能（需要乘以PT和CT的倍率）
    if (data.kwhp4) data.kwhp4 = safeMultiply(data.kwhp4, pt * ct);
    // 反向有功总电能（需要乘以PT和CT的倍率）
    if (data.kwhn) data.kwhn = safeMultiply(data.kwhn, pt * ct);
    // A相电压（需要乘以PT的倍率）
    if (data.ua) data.ua = safeMultiply(data.ua, pt);
    // B相电压（需要乘以PT的倍率）
    if (data.ub) data.ub = safeMultiply(data.ub, pt);
    // C相电压（需要乘以PT的倍率）
    if (data.uc) data.uc = safeMultiply(data.uc, pt);
    // A相电流（需要乘以CT的倍率）
    if (data.ia) data.ia = safeMultiply(data.ia, ct);
    // B相电流（需要乘以CT的倍率）
    if (data.ib) data.ib = safeMultiply(data.ib, ct);
    // C相电流（需要乘以CT的倍率）
    if (data.ic) data.ic = safeMultiply(data.ic, ct);
    // 总有功功率（需要乘以PT和CT的倍率）
    if (data.pt) data.pt = safeMultiply(data.pt, pt * ct);
    // A相有功功率（需要乘以PT和CT的倍率）
    if (data.pa) data.pa = safeMultiply(data.pa, pt * ct);
    // B相有功功率（需要乘以PT和CT的倍率）
    if (data.pb) data.pb = safeMultiply(data.pb, pt * ct);
    // C相有功功率（需要乘以PT和CT的倍率）
    if (data.pc) data.pc = safeMultiply(data.pc, pt * ct);
    // 总无功功率（需要乘以PT和CT的倍率）
    if (data.qt) data.qt = safeMultiply(data.qt, pt * ct);
    // A相无功功率（需要乘以PT和CT的倍率）
    if (data.q1) data.q1 = safeMultiply(data.q1, pt * ct);
    // B相无功功率（需要乘以PT和CT的倍率）
    if (data.q2) data.q2 = safeMultiply(data.q2, pt * ct);
    // C相无功功率（需要乘以PT和CT的倍率）
    if (data.q3) data.q3 = safeMultiply(data.q3, pt * ct);
    // 总视在功率（需要乘以PT和CT的倍率）
    // A相视在功率（需要乘以PT和CT的倍率）
    // B相视在功率（需要乘以PT和CT的倍率）
    // C相视在功率（需要乘以PT和CT的倍率）

    // 最大需量类数据（需要乘以PT和CT的倍率）
    if (data.demand_max) data.demand_max = safeMultiply(data.demand_max, pt * ct);
    if (data.demand_max_rate1) data.demand_max_rate1 = safeMultiply(data.demand_max_rate1, pt * ct);
    if (data.demand_max_rate2) data.demand_max_rate2 = safeMultiply(data.demand_max_rate2, pt * ct);
    if (data.demand_max_rate3) data.demand_max_rate3 = safeMultiply(data.demand_max_rate3, pt * ct);
    if (data.demand_max_rate4) data.demand_max_rate4 = safeMultiply(data.demand_max_rate4, pt * ct);
}

// 解码函数，用于解析消息数据
this.decode = function (msg) {
    var resultDatas = [];
    var gatewayNo = msg.gatewayNo;
    var datas = msg.data.replace(/\s+/g, '');
    //console.log('datas: ' + datas);

    var packets = [];
    var startFlag = '68';
    var endFlag = '16';
    var currentIndex = 0;
    var workArea = datas;

    // Optional: Strip common preamble like 'fefefefe' 
    var knownPreambles = ["fefefefe"];
    for (var p = 0; p < knownPreambles.length; p++) {
        if (workArea.toLowerCase().startsWith(knownPreambles[p])) {
            workArea = workArea.substring(knownPreambles[p].length);
            break;
        }
    }

    while (currentIndex < workArea.length) {
        var frameStartOffset = workArea.indexOf(startFlag, currentIndex);

        if (frameStartOffset === -1) {
            // No more start flags
            break;
        }

        // A DLT645 frame is: 68 Addr(6) 68 Ctrl(1) Len(1) Data(Len) CS(1) 16(1)
        // The Length byte (L) is the 10th byte (index 9), so char offset +18 from frame start.
        // Need at least 10 bytes (20 chars) to read up to and include L.
        if (frameStartOffset + 20 > workArea.length) {
            // Not enough data to read the L byte and what follows
            break;
        }

        var lengthByteHex = workArea.substr(frameStartOffset + 18, 2);
        var dataLength;

        // Validate L byte format
        if (!/^[0-9a-fA-F]{2}$/.test(lengthByteHex)) {
            currentIndex = frameStartOffset + 2; // Skip this '68'
            if (currentIndex >= workArea.length) break;
            continue;
        }
        dataLength = parseInt(lengthByteHex, 16);

        // Total frame byte length: 1(68)+6(Addr)+1(68)+1(Ctrl)+1(L_byte_itself)+dataLength+1(CS)+1(16)
        var expectedFrameByteLength = 12 + dataLength;
        var expectedFrameCharLength = expectedFrameByteLength * 2;

        if (frameStartOffset + expectedFrameCharLength > workArea.length) {
            // Not enough data for the calculated frame length
            break;
        }

        var potentialPacket = workArea.substr(frameStartOffset, expectedFrameCharLength);

        // Check if it starts with 68 (it should by indexOf) and ends with 16
        if (potentialPacket.startsWith(startFlag) && potentialPacket.endsWith(endFlag) && potentialPacket.length === expectedFrameCharLength) {
            packets.push(potentialPacket);
            currentIndex = frameStartOffset + expectedFrameCharLength;
        } else {
            // Malformed packet or L byte was misleading. Skip this '68' and search for next.
            currentIndex = frameStartOffset + 2;
            if (currentIndex >= workArea.length) break;
        }
    }

    // 处理每个数据包
    for (var k = 0; k < packets.length; k++) {
        var packet = packets[k];
        // 计算校验和
        var checkSum = 0;
        for (var i = 0; i < packet.length - 4; i += 2) {
            checkSum += parseInt(packet.substr(i, 2), 16);
        }
        checkSum %= 256;

        var checkSumHex = packet.substr(packet.length - 4, 2);
        if (checkSum !== parseInt(checkSumHex, 16)) {
            //console.log('校验和错误');
            continue; // 跳过无效的数据包
        }

        // 提取地址域（反转每两个字节）
        var address = parseDataReverse(packet, 2, 12);
        //console.log('地址: ' + address);

        // 设置设备编号， address 加上 gatewayNo 后六位，先判断是否有gatewayNo和是否大于6位
        var deviceNo = address;
        if (gatewayNo != null && gatewayNo.length >= 6) {
            deviceNo = address + '_' + gatewayNo.substring(gatewayNo.length - 6);
        }

        var obj = {
            // deviceKey: address,
            deviceKey: deviceNo,
            data: {},
            time: (new Date()).getTime().toString(),
            protocol_version: '',
            data_identifiers: []
        };

        // 控制码
        var controlCode = packet.substr(16, 2);
        //console.log('控制码: ' + controlCode);

        // 2007 协议
        if (controlCode === '91') {
            obj.protocol_version = '2007';

            // 数据长度
            var dataLengthHex = packet.substr(18, 2);
            var dataLength = parseInt(dataLengthHex, 16);
            //console.log('数据长度: ' + dataLength);

            // 数据域
            var data = packet.substr(20, dataLength * 2);
            //console.log('数据: ' + data);

            // 数据标识（mark）和数据定义
            var mark = parseData(data, 0, 8);
            var dataDef = data.substring(8);
            obj.data_identifiers.push({
                id: mark,
                description: getDataIdentifierDescription2007(mark)
            });
            //console.log('标识 (Mark): ' + mark);
            //console.log('数据 (Data): ' + dataDef);

            // 根据数据标识处理数据
            switch (mark) {
                case '00010000':
                    //console.log('数据标识：正向有功总电能');
                    var energyData1 = parseEnergyDataBySlice(dataDef, 0, 8, 6);
                    //console.log('解析数据：' + energyData1);
                    obj.data['kwhp'] = energyData1;
                    break;
                case '00010100':
                    //console.log('数据标识：正向有功费率 1 电能');
                    var energyData1_1 = parseEnergyDataBySlice(dataDef, 0, 8, 6);
                    //console.log('解析数据：' + energyData1_1);
                    obj.data['kwhp1'] = energyData1_1;
                    break;
                case '00010200':
                    //console.log('数据标识：正向有功费率 2 电能');
                    var energyData1_2 = parseEnergyDataBySlice(dataDef, 0, 8, 6);
                    //console.log('解析数据：' + energyData1_2);
                    obj.data['kwhp2'] = energyData1_2;
                    break;
                case '00010300':
                    //console.log('数据标识：正向有功费率 3 电能');
                    var energyData1_3 = parseEnergyDataBySlice(dataDef, 0, 8, 6);
                    //console.log('解析数据：' + energyData1_3);
                    obj.data['kwhp3'] = energyData1_3;
                    break;
                case '00010400':
                    //console.log('数据标识：正向有功费率 4 电能');
                    var energyData1_4 = parseEnergyDataBySlice(dataDef, 0, 8, 6);
                    //console.log('解析数据：' + energyData1_4);
                    obj.data['kwhp4'] = energyData1_4;
                    break;
                case '0001ff00':
                    //console.log('数据标识：正向有功电能数据块');
                    var dataBlockLength = 8;
                    var dataBlockCount = dataDef.length / dataBlockLength;
                    //console.log('数据块数量：' + dataBlockCount);
                    var energyBlocks = [];
                    for (var j = 0; j < dataBlockCount; j++) {
                        var blockData = dataDef.substr(j * dataBlockLength, dataBlockLength);
                        var energyData = parseEnergyDataBySlice(blockData, 0, dataBlockLength, 6);
                        //console.log('解析数据：' + energyData);
                        energyBlocks.push(energyData);
                    }
                    obj.data['kwhp'] = energyBlocks[0];
                    obj.data['kwhp1'] = energyBlocks[1];
                    obj.data['kwhp2'] = energyBlocks[2];
                    obj.data['kwhp3'] = energyBlocks[3];
                    obj.data['kwhp4'] = energyBlocks[4];
                    break;
                case '00020000':
                    //console.log('数据标识：反向有功总电能');
                    var energyData2 = parseEnergyDataBySlice(dataDef, 0, 8, 6);
                    //console.log('解析数据：' + energyData2);
                    obj.data['kwhn'] = energyData2;
                    break;
                case '0002ff00':
                    //console.log('数据标识：反向有功电能数据块');
                    var dataBlockLength2 = 8;
                    var dataBlockCount2 = dataDef.length / dataBlockLength2;
                    //console.log('数据块数量：' + dataBlockCount2);
                    var energyBlocks = [];
                    for (var j = 0; j < dataBlockCount2; j++) {
                        var blockData = dataDef.substr(j * dataBlockLength2, dataBlockLength2);
                        var energyData = parseEnergyDataBySlice(blockData, 0, dataBlockLength2, 6);
                        //console.log('解析数据：' + energyData);
                        energyBlocks.push(energyData);
                    }
                    obj.data['kwhn'] = energyBlocks[0];
                    break;
                case '00030000':
                    //console.log('数据标识：组合无功 1 总电能'); (通常可视为正向无功总电能)
                    var energyData2 = parseEnergyDataBySlice(dataDef, 0, 8, 6);
                    //console.log('解析数据：' + energyData2);
                    obj.data['kvarhp'] = energyData2;
                    break;
                case '0003ff00':
                    //console.log('数据标识：组合无功 1 电能数据块');(通常可视为正向无功总电能)
                    var dataBlockLength2 = 8;
                    var dataBlockCount2 = dataDef.length / dataBlockLength2;
                    //console.log('数据块数量：' + dataBlockCount2);
                    var energyBlocks = [];
                    for (var j = 0; j < dataBlockCount2; j++) {
                        var blockData = dataDef.substr(j * dataBlockLength2, dataBlockLength2);
                        var energyData = parseEnergyDataBySlice(blockData, 0, dataBlockLength2, 6);
                        //console.log('解析数据：' + energyData);
                        energyBlocks.push(energyData);
                    }
                    obj.data['kvarhp'] = energyBlocks[0];
                    break;
                case '00040000':
                    //console.log('数据标识：组合无功 2 总电能'); (通常可视为反向无功总电能)
                    var energyData2 = parseEnergyDataBySlice(dataDef, 0, 8, 6);
                    //console.log('解析数据：' + energyData2);
                    obj.data['kvarhn'] = energyData2;
                    break;
                case '0004ff00':
                    //console.log('数据标识：组合无功 2 电能数据块');(通常可视为反向无功总电能)
                    var dataBlockLength2 = 8;
                    var dataBlockCount2 = dataDef.length / dataBlockLength2;
                    //console.log('数据块数量：' + dataBlockCount2);
                    var energyBlocks = [];
                    for (var j = 0; j < dataBlockCount2; j++) {
                        var blockData = dataDef.substr(j * dataBlockLength2, dataBlockLength2);
                        var energyData = parseEnergyDataBySlice(blockData, 0, dataBlockLength2, 6);
                        //console.log('解析数据：' + energyData);
                        energyBlocks.push(energyData);
                    }
                    obj.data['kvarhn'] = energyBlocks[0];
                    break;
                case '01010000':
                    //console.log('数据标识：正向有功总最大需量及发生时间');
                    var demandValue = parseEnergyDataBySlice(dataDef, 0, 6, 2);
                    //console.log('最大需量值：' + demandValue);
                    obj.data['demand_max'] = demandValue;

                    // 解析发生时间 (后5个字节，格式：YYMMDDHHmm)
                    var timeData = parseData(dataDef, 6, 10);
                    //console.log('时间数据：' + timeData);
                    if (timeData && timeData.length >= 10) {
                        var year = '20' + timeData.slice(0, 2);
                        var month = timeData.slice(2, 4);
                        var day = timeData.slice(4, 6);
                        var hour = timeData.slice(6, 8);
                        var minute = timeData.slice(8, 10);
                        var timeStr = year + '-' + month + '-' + day + ' ' + hour + ':' + minute;
                        //console.log('发生时间：' + timeStr);
                        obj.data['demand_max_time'] = timeStr;
                    }
                    break;
                case '0101ff00':
                    //console.log('数据标识：正向有功最大需量及发生时间数据块');
                    var demandBlockLength = 16; // 每个最大需量数据块：3字节需量值 + 5字节时间 = 8字节，但实际可能是16字节
                    var demandBlockCount = dataDef.length / demandBlockLength;
                    //console.log('最大需量数据块数量：' + demandBlockCount);

                    // 定义最大需量字段名和时间字段名的对应关系
                    var demandFields = [
                        { value: 'demand_max', time: 'demand_max_time' },          // 总最大需量
                        { value: 'demand_max_rate1', time: 'demand_max_rate1_time' }, // 费率1最大需量
                        { value: 'demand_max_rate2', time: 'demand_max_rate2_time' }, // 费率2最大需量
                        { value: 'demand_max_rate3', time: 'demand_max_rate3_time' }, // 费率3最大需量
                        { value: 'demand_max_rate4', time: 'demand_max_rate4_time' }, // 费率4最大需量
                    ];

                    for (var j = 0; j < demandBlockCount && j < demandFields.length; j++) {
                        var blockData = dataDef.substr(j * demandBlockLength, demandBlockLength);

                        // 解析最大需量值 (前3个字节，保留2位小数)
                        var demandValue = parseEnergyDataBySlice(blockData, 0, 6, 2);
                        //console.log('最大需量值[' + j + ']：' + demandValue);
                        obj.data[demandFields[j].value] = demandValue;

                        // 解析发生时间 (后5个字节，格式：YYMMDDHHmm)
                        var timeData = parseData(blockData, 6, 10);
                        //console.log('时间数据[' + j + ']：' + timeData);
                        if (timeData && timeData.length >= 10) {
                            var year = '20' + timeData.slice(0, 2);
                            var month = timeData.slice(2, 4);
                            var day = timeData.slice(4, 6);
                            var hour = timeData.slice(6, 8);
                            var minute = timeData.slice(8, 10);
                            var timeStr = year + '-' + month + '-' + day + ' ' + hour + ':' + minute;
                            //console.log('发生时间[' + j + ']：' + timeStr);
                            obj.data[demandFields[j].time] = timeStr;
                        }
                    }
                    break;
                case '0201ff00':
                    //console.log('数据标识：电压数据块');
                    var dataBlockLength4 = 4;
                    var dataBlockCount4 = dataDef.length / dataBlockLength4;
                    //console.log('数据块数量：' + dataBlockCount4);
                    var voltageBlocks = [];
                    for (var j = 0; j < dataBlockCount4; j++) {
                        var blockData = dataDef.substr(j * dataBlockLength4, dataBlockLength4);
                        var voltageData = parseEnergyDataBySlice(blockData, 0, dataBlockLength4, 3);
                        //console.log('解析数据：' + voltageData);
                        voltageBlocks.push(voltageData);
                    }
                    obj.data['ua'] = voltageBlocks[0];
                    obj.data['ub'] = voltageBlocks[1];
                    obj.data['uc'] = voltageBlocks[2];
                    break;
                case '0202ff00':
                    //console.log('数据标识：电流数据块');
                    var dataBlockLength4 = 6;
                    var dataBlockCount4 = dataDef.length / dataBlockLength4;
                    //console.log('数据块数量：' + dataBlockCount4);
                    var voltageBlocks = [];
                    for (var j = 0; j < dataBlockCount4; j++) {
                        var blockData = dataDef.substr(j * dataBlockLength4, dataBlockLength4);
                        var voltageData = parseEnergyDataBySlice(blockData, 0, dataBlockLength4, 3);
                        //console.log('解析数据：' + voltageData);
                        voltageBlocks.push(voltageData);
                    }
                    obj.data['ia'] = voltageBlocks[0];
                    obj.data['ib'] = voltageBlocks[1];
                    obj.data['ic'] = voltageBlocks[2];
                    break;
                case '0203ff00':
                    //console.log('数据标识：瞬时有功功率数据块');
                    var dataBlockLength4 = 6;
                    var dataBlockCount4 = dataDef.length / dataBlockLength4;
                    //console.log('数据块数量：' + dataBlockCount4);
                    var voltageBlocks = [];
                    for (var j = 0; j < dataBlockCount4; j++) {
                        var blockData = dataDef.substr(j * dataBlockLength4, dataBlockLength4);
                        var voltageData = parseEnergyDataBySlice(blockData, 0, dataBlockLength4, 2);
                        //console.log('解析数据：' + voltageData);
                        voltageBlocks.push(voltageData);
                    }
                    obj.data['pt'] = voltageBlocks[0];
                    obj.data['pa'] = voltageBlocks[1];
                    obj.data['pb'] = voltageBlocks[2];
                    obj.data['pc'] = voltageBlocks[3];
                    break;
                case '0204ff00':
                    //console.log('数据标识：瞬时无功功率数据块');
                    var dataBlockLength4 = 6;
                    var dataBlockCount4 = dataDef.length / dataBlockLength4;
                    //console.log('数据块数量：' + dataBlockCount4);
                    var voltageBlocks = [];
                    for (var j = 0; j < dataBlockCount4; j++) {
                        var blockData = dataDef.substr(j * dataBlockLength4, dataBlockLength4);
                        var voltageData = parseEnergyDataBySlice(blockData, 0, dataBlockLength4, 2);
                        //console.log('解析数据：' + voltageData);
                        voltageBlocks.push(voltageData);
                    }
                    obj.data['qt'] = voltageBlocks[0];
                    obj.data['q1'] = voltageBlocks[1];
                    obj.data['q2'] = voltageBlocks[2];
                    obj.data['q3'] = voltageBlocks[3];
                    break;
                case '0205ff00':
                    //console.log('数据标识：瞬时视在功率数据块');
                    var dataBlockLength4 = 6;
                    var dataBlockCount4 = dataDef.length / dataBlockLength4;
                    //console.log('数据块数量：' + dataBlockCount4);
                    var voltageBlocks = [];
                    for (var j = 0; j < dataBlockCount4; j++) {
                        var blockData = dataDef.substr(j * dataBlockLength4, dataBlockLength4);
                        var voltageData = parseEnergyDataBySlice(blockData, 0, dataBlockLength4, 2);
                        //console.log('解析数据：' + voltageData);
                        voltageBlocks.push(voltageData);
                    }
                    obj.data['st'] = voltageBlocks[0];
                    obj.data['sa'] = voltageBlocks[1];
                    obj.data['sb'] = voltageBlocks[2];
                    obj.data['sc'] = voltageBlocks[3];
                    break;
                case '0206ff00':
                    //console.log('数据标识：功率因数数据块');
                    var dataBlockLength4 = 4;
                    var dataBlockCount4 = dataDef.length / dataBlockLength4;
                    //console.log('数据块数量：' + dataBlockCount4);
                    var voltageBlocks = [];
                    for (var j = 0; j < dataBlockCount4; j++) {
                        var blockData = dataDef.substr(j * dataBlockLength4, dataBlockLength4);
                        var voltageData = parseEnergyDataBySlice(blockData, 0, dataBlockLength4, 1);
                        //console.log('解析数据：' + voltageData);
                        voltageBlocks.push(voltageData);
                    }
                    obj.data['pft'] = voltageBlocks[0];
                    obj.data['pf1'] = voltageBlocks[1];
                    obj.data['pf2'] = voltageBlocks[2];
                    obj.data['pf3'] = voltageBlocks[3];
                    break;
                case '02800001':
                    //console.log('数据标识：零线电流');
                    var energyData1 = parseEnergyDataBySlice(dataDef, 0, 6, 3);
                    //console.log('解析数据：' + energyData1);
                    obj.data['inc'] = energyData1;
                    break;
                default:
                    //console.log('未知的数据标识');
                    break;
            }
            setRate(obj.data, obj.deviceKey);
            resultDatas.push(obj);
        } else if (controlCode === '81') {
            // 1997 协议
            obj.protocol_version = '1997';

            // 数据长度
            var dataLengthHex = packet.substr(18, 2);
            var dataLength = parseInt(dataLengthHex, 16);
            //console.log('数据长度: ' + dataLength);

            // 数据域
            var data = packet.substr(20, dataLength * 2);
            //console.log('数据: ' + data);

            // 数据标识（mark）和数据定义
            var mark = parseData(data, 0, 4);
            var dataDef = data.substring(4);
            obj.data_identifiers.push({
                id: mark,
                description: getDataIdentifierDescription1997(mark)
            });
            //console.log('标识 (Mark): ' + mark);
            //console.log('数据 (Data): ' + dataDef);

            // 根据数据标识处理数据
            switch (mark) {
                case '9010':
                    //console.log('数据标识： 正向有功总电能');
                    obj.data['kwhp'] = parseEnergyDataBySlice(dataDef, 0, 8, 6);
                    break;
                case '9011':
                    //console.log('数据标识： 正向有功费率 1 电能');
                    obj.data['kwhp1'] = parseEnergyDataBySlice(dataDef, 0, 8, 6);
                    break;
                case '9012':
                    //console.log('数据标识： 正向有功费率 2 电能');
                    obj.data['kwhp2'] = parseEnergyDataBySlice(dataDef, 0, 8, 6);
                    break;
                case '9013':
                    //console.log('数据标识： 正向有功费率 3 电能');
                    obj.data['kwhp3'] = parseEnergyDataBySlice(dataDef, 0, 8, 6);
                    break;
                case '9014':
                    //console.log('数据标识： 正向有功费率 4 电能');
                    obj.data['kwhp4'] = parseEnergyDataBySlice(dataDef, 0, 8, 6);
                    break;
                case '901f':
                    //console.log('数据标识： 正向有功总电能数据块');
                    var dataBlockLength = 8;
                    var dataBlockCount = dataDef.length / dataBlockLength;
                    //console.log('数据块数量：' + dataBlockCount);
                    var energyBlocks = [];
                    for (var j = 0; j < dataBlockCount; j++) {
                        var blockData = dataDef.substr(j * dataBlockLength, dataBlockLength);
                        //console.log('解析数据：' + energyData);
                        energyBlocks.push(parseEnergyDataBySlice(blockData, 0, dataBlockLength, 6));
                    }
                    obj.data['kwhp'] = energyBlocks[0];
                    obj.data['kwhp1'] = energyBlocks[1];
                    obj.data['kwhp2'] = energyBlocks[2];
                    obj.data['kwhp3'] = energyBlocks[3];
                    obj.data['kwhp4'] = energyBlocks[4];
                    break;
                case '9020':
                    //console.log('数据标识： 反向有功总电能');
                    obj.data['kwhn'] = parseEnergyDataBySlice(dataDef, 0, 8, 6);
                    break;
                case '902f':
                    //console.log('数据标识： 反向有功总电能数据块');
                    var dataBlockLength = 8;
                    var dataBlockCount = dataDef.length / dataBlockLength;
                    //console.log('数据块数量：' + dataBlockCount);
                    var energyBlocks = [];
                    for (var j = 0; j < dataBlockCount; j++) {
                        var blockData = dataDef.substr(j * dataBlockLength, dataBlockLength);
                        //console.log('解析数据：' + energyData);
                        energyBlocks.push(parseEnergyDataBySlice(blockData, 0, dataBlockLength, 6));
                    }
                    obj.data['kwhn'] = energyBlocks[0];
                    break;
                case '9110':
                    //console.log('数据标识： 正向无功总电能');
                    obj.data['kvarhp'] = parseEnergyDataBySlice(dataDef, 0, 8, 6);
                    break;
                case '911f':
                    //console.log('数据标识： 正向无功总电能数据块');
                    var dataBlockLength = 8;
                    var dataBlockCount = dataDef.length / dataBlockLength;
                    //console.log('数据块数量：' + dataBlockCount);
                    var energyBlocks = [];
                    for (var j = 0; j < dataBlockCount; j++) {
                        var blockData = dataDef.substr(j * dataBlockLength, dataBlockLength);
                        //console.log('解析数据：' + energyData);
                        energyBlocks.push(parseEnergyDataBySlice(blockData, 0, dataBlockLength, 6));
                    }
                    obj.data['kvarhp'] = energyBlocks[0];
                    break;
                case '9120':
                    //console.log('数据标识： 反向无功总电能');
                    obj.data['kvarhn'] = parseEnergyDataBySlice(dataDef, 0, 8, 6);
                    break;
                case '912f':
                    //console.log('数据标识： 反向无功总电能数据块');
                    var dataBlockLength = 8;
                    var dataBlockCount = dataDef.length / dataBlockLength;
                    //console.log('数据块数量：' + dataBlockCount);
                    var energyBlocks = [];
                    for (var j = 0; j < dataBlockCount; j++) {
                        var blockData = dataDef.substr(j * dataBlockLength, dataBlockLength);
                        //console.log('解析数据：' + energyData);
                        energyBlocks.push(parseEnergyDataBySlice(blockData, 0, dataBlockLength, 6));
                    }
                    obj.data['kvarhn'] = energyBlocks[0];
                    break;
                case 'b611':
                    //console.log('数据标识：A 相电压');
                    obj.data['ua'] = parseEnergyDataBySlice(dataDef, 0, 4, 0);
                    break;
                case 'b612':
                    //console.log('数据标识：B 相电压');
                    obj.data['ub'] = parseEnergyDataBySlice(dataDef, 0, 4, 0);
                    break;
                case 'b613':
                    //console.log('数据标识：C 相电压');
                    obj.data['uc'] = parseEnergyDataBySlice(dataDef, 0, 4, 0);
                    break;
                case 'b621':
                    //console.log('数据标识：A 相电流');
                    obj.data['ia'] = parseEnergyDataBySlice(dataDef, 0, 4, 2);
                    break;
                case 'b622':
                    //console.log('数据标识：B 相电流');
                    obj.data['ib'] = parseEnergyDataBySlice(dataDef, 0, 4, 2);
                    break;
                case 'b623':
                    //console.log('数据标识：C 相电流');
                    obj.data['ic'] = parseEnergyDataBySlice(dataDef, 0, 4, 2);
                    break;
                case 'b630':
                    //console.log('数据标识：瞬时有功功率');
                    obj.data['pt'] = parseEnergyDataBySlice(dataDef, 0, 6, 2);
                    break;
                case 'b631':
                    //console.log('数据标识：A 相有功功率');
                    obj.data['pa'] = parseEnergyDataBySlice(dataDef, 0, 6, 2);
                    break;
                case 'b632':
                    //console.log('数据标识：B 相有功功率');
                    obj.data['pb'] = parseEnergyDataBySlice(dataDef, 0, 6, 2);
                    break;
                case 'b633':
                    //console.log('数据标识：C 相有功功率');
                    obj.data['pc'] = parseEnergyDataBySlice(dataDef, 0, 6, 2);
                    break;
                case 'b640':
                    //console.log('数据标识：瞬时无功功率');
                    obj.data['qt'] = parseEnergyDataBySlice(dataDef, 0, 4, 2);
                    break;
                case 'b641':
                    //console.log('数据标识：A 相无功功率');
                    obj.data['q1'] = parseEnergyDataBySlice(dataDef, 0, 4, 2);
                    break;
                case 'b642':
                    //console.log('数据标识：B 相无功功率');
                    obj.data['q2'] = parseEnergyDataBySlice(dataDef, 0, 4, 2);
                    break;
                case 'b643':
                    //console.log('数据标识：C 相无功功率');
                    obj.data['q3'] = parseEnergyDataBySlice(dataDef, 0, 4, 2);
                    break;
                case 'b650':
                    //console.log('数据标识：总功率因数');
                    obj.data['pft'] = parseEnergyDataBySlice(dataDef, 0, 4, 1);
                    break;
                case 'b651':
                    //console.log('数据标识：A 相功率因数');
                    obj.data['pf1'] = parseEnergyDataBySlice(dataDef, 0, 4, 1);
                    break;
                case 'b652':
                    //console.log('数据标识：B 相功率因数');
                    obj.data['pf2'] = parseEnergyDataBySlice(dataDef, 0, 4, 1);
                    break;
                case 'b653':
                    //console.log('数据标识：C 相功率因数');
                    obj.data['pf3'] = parseEnergyDataBySlice(dataDef, 0, 4, 1);
                    break;
                case 'a010':
                    //console.log('数据标识：正向有功总最大需量');
                    obj.data['demand_max'] = parseEnergyDataBySlice(dataDef, 0, 6, 2);
                    break;
                case 'a01f':
                    //console.log('数据标识：有功最大需量数据块');
                    var demandBlockLength = 6; // 每个最大需量值：3字节，保留2位小数
                    var demandBlockCount = dataDef.length / demandBlockLength;
                    //console.log('最大需量数据块数量：' + demandBlockCount);

                    // 定义最大需量字段名
                    var demandValueFields = [
                        'demand_max',        // 总最大需量
                        'demand_max_rate1',  // 费率1最大需量
                        'demand_max_rate2',  // 费率2最大需量
                        'demand_max_rate3',  // 费率3最大需量
                        'demand_max_rate4'   // 费率4最大需量
                    ];

                    for (var j = 0; j < demandBlockCount && j < demandValueFields.length; j++) {
                        var blockData = dataDef.substr(j * demandBlockLength, demandBlockLength);
                        // 解析最大需量值 (3字节，保留2位小数)
                        var demandValue = parseEnergyDataBySlice(blockData, 0, demandBlockLength, 2);
                        //console.log('最大需量值[' + j + ']：' + demandValue);
                        obj.data[demandValueFields[j]] = demandValue;
                    }
                    break;
                case 'b010':
                    //console.log('数据标识：正向有功总最大需量发生时间');
                    // 解析发生时间 (格式：MMDDHHmm)
                    var timeData = parseData(dataDef, 0, 8);
                    //console.log('时间数据：' + timeData);
                    if (timeData && timeData.length >= 8) {
                        var currentYear = new Date().getFullYear();
                        var month = timeData.slice(0, 2);
                        var day = timeData.slice(2, 4);
                        var hour = timeData.slice(4, 6);
                        var minute = timeData.slice(6, 8);
                        var timeStr = currentYear + '-' + month + '-' + day + ' ' + hour + ':' + minute;
                        //console.log('发生时间：' + timeStr);
                        obj.data['demand_max_time'] = timeStr;
                    }
                    break;
                case 'b01f':
                    //console.log('数据标识：正向有功最大需量发生时间数据块');
                    var timeBlockLength = 8; // 每个时间数据：4字节，格式：MMDDHHmm
                    var timeBlockCount = dataDef.length / timeBlockLength;
                    //console.log('最大需量时间数据块数量：' + timeBlockCount);

                    // 定义最大需量时间字段名
                    var demandTimeFields = [
                        'demand_max_time',        // 总最大需量发生时间
                        'demand_max_rate1_time',  // 费率1最大需量发生时间
                        'demand_max_rate2_time',  // 费率2最大需量发生时间
                        'demand_max_rate3_time',  // 费率3最大需量发生时间
                        'demand_max_rate4_time'   // 费率4最大需量发生时间
                    ];

                    for (var j = 0; j < timeBlockCount && j < demandTimeFields.length; j++) {
                        var blockData = dataDef.substr(j * timeBlockLength, timeBlockLength);
                        // 解析发生时间 (格式：MMDDHHmm)
                        var timeData = parseData(blockData, 0, 8);
                        //console.log('时间数据[' + j + ']：' + timeData);
                        if (timeData && timeData.length >= 8) {
                            var currentYear = new Date().getFullYear();
                            var month = timeData.slice(0, 2);
                            var day = timeData.slice(2, 4);
                            var hour = timeData.slice(4, 6);
                            var minute = timeData.slice(6, 8);
                            var timeStr = currentYear + '-' + month + '-' + day + ' ' + hour + ':' + minute;
                            //console.log('发生时间[' + j + ']：' + timeStr);
                            obj.data[demandTimeFields[j]] = timeStr;
                        }
                    }
                    break;

            }
            setRate(obj.data, obj.deviceKey);
            resultDatas.push(obj);
        }
    }

    //console.log(resultDatas);
    return resultDatas;
};

// 编码函数（如果需要）
this.encode = function (msg) {
    // 在此实现编码逻辑
    return null;
};

// 示例消息
var mag = {
    // data: '6889040000000068811752c3ac58b437348935333949c3349574a93443454534dd5b16'
    // data: '0cc0055105510551012d0105510551512d012d510580'
    // data: 'fefefefe6844281601000068010283e9c2166844281601000068810483e9943c1416'
    // data: 'fefefefe681524230200006811043332353512166815242302000068910d333235359935333333337835331516'
    // data: 'fefefefe6837232302000068110432363d353e166837232302000068912e32363d353333333333333333333333333333333333333333333333333333333333333333333333333333333333334616'
    data: '6808010000000068811252d3343333343333343333343333343333dd7016'
}
this.decode(mag);