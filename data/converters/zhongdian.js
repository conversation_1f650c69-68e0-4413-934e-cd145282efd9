this.decode = function (msg) {
    var resultDatas = [];
    var data = msg.data;
    var obj = {};
    obj.deviceKey = msg.SN;
    obj.data = {
        "kwhp": data.kWhImp,
        "kvarhp": data.kvarhImp,
        "kwhn": data.kWhExp,
        "ua": data.Ua,
        "ub": data.Ub,
        "uc": data.Uc,
        "uab": data.Uab,
        "ubc": data.Ubc,
        "uca": data.Uca,
        "ia": data.Ia,
        "ib": data.Ib,
        "ic": data.Ic,
        "pt": data.P,
        "qt": data.Q,
        "pft": data.PFtot,
        "hz": data.Hz,
        "pf1": data.PFa,
        "pf2": data.PFb,
        "pf3": data.PFc,
        "st": data.Stot,
        "pa": data.Pa,
        "pb": data.Pb,
        "pc": data.Pc,
        "iavg": data.Iavg,
        "q1": data.Qa,
        "q2": data.Qb,
        "q3": data.Qc,
        "sa": data.Sa,
        "sb": data.Sb,
        "sc": data.Sc,
        "inc": data.Inc
    };
    obj.time = data.recTime;
    resultDatas.push(obj)

    if (resultDatas) {
        return resultDatas;
    }
    return null;
};
this.encode = function (service, device) {
    return null;
}


//{"cmd":"SnapshotData","ts":1722388320,"SN":3401110761,"group":1,"data":{"recTime":1722388320,"Ua":0,"Ub":0,"Uc":0,"Uab":0,"Ubc":0,"Uca":0,"Ia":0,"Ib":0.01,"Ic":0,"P":0,"Q":0,"PFtot":1,"Uunb":0,"Iunb":0,"IaTHD":0,"IbTHD":99.99,"IcTHD":0,"UaTHD":0,"UbTHD":0,"UcTHD":0,"kWTotDmd":0,"kWTMDmd":0,"kWhImp":2.7,"kvarhImp":0,"kWhExp":0.82,"Hz":0,"PFa":1,"PFb":1,"PFc":1,"Stot":0,"Pa":0,"Pb":0,"Pc":0,"Ulnavg":0,"Ullavg":0,"Iavg":0.003,"Qa":0,"Qb":0,"Qc":0,"Sa":0,"Sb":0,"Sc":0,"Inc":0,"kWhImpCt":0,"kWhExpCt":0,"kvarhImpCt":0,"kvarhExpCt":0}}