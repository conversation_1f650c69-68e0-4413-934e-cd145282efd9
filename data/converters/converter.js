// 宏电
this.decode = function (msg) {
	var resultDatas = [];
	var datas =  msg.data;
	datas.forEach(
		function(res) {
			if (res) {
				var dataNew={
					"deviceKey":"",
					"data":{}
				};
				dataNew.deviceKey = res.device;
				dataNew.data = res;
				delete dataNew.data.device;
				resultDatas.push(dataNew);
			}
		}
	)
	if (resultDatas) {
		return resultDatas;
	}
	return null;
};
// 四信
this.decode = function (msg) {
	var key = "pid"
	var utime = msg.utime
	var array = msg.content
	var groups = {}
	array.forEach(function (o) {
		var group = o[key]
		groups[group] = groups[group] || {}
		groups[group]["pid"] = o["pid"]
		groups[group][o["addr"]] = o["addrv"]
		groups[group]["ctime"] = o["ctime"]
		groups[group]["utime"] = utime
	})
	return Object.values(groups)

};

this.encode = function (service,device) {
	return null;
};
