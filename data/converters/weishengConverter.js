// 16进制减去 33H,两个字节顺序从后往前
function parseData(datas, start, size) {
    var data = '';
    for (var i = start + size - 2; i >= start; i -= 2) {
        // 16进制减去 33H
        var value = parseInt(datas.slice(i, i + 2), 16) - 0x33;
        
        // 负数转换为补码
        if (value < 0) {
            value = 256 + value;
        }
        
        data += padZero(value.toString(16), 2);
    }
    return data;
}

function hexToAscii(hexString) {
    var asciiString = '';
    for (var i = 0; i < hexString.length; i += 2) {
        asciiString += String.fromCharCode(parseInt(hexString.substr(i, 2), 16));
    }
    return asciiString;
}

// 补零
function padZero(num, size) {
    var s = num + "";
    while (s.length < size) s = "0" + s;
    return s;
}

// 解析电能数据  datas:数据  start:开始位置  size:数据长度  point:小数点位置 isNegative:判断最高位是符号位，判断正负
function parseEnergyDataBySlice(datas, start, size, point, isNegative) {
    var data = parseData(datas, start, size);
    //在第point位后加小数点
    data = parseFloat(data.slice(0, point) + '.' + data.slice(point)).toString();
    // 最高位是符号位，判断正负
    if (isNegative && parseInt(data.slice(0, 1), 16) >= 8) {
        // 加负号
        data = '-' + parseFloat(data.slice(1));
    }
    // 检查是否为NaN，如果是则设置为0
    if (isNaN(data)) {
        data = '0';
    }
    return data;
}


// 解析电能数据  datas:数据  start:开始位置  size:数据长度  point:小数点位置 isNegative:判断最高位是符号位，判断正负
function parseEnergyDataHexBySlice(datas, start, size, point, isNegative) {
    var data = parseData(datas, start, size); // 假设parseData是处理原始数据的函数

    // 将十六进制字符串转换为十进制数
    var hexData = parseInt(data, 16);

    // 如果是负数，进行符号位判断
    if (isNegative && (parseInt(data.slice(0, 1), 16) >= 8)) {
        // 加负号
        hexData = -hexData;
    }

    // 转换为浮动数，并在正确的位置添加小数点
    var result = (hexData / Math.pow(10, point)).toFixed(point);

    // 检查是否为NaN，如果是则设置为0
    if (isNaN(result)) {
        result = '0';
    }

    return result;
}


// 安全乘法
function safeMultiply(value, multiplier) {
    return value ? (parseFloat((parseFloat(value) * multiplier).toFixed(3))).toString() : null;
}

// 设置倍率
function setRate(data, deviceKey) {
    if (!data || !deviceKey) {
        return;
    }

    var ct = 1;
    var pt = 1;

    var deviceRates = {
        "587665000404": {"ct": "400/5", "pt": "35000/100"},
        "598874000017": {"ct": "800/5", "pt": "110000/100"},
        "598874000020": {"ct": "300/5", "pt": "110000/100"},
        "598874000018": {"ct": "300/5", "pt": "110000/100"}
    };

    if (deviceRates[deviceKey]) {
        var rates = deviceRates[deviceKey];
        ct = parseFloat(rates.ct.split('/')[0]) / parseFloat(rates.ct.split('/')[1]);
        pt = parseFloat(rates.pt.split('/')[0]) / parseFloat(rates.pt.split('/')[1]);
    } else {
        return;
    }

    // 正向有功总电能（需要乘以PT和CT的倍率）
    if (data.kwhp) data.kwhp = safeMultiply(data.kwhp, pt * ct);
    // 反向有功总电能（需要乘以PT和CT的倍率）
    if (data.kwhn) data.kwhn = safeMultiply(data.kwhn, pt * ct);
    // A相电压（需要乘以PT的倍率）
    if (data.ua) data.ua = safeMultiply(data.ua, pt);
    // B相电压（需要乘以PT的倍率）
    if (data.ub) data.ub = safeMultiply(data.ub, pt);
    // C相电压（需要乘以PT的倍率）
    if (data.uc) data.uc = safeMultiply(data.uc, pt);
    // A相电流（需要乘以CT的倍率）
    if (data.ia) data.ia = safeMultiply(data.ia, ct);
    // B相电流（需要乘以CT的倍率）
    if (data.ib) data.ib = safeMultiply(data.ib, ct);
    // C相电流（需要乘以CT的倍率）
    if (data.ic) data.ic = safeMultiply(data.ic, ct);
    // 总有功功率（需要乘以PT和CT的倍率）
    if (data.pt) data.pt = safeMultiply(data.pt, pt * ct);
    // A相有功功率（需要乘以PT和CT的倍率）
    if (data.pa) data.pa = safeMultiply(data.pa, pt * ct);
    // B相有功功率（需要乘以PT和CT的倍率）
    if (data.pb) data.pb = safeMultiply(data.pb, pt * ct);
    // C相有功功率（需要乘以PT和CT的倍率）
    if (data.pc) data.pc = safeMultiply(data.pc, pt * ct);
    // 总无功功率（需要乘以PT和CT的倍率）
    if (data.qt) data.qt = safeMultiply(data.qt, pt * ct);
    // A相无功功率（需要乘以PT和CT的倍率）
    if (data.q1) data.q1 = safeMultiply(data.q1, pt * ct);
    // B相无功功率（需要乘以PT和CT的倍率）
    if (data.q2) data.q2 = safeMultiply(data.q2, pt * ct);
    // C相无功功率（需要乘以PT和CT的倍率）
    if (data.q3) data.q3 = safeMultiply(data.q3, pt * ct);
    // 总视在功率（需要乘以PT和CT的倍率）
    // A相视在功率（需要乘以PT和CT的倍率）
    // B相视在功率（需要乘以PT和CT的倍率）
    // C相视在功率（需要乘以PT和CT的倍率）
}

//数据解析
this.decode = function (msg) {
    var resultDatas = [];
    var datas = msg.data;
    // 帧总长度 00 EA
    var frameLength = datas.slice(0, 4);
    // 帧起始符 68
    var frameStart = datas.slice(4, 6);
    // 模块地址 135792468417  网关地址 135792468417
    var moduleAddress = datas.slice(16, 18) + datas.slice(14, 16) + datas.slice(12, 14) + datas.slice(10, 12) + datas.slice(8, 10) + datas.slice(6, 8);
    // 帧起始符 68
    var frameStart1 = datas.slice(18, 20);
    // 控制码 22
    var controlCode = datas.slice(20, 22);
    // 数据域长度 00 DD
    var dataLength = datas.slice(22, 26);
    // 周期上报标识 16进制减去 33H  原数据 37 33 B7 33   转换为 00840004
    var periodReport = parseData(datas, 26, 8);
    if (periodReport === '00840004') {
        // 15分钟上报
        // 第一块表数据总长度 33 0A
        var firstDataLength = datas.slice(34, 38);
        //第一块表地址 8C B5 79 C5 8A 46  135792468259
        var firstDataAddress = parseData(datas, 38, 12);
        // 数据项长度
        var dataItemLength = datas.slice(50, 52);
        // 数据标识 8个字节 55 33 B6 33 将十六进制的字符串转换为十进制的数字，然后减去33，再将结果转换为十六进制的字符串
        var dataFlag = parseData(datas, 52, 8);

        var kwhp;
        var kwhn;
        var ua;
        var ub;
        var uc;
        var ia;
        var ib;
        var ic;
        var pt;
        var pa;
        var pb;
        var pc;
        var qt;
        var q1;
        var q2;
        var q3;
        var pft;
        var pf1;
        var pf2;
        var pf3;

        // 时间 10 个字节
        var time = '20' + padZero((parseInt(datas.slice(68, 70), 16) - 0x33).toString(16), 2) + '-' +
            padZero((parseInt(datas.slice(66, 68), 16) - 0x33).toString(16), 2) + '-' +
            padZero((parseInt(datas.slice(64, 66), 16) - 0x33).toString(16), 2) + ' ' +
            padZero((parseInt(datas.slice(62, 64), 16) - 0x33).toString(16), 2) + ':' +
            padZero((parseInt(datas.slice(60, 62), 16) - 0x33).toString(16), 2) + ':00';
        //转为时间戳
        var timestamp = new Date(time).getTime().toString();

        if (dataFlag === '00830022') { //645 协议
            // 正向有功总电能 8个字节
            kwhp = parseEnergyDataBySlice(datas, 70, 8, 6, false);
            // 反向有功总电能 8个字节
            kwhn = parseEnergyDataBySlice(datas, 142, 8, 6, false);
            // 电压数据块1 4个字节 A 相电压
            ua = parseEnergyDataBySlice(datas, 358, 4, 3, false);
            // B 相电压
            ub = parseEnergyDataBySlice(datas, 362, 4, 3, false);
            // C 相电压
            uc = parseEnergyDataBySlice(datas, 366, 4, 3, false);
            // 电流数据块 6个字节 A 相电流
            ia = parseEnergyDataBySlice(datas, 370, 6, 3, true);
            // B 相电流
            ib = parseEnergyDataBySlice(datas, 376, 6, 3, true);
            // C 相电流
            ic = parseEnergyDataBySlice(datas, 382, 6, 3, true);
            // 有功功率数据块 6个字节 总有功功率
            pt = parseEnergyDataBySlice(datas, 404, 6, 2, true);
            // A 相有功功率
            pa = parseEnergyDataBySlice(datas, 410, 6, 2, true);
            // B 相有功功率
            pb = parseEnergyDataBySlice(datas, 416, 6, 2, true);
            // C 相有功功率
            pc = parseEnergyDataBySlice(datas, 422, 6, 2, true);
            // 无功功率数据块 6个字节 总无功功率
            qt = parseEnergyDataBySlice(datas, 428, 6, 2, true);
            // A 相无功功率
            q1 = parseEnergyDataBySlice(datas, 434, 6, 2, true);
            // B 相无功功率
            q2 = parseEnergyDataBySlice(datas, 440, 6, 2, true);
            // C 相无功功率
            q3 = parseEnergyDataBySlice(datas, 446, 6, 2, true);
            // 功率因数数据块 4个字节 总功率因数
            pft = parseEnergyDataBySlice(datas, 452, 4, 1, true);
            // A 相功率因数
            pf1 = parseEnergyDataBySlice(datas, 456, 4, 1, true);
            // B 相功率因数
            pf2 = parseEnergyDataBySlice(datas, 460, 4, 1, true);
            // C 相功率因数
            pf3 = parseEnergyDataBySlice(datas, 464, 4, 1, true);
            // 校验码
            // var checkCode = datas.slice(468, 470);
            // 帧结束符
            // var frameEnd = datas.slice(470, 472);
        } else if (dataFlag === '00830012') { // 645 协议
            // 正向有功总电能 8个字节
            kwhp = parseEnergyDataBySlice(datas, 70, 8, 6, false);
            // 反向有功总电能 8个字节
            kwhn = parseEnergyDataBySlice(datas, 78, 8, 6, false);
            // 当前组合无功1总电能 8个字节
            // var q1 = parseEnergyDataBySlice(datas, 86, 8, 6);
            // 当前组合无功2总电能 8个字节
            // var q2 = parseEnergyDataBySlice(datas, 94, 8, 6);
            // 当前第一象限无功总电能 8个字节
            // var q11 = parseEnergyDataBySlice(datas, 102, 8, 6);
            // 当前第二象限无功总电能 8个字节
            // var q22 = parseEnergyDataBySlice(datas, 110, 8, 6);
            // 当前第三象限无功总电能 8个字节
            // var q33 = parseEnergyDataBySlice(datas, 118, 8, 6);
            // 当前第四象限无功总电能 8个字节
            // var q44 = parseEnergyDataBySlice(datas, 126, 8, 6);
            // 电压数据块 4个字节 *3
            ua = parseEnergyDataBySlice(datas, 134, 4, 3, false);
            ub = parseEnergyDataBySlice(datas, 138, 4, 3, false);
            uc = parseEnergyDataBySlice(datas, 142, 4, 3, false);
            // 电流数据块 6个字节 *3
            ia = parseEnergyDataBySlice(datas, 146, 6, 3, true);
            ib = parseEnergyDataBySlice(datas, 152, 6, 3, true);
            ic = parseEnergyDataBySlice(datas, 158, 6, 3, true);
            // 电网频率 4个字节
            // var f = parseEnergyDataBySlice(datas, 164, 4, 1);
            // 有功需量 6个字节
            // var pt = parseEnergyDataBySlice(datas, 168, 6, 2);
            // 无功需量 6个字节
            // var qt = parseEnergyDataBySlice(datas, 174, 6, 2);
            // 有功功率数据块 6个字节 *4
            pt = parseEnergyDataBySlice(datas, 180, 6, 2, true);
            pa = parseEnergyDataBySlice(datas, 186, 6, 2, true);
            pb = parseEnergyDataBySlice(datas, 192, 6, 2, true);
            pc = parseEnergyDataBySlice(datas, 198, 6, 2, true);
            // 无功功率数据块 6个字节 *4
            qt = parseEnergyDataBySlice(datas, 204, 6, 2, true);
            q1 = parseEnergyDataBySlice(datas, 210, 6, 2, true);
            q2 = parseEnergyDataBySlice(datas, 216, 6, 2, true);
            q3 = parseEnergyDataBySlice(datas, 222, 6, 2, true);
            // 功率因数数据块 4个字节 *4
            pft = parseEnergyDataBySlice(datas, 228, 4, 1, true);
            pf1 = parseEnergyDataBySlice(datas, 232, 4, 1, true);
            pf2 = parseEnergyDataBySlice(datas, 236, 4, 1, true);
            pf3 = parseEnergyDataBySlice(datas, 240, 4, 1, true);
            // 校验码 2个字节
            // 帧结束符 2个字节
        } else if (dataFlag === '00830222') { // 698 协议
            //(当前)正向有功总电能 16 个字节
            kwhp = parseEnergyDataHexBySlice(datas, 70, 16, 4, false);
            //(当前)正向有功费率 1 - 5电能 16 个字节 
            var kwhp1 = parseEnergyDataHexBySlice(datas, 86, 16, 4, false);
            var kwhp2 = parseEnergyDataHexBySlice(datas, 102, 16, 4, false);
            var kwhp3 = parseEnergyDataHexBySlice(datas, 118, 16, 4, false);
            var kwhp4 = parseEnergyDataHexBySlice(datas, 134, 16, 4, false);
            var kwhp5 = parseEnergyDataHexBySlice(datas, 150, 16, 4, false);
            // (当前)反向有功总电能 16 个字节
            kwhn = parseEnergyDataHexBySlice(datas, 166, 16, 4, false);
            // (当前)反向有功费率 1 - 5电能 16 个字节
            var kwhn1 = parseEnergyDataHexBySlice(datas, 182, 16, 4, false);
            var kwhn2 = parseEnergyDataHexBySlice(datas, 198, 16, 4, false);
            var kwhn3 = parseEnergyDataHexBySlice(datas, 214, 16, 4, false);
            var kwhn4 = parseEnergyDataHexBySlice(datas, 230, 16, 4, false);
            var kwhn5 = parseEnergyDataHexBySlice(datas, 246, 16, 4, false);
            // (当前)组合无功1总电能 16 个字节
            // var q1 = (parseInt(parseData(datas, 262, 16), 16) / 10000).toString();
            // (当前)组合无功1费率 1 - 5电能 16 个字节
            var q11 = parseEnergyDataHexBySlice(datas, 278, 16, 4, false);
            var q12 = parseEnergyDataHexBySlice(datas, 294, 16, 4, false);
            var q13 = parseEnergyDataHexBySlice(datas, 310, 16, 4, false);
            var q14 = parseEnergyDataHexBySlice(datas, 326, 16, 4, false);
            var q15 = parseEnergyDataHexBySlice(datas, 342, 16, 4, false);
            // (当前)组合无功2总电能 16 个字节
            // var q2 = (parseInt(parseData(datas, 358, 16), 16) / 10000).toString();
            // (当前)组合无功2费率 1 - 5电能 16 个字节
            var q21 = parseEnergyDataHexBySlice(datas, 374, 16, 4, false);
            var q22 = parseEnergyDataHexBySlice(datas, 390, 16, 4, false);
            var q23 = parseEnergyDataHexBySlice(datas, 406, 16, 4, false);
            var q24 = parseEnergyDataHexBySlice(datas, 422, 16, 4, false);
            var q25 = parseEnergyDataHexBySlice(datas, 438, 16, 4, false);

            var data2 = datas.slice(454, datas.length);
            // 第二个数据长度 2个字节
            var data2Length = parseData(datas, 454, 2);
            // 第二个数据标识 8个字节
            var data2Flag = parseData(datas, 456, 8);
            // 电压数据块 4个字节*3
            ua = parseEnergyDataBySlice(datas, 464, 4, 3, false);
            ub = parseEnergyDataBySlice(datas, 468, 4, 3, false);
            uc = parseEnergyDataBySlice(datas, 472, 4, 3, false);
            // 电流数据块 6个字节*3
            ia = parseEnergyDataBySlice(datas, 476, 6, 3, true);
            ib = parseEnergyDataBySlice(datas, 482, 6, 3, true);
            ic = parseEnergyDataBySlice(datas, 488, 6, 3, true);
            // 电网频率 4个字节
            // var f = parseEnergyDataBySlice(datas, 494, 4, 1);
            // 当前有功需量 6个字节
            // var pt = parseEnergyDataBySlice(datas, 498, 6, 2);
            // 当前无功需量 6个字节
            // var qt = parseEnergyDataBySlice(datas, 504, 6, 2);
            // 有功功率数据块 6个字节*4
            pt = parseEnergyDataBySlice(datas, 510, 6, 2, true);
            pa = parseEnergyDataBySlice(datas, 516, 6, 2, true);
            pb = parseEnergyDataBySlice(datas, 522, 6, 2, true);
            pc = parseEnergyDataBySlice(datas, 528, 6, 2, true);
            // 无功功率数据块 6个字节*4
            qt = parseEnergyDataBySlice(datas, 534, 6, 2, true);
            q1 = parseEnergyDataBySlice(datas, 540, 6, 2, true);
            q2 = parseEnergyDataBySlice(datas, 546, 6, 2, true);
            q3 = parseEnergyDataBySlice(datas, 552, 6, 2, true);
            // 功率因数数据块 4个字节*4
            pft = parseEnergyDataBySlice(datas, 558, 4, 1, true);
            pf1 = parseEnergyDataBySlice(datas, 562, 4, 1, true);
            pf2 = parseEnergyDataBySlice(datas, 566, 4, 1, true);
            pf3 = parseEnergyDataBySlice(datas, 570, 4, 1, true);
            // 校验码 2个字节
            // 帧结束符 2个字节
            
            
        }
        

        var obj = {};
        obj.deviceKey = firstDataAddress;
        obj.data = {
            "periodReport": periodReport,
            "deviceKey": firstDataAddress,
            "time": timestamp,
            "kwhp": kwhp,
            "kwhn": kwhn,
            "ua": ua,
            "ub": ub,
            "uc": uc,
            "ia": ia,
            "ib": ib,
            "ic": ic,
            "pt": pt,
            "pa": pa,
            "pb": pb,
            "pc": pc,
            "qt": qt,
            "q1": q1,
            "q2": q2,
            "q3": q3,
            "pft": pft,
            "pf1": pf1,
            "pf2": pf2,
            "pf3": pf3
        };
        // 设置倍率
        setRate(obj.data, obj.deviceKey);
        obj.time = timestamp;
        resultDatas.push(obj)
    } else if (periodReport === '00840000') {
        // 登录 采集器
        // IMEI(ASCII) 30 个字节
        var imei = hexToAscii(parseData(datas, 34, 30));
        // IMSI(ASCII) 40 个字节
        var imsi = hexToAscii(parseData(datas, 64, 40));
        // ICCID(ASCII) 40 个字节
        var iccid = hexToAscii(parseData(datas, 104, 40));
        // 生产商(ASCII) 16 个字节
        var manufacturer = hexToAscii(parseData(datas, 144, 16));
        // 型号(ASCII) 16 个字节
        var model = hexToAscii(parseData(datas, 160, 16));
        // 硬件版本号(ASCII) 16 个字节
        var hardwareVersion = hexToAscii(parseData(datas, 176, 16));
        // 硬件版本日期(ASCII) 16 个字节
        var hardwareVersionDate = hexToAscii(parseData(datas, 192, 16));
        // 保留 16 个字节
        var reserve = parseData(datas, 208, 16)
        // 固件版本日期(ASCII) 16 个字节
        var firmwareVersionDate = hexToAscii(parseData(datas, 224, 16));
        // 电表数量 2 个字节
        var meterCount = parseData(datas, 240, 2)
        // 电表1唯一标识(电表地址) 12个字节 根据数量循环
        var meterAddress1 = '';
        var meterAddress2 = '';
        var meterAddress3 = '';
        if (parseInt(meterCount) > 0) {
            for (var i = 0; i < parseInt(meterCount, 16); i++) {
                // 电表地址
                if (i === 0) {
                    meterAddress1 = parseData(datas, 242, 12)
                }
                if (i === 1) {
                    meterAddress2 = parseData(datas, 254, 12)
                }
                if (i === 2) {
                    meterAddress3 = parseData(datas, 266, 12)
                }
            }
        }
        // 校验码 2个字节
        // 帧结束符  2个字节
        var obj = {};
        obj.deviceKey = moduleAddress;
        obj.data = {
            "periodReport": periodReport,
            "imei": imei,
            "imsi": imsi,
            "iccid": iccid,
            "manufacturer": manufacturer,
            "model": model,
            "hardware_version": hardwareVersion,
            "hardware_version_date": hardwareVersionDate,
            "firmware_version_date": firmwareVersionDate,
            "meter_count": meterCount,
            "meter_address_one": meterAddress1,
            "meter_address_two": meterAddress2,
            "meter_address_three": meterAddress3
        };
        resultDatas.push(obj)
    } else if (periodReport === '00840003') {
        // 日冻结数据上报
        var obj = {};
        obj.deviceKey = moduleAddress;
        obj.data = {
            "periodReport": periodReport
        };
        resultDatas.push(obj)

    } else if (periodReport === '00840001') {
        // 主站通信测试,登录帧一样的回复，指令码变成 00840001
        var obj = {};
        obj.deviceKey = moduleAddress;
        obj.data = {
            "periodReport": periodReport
        };
        resultDatas.push(obj)
    } else {
        return null;
    }
    return resultDatas;
}
// 数据编码
this.encode = function (msg) {
    var datas = msg.data;
    // 模块地址
    var moduleAddress = datas.slice(16, 18) + datas.slice(14, 16) + datas.slice(12, 14) + datas.slice(10, 12) + datas.slice(8, 10) + datas.slice(6, 8);
    // 帧起始符 68
    var frameStart1 = datas.slice(18, 20);
    // 控制码 22
    var controlCode = datas.slice(20, 22);
    // 数据域长度 00 DD
    var dataLength = datas.slice(22, 26);
    // 周期上报标识 16进制减去 33H  原数据 37 33 B7 33   转换为 00840004
    var periodReport = parseData(datas, 26, 8);
    var data = ''
    if (periodReport === '00840000' || periodReport === '00840001' || periodReport === '00840003' || periodReport === '00840004') {
        // 登录 和 主站通信测试 日冻结数据上报 15分钟上报
        var moduleAddress1 = datas.slice(6, 18)
        var periodReport1 = datas.slice(26, 34)
        // 正常回复： 68 模块地址 68 A2 数据域长度 周期上报标识 校验码 16
        data = '68' + moduleAddress1 + '68A204' + periodReport1
        // 校验码 CS
        var cs = 0;
        for (var i = 0; i < data.length; i += 2) {
            cs += parseInt(data.slice(i, i + 2), 16)
        }
        cs = cs.toString(16).slice(-2)
        data += cs
        data += '16'
    } else {
        return null;
    }
    var resultDatas = {};
    resultDatas.deviceKey = moduleAddress;
    resultDatas.data = data;
    return resultDatas;
}

//const mag = {"data": "00EA68800200790859682200DD3733B733330A37373398A98B035533B6338C3B4739577336333333333333333333333333333333333333333836333333333333493433335C3333338C3433333333333333333333333333333333333349333333333333333B3333333A33333333333333333333333333333333333333333333333434333333333333733333333C33333384333333333333333333333333333333333333336B4333337543634CB3333333CC49B33583A664B3A935B3C863B33849B3333333B547B33A35B39C44B3333333993C33CABC3ABB33336BBBB816"}
// （645协议）：设备上报
//const mag = {"data": "007A686082469257136822006D3733B733339A93B579C58A46934533B63333484943565BB53333A7743333375A333335A63333375A33333333333335A6333333333333CB54CB54CC54CC7C33CA7C33C97C333383333333333333935C36B83C34C63C34BC3C34333333333333333333333333CC3C3343334333437916"}
// 698 协议
//const mag = {"data": "011F68750200790859682201123733B733343F4A3333A7BB8CFC5535B633784857355872643333333333333333333333333333C434333333333333E0623333333333333333333333333333333333333333333CD3433333333333333333333333333333333333333333333CD3433333333333333333333333333333333333333333333A74733333333333333333333333333335134333333333333894633333333333333333333333333333333333333333333C13433333333333333333333333333333333333333333333C134333333333333333333333333333333333333333333336E5935B6333A39393943398B4433964433B54433CB7C7854335A35B34C5433353A33C73933543A338835B39633B34534B3B533B3C53CBB3CB73CB73C5316"}
// const mag = {"data": "011F68750200790859682201123733B733343F4A3333A7BB8CFC5535B6336567BB122F2F4C354C446338FC1C4C6597FAC1506DA71A02D271AF2A2212A2703353939933533B333333F4ED33333D33333343333B383447533D35333334434C6597FAC1506DA71A02D271AF2A22122F2F2F685F6061665F606130462F2FA3B78861126262B1B762626262626262626262626262626163626262626262CD9062626262626246866262626262626262626262626262F16362626262626262626262626262626262626262626262F063626262733333333333333334333333343333333333336E5935B6334A333333B3333333BB9D335339333333C47B343334333333882D333334333333EE443433333333334F333333333333339A7534333333338416\n"}
const mag = {"data": "011F68750200790859682201123733B733343F4A3333A7BB8CFC5535B633EEE3F0CDF02C4C354C455338CBCB5B83D373B33435373A414F6AA00CE69AA2703353EB9833533B333333F4ED33333D33333343333B383436533D35333334435B83D373B33435373A414F6AA00CE69A13F4B6393E485D87CBCBCBCBCBCBCBCBCBCBCBCBCBCBA321CBCBCBCBCBCBCBCBCBCBCBCBCBCBCACCCBCBCBCBCBCBBFFACBCBCBCBCBCBAFEFCBCBCBCBCBCBCBCBCBCBCBCBCBCB5ACCCBCBCBCBCBCBCBCBCBCBCBCBCBCBCBCBCBCBCBCBCBCB59CCCBCBCB733333333333333334333333343333333333336E5935B6334A333333B3333333BB9D335339333333C47B343334333333882D333334333333EE443433333333334F333333333333339A7534333333338F16\n"}

var d = this.decode(mag)
console.log(d)
var e = this.encode(mag)
console.log(e)