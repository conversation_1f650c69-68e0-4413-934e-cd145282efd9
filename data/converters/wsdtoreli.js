// 温湿度转热力
this.decode = function (msg) {
    var data = {};
    msg.forEach(function (res) {
        if (res) {
            data.upPacketSN = -1;
            data.upDataSN = -1;
            data.topic = "ad";
            // res.time 为 2024-07-04 00:00:00 转为 long 类型
            data.timestamp = new Date(res.time).getTime().toString();
            data.tenantId = "";
            data.serviceId = "data_report";
            data.protocol = "mqtt";
            data.productId = "";
            data.payload = {
                "current_temperature": res.temperature,
                "current_humidity": res.humidity
            };
            data.messageType = "dataReport";
            data.deviceType = "";
            data.deviceId = res.deviceNo;
            data.assocAssetId = "";
            data.IMSI = "";
        }
    });
    return data;
};
this.encode = function (service, device) {
    return null;
}

const mag = [{
    "device_id": "nlrEUIpV58wFk1PI",
    "log": "10-0:0103M0T0S0P0D0",
    "temperature": 39.3,
    "humidity": 56.3,
    "deviceKey": "nlrEUIpV58wFk1PI",
    "deviceNo": "151818560089869",
    "time": "2024-07-04 16:00:27"
}]
this.decode(mag)

//{
//   "upPacketSN": -1,
//   "upDataSN": -1,
//   "topic": "ad",
//   "timestamp": 1720076175211,
//   "tenantId": "2000040039",
//   "serviceId": "data_report",
//   "protocol": "mqtt",
//   "productId": "15181856",
//   "payload": {
//     "current_temperature": 29.9,
//     "current_humidity": 39.4
//   },
//   "messageType": "dataReport",
//   "deviceType": "",
//   "deviceId": "151818560148410",
//   "assocAssetId": "",
//   "IMSI": "",
//   "IMEI": ""
// }