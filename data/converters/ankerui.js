// 安科瑞数据转换器
this.decode = function (msg) {
    var resultDatas = [];
    msg.devices.forEach(
        function (res) {
            var fields = res.fields
            var deviceData = {}
            var time = res.identity.time + ""
            deviceData.time = (time - time % 1000).toString()
            // 替换属性名
            for (var data in fields) {
                if (data !== '') {
                    var key = propertyNameMap[data]
                    if (key) {
                        deviceData[key] = fields[data].value
                    }
                }
                var key = propertyNameMap[data]
                if (key) {
                    deviceData[key] = fields[data].value
                }
            }
            if (deviceData.length !== 0) {
                resultDatas.push(deviceData)
            }
        }
    )
    console.log(resultDatas)
    if (resultDatas.length !== 0) {
        return resultDatas;
    }
    return null;
};

this.encode = function (service, id) {
    return null;
}

var propertyNameMap = {
    "02010100": "ua",
    "02010200": "ub",
    "02010300": "uc",
    "02010101": "uab",
    "02010202": "ubc",
    "02010303": "uca",
    "02020100": "ia",
    "02020200": "ib",
    "02020300": "ic",
    "02030100": "pa",
    "02030200": "pb",
    "02030300": "pc",
    "02030000": "pt",
    "02040101": "q1",
    "02040102": "q2",
    "02040103": "q3",
    "02040100": "qt",
    "02050100": "sa",
    "02050200": "sb",
    "02050300": "sc",
    "02050000": "st",
    "02060100": "pf1",
    "02060200": "pf2",
    "02060300": "pf3",
    "02060000": "pft",
    "00010000": "kwhp",
    "00020000": "kwhn",
    "020A0101": "thdua",
    "020A0201": "thdub",
    "020A0301": "thduc",
    "020B0101": "thdia",
    "020B0201": "thdib",
    "020B0301": "thdic",
    "03090000": "u_unbalance_rate",
    "030A0000": "i_unbalance_rate",
    "00000100": "kwhp_peak",
    "00000200": "kwhp_sharp",
    "00000300": "kwhp_flat",
    "00000400": "kwhp_valley",
    "02800002": "freq",
    "00030000": "rssi",
    "03010000": "kvarhp"
}

// 示例消息
// var mag = {"id":"907","retransmission":"false","devices":[{"identity":{"template":"SK2rN3eSDPeBjKTQ","device":"w5FHNByKYYdP2Y34","time":1747791060172},"fields":{"02040103":{"value":-2640},"02040100":{"value":21120},"02050100":{"value":29040},"02050200":{"value":0},"02050300":{"value":29040},"02050000":{"value":47520},"02060100":{"value":0.581},"02060200":{"value":1},"02060300":{"value":0.996},"02060000":{"value":0.908},"02800002":{"value":49.98},"00030000":{"value":24},"00010000":{"value":2085336.0000000004},"00020000":{"value":0},"03010000":{"value":1073688}}}]}
var mag = {"id": "997", "retransmission": "false", "devices": [{"identity": {"template": "SK2rN3eSDPeBjKTQ", "device": "w5FHNByKYYdP2Y34", "time": 1747796460172}, "fields": {"02040103": {"value": 0}, "02040100": {"value": 21120}, "02050100": {"value": 29040}, "02050200": {"value": 0}, "02050300": {"value": 29040}, "02050000": {"value": 47520}, "02060100": {"value": 0.576}, "02060200": {"value": 1}, "02060300": {"value": 0.996}, "02060000": {"value": 0.906}, "02800002": {"value": 49.95}, "00030000": {"value": 24}, "00010000": {"value": 2155824}, "00020000": {"value": 0}, "03010000": {"value": 1110648}}}]}
// var mag = {"id": "998", "retransmission": "false", "devices": [{"identity": {"template": "SK2rN3eSDPeBjKTQ", "device": "w5FHNByKYYdP2Y34", "time": 1747796520172}, "fields": {"02040103": {"value": 0}, "02040100": {"value": 21120}, "02050100": {"value": 29040}, "02050200": {"value": 0}, "02050300": {"value": 29040}, "02050000": {"value": 47520}, "02060100": {"value": 0.577}, "02060200": {"value": 1}, "02060300": {"value": 0.996}, "02060000": {"value": 0.906}, "02800002": {"value": 49.95}, "00030000": {"value": 24}, "00010000": {"value": 2156616}, "00020000": {"value": 0}, "03010000": {"value": 1110912}}}]}
this.decode(mag);