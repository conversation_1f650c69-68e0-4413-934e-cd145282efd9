// 16进制减去 33H,两个字节顺序从后往前
function parseData(datas, start, size) {
    var data = '';
    for (var i = start + size - 2; i >= start; i -= 2) {
        data += padZero((parseInt(datas.slice(i, i + 2), 16) - 0x33).toString(16), 2);
    }
    return data;
}

function hexToAscii(hexString) {
    var asciiString = '';
    for (var i = 0; i < hexString.length; i += 2) {
        asciiString += String.fromCharCode(parseInt(hexString.substr(i, 2), 16));
    }
    return asciiString;
}

// 补零
function padZero(num, size) {
    var s = num + "";
    while (s.length < size) s = "0" + s;
    return s;
}

// 解析电能数据  datas:数据  start:开始位置  size:数据长度  point:小数点位置
function parseEnergyDataBySlice(datas, start, size, point) {
    var data = parseData(datas, start, size);
    //在第point位后加小数点
    data = parseFloat(data.slice(0, point) + '.' + data.slice(point)).toString();
    // 最高位是符号位，判断正负
    if (parseInt(data.slice(0, 1), 16) >= 8) {
        // 加负号
        data = '-' + parseFloat(data.slice(1));
    }
    // 检查是否为NaN，如果是则设置为0
    if (isNaN(data)) {
        data = '0';
    }
    return data;
}

// 安全乘法
function safeMultiply(value, multiplier) {
    return value ? (parseFloat((parseFloat(value) * multiplier).toFixed(3))).toString() : null;
}

// 设置倍率
function setRate(data, deviceKey) {
    if (!data || !deviceKey) {
        return;
    }

    var ct = 1;
    var pt = 1;

    var deviceRates = {
        "587665000404": {"ct": "400/5", "pt": "35000/100"}
    };

    if (deviceRates[deviceKey]) {
        var rates = deviceRates[deviceKey];
        ct = parseFloat(rates.ct.split('/')[0]) / parseFloat(rates.ct.split('/')[1]);
        pt = parseFloat(rates.pt.split('/')[0]) / parseFloat(rates.pt.split('/')[1]);
    } else {
        return;
    }

    // 正向有功总电能（需要乘以PT和CT的倍率）
    if (data.kwhp) data.kwhp = safeMultiply(data.kwhp, pt * ct);
    // 反向有功总电能（需要乘以PT和CT的倍率）
    if (data.kwhn) data.kwhn = safeMultiply(data.kwhn, pt * ct);
    // A相电压（需要乘以PT的倍率）
    if (data.ua) data.ua = safeMultiply(data.ua, pt);
    // B相电压（需要乘以PT的倍率）
    if (data.ub) data.ub = safeMultiply(data.ub, pt);
    // C相电压（需要乘以PT的倍率）
    if (data.uc) data.uc = safeMultiply(data.uc, pt);
    // A相电流（需要乘以CT的倍率）
    if (data.ia) data.ia = safeMultiply(data.ia, ct);
    // B相电流（需要乘以CT的倍率）
    if (data.ib) data.ib = safeMultiply(data.ib, ct);
    // C相电流（需要乘以CT的倍率）
    if (data.ic) data.ic = safeMultiply(data.ic, ct);
    // 总有功功率（需要乘以PT和CT的倍率）
    if (data.pt) data.pt = safeMultiply(data.pt, pt * ct);
    // A相有功功率（需要乘以PT和CT的倍率）
    if (data.pa) data.pa = safeMultiply(data.pa, pt * ct);
    // B相有功功率（需要乘以PT和CT的倍率）
    if (data.pb) data.pb = safeMultiply(data.pb, pt * ct);
    // C相有功功率（需要乘以PT和CT的倍率）
    if (data.pc) data.pc = safeMultiply(data.pc, pt * ct);
    // 总无功功率（需要乘以PT和CT的倍率）
    if (data.qt) data.qt = safeMultiply(data.qt, pt * ct);
    // A相无功功率（需要乘以PT和CT的倍率）
    if (data.q1) data.q1 = safeMultiply(data.q1, pt * ct);
    // B相无功功率（需要乘以PT和CT的倍率）
    if (data.q2) data.q2 = safeMultiply(data.q2, pt * ct);
    // C相无功功率（需要乘以PT和CT的倍率）
    if (data.q3) data.q3 = safeMultiply(data.q3, pt * ct);
    // 总视在功率（需要乘以PT和CT的倍率）
    // A相视在功率（需要乘以PT和CT的倍率）
    // B相视在功率（需要乘以PT和CT的倍率）
    // C相视在功率（需要乘以PT和CT的倍率）
}

//数据解析
this.decode = function (msg) {
    var resultDatas = [];
    var datas = msg.data;
    // 帧总长度 00 EA
    var frameLength = datas.slice(0, 4);
    // 帧起始符 68
    var frameStart = datas.slice(4, 6);
    // 模块地址 135792468417  网关地址 135792468417
    var moduleAddress = datas.slice(16, 18) + datas.slice(14, 16) + datas.slice(12, 14) + datas.slice(10, 12) + datas.slice(8, 10) + datas.slice(6, 8);
    // 帧起始符 68
    var frameStart1 = datas.slice(18, 20);
    // 控制码 22
    var controlCode = datas.slice(20, 22);
    // 数据域长度 00 DD
    var dataLength = datas.slice(22, 26);
    // 周期上报标识 16进制减去 33H  原数据 37 33 B7 33   转换为 00840004
    var periodReport = parseData(datas, 26, 8);
    if (periodReport === '00840004') {
        // 15分钟上报
        // 第一块表数据总长度 33 0A
        var firstDataLength = datas.slice(34, 38);
        //第一块表地址 8C B5 79 C5 8A 46  135792468259
        var firstDataAddress = parseData(datas, 38, 12);
        // 数据项长度
        var dataItemLength = datas.slice(50, 52);
        // 数据标识 8个字节 55 33 B6 33 将十六进制的字符串转换为十进制的数字，然后减去33，再将结果转换为十六进制的字符串
        var dataFlag = parseData(datas, 52, 8);
        // 时间 10 个字节
        var time = '20' + padZero((parseInt(datas.slice(68, 70), 16) - 0x33).toString(16), 2) + '-' +
            padZero((parseInt(datas.slice(66, 68), 16) - 0x33).toString(16), 2) + '-' +
            padZero((parseInt(datas.slice(64, 66), 16) - 0x33).toString(16), 2) + ' ' +
            padZero((parseInt(datas.slice(62, 64), 16) - 0x33).toString(16), 2) + ':' +
            padZero((parseInt(datas.slice(60, 62), 16) - 0x33).toString(16), 2) + ':00';
        //转为时间戳
        var timestamp = new Date(time).getTime().toString();

        // 正向有功总电能 8个字节
        // var forwardActiveTotalEnergy = datas.slice(70, 78);
        var forwardActiveTotalEnergy = parseEnergyDataBySlice(datas, 70, 8, 6);
        // 正向有功费率1电能 8个字节
        // var forwardActiveRate1Energy = datas.slice(78, 86);
        var forwardActiveRate1Energy = parseEnergyDataBySlice(datas, 78, 8, 6);
        // var forwardActiveRate2Energy = datas.slice(86, 94);
        var forwardActiveRate2Energy = parseEnergyDataBySlice(datas, 86, 8, 6);
        // var forwardActiveRate3Energy = datas.slice(94, 102);
        var forwardActiveRate3Energy = parseEnergyDataBySlice(datas, 94, 8, 6);
        // var forwardActiveRate4Energy = datas.slice(102, 110);
        var forwardActiveRate4Energy = parseEnergyDataBySlice(datas, 102, 8, 6);
        // var forwardActiveRate5Energy = datas.slice(110, 118,6);
        var forwardActiveRate5Energy = parseEnergyDataBySlice(datas, 110, 8, 6);
        // var forwardActiveRate6Energy = datas.slice(118, 126);
        var forwardActiveRate6Energy = parseEnergyDataBySlice(datas, 118, 8, 6);
        // var forwardActiveRate7Energy = datas.slice(126, 134);
        var forwardActiveRate7Energy = parseEnergyDataBySlice(datas, 126, 8, 6);
        // 正向有功费率8电能 8个字节
        var forwardActiveRate8Energy = parseEnergyDataBySlice(datas, 134, 8, 6);
        // 反向有功总电能 8个字节
        var reverseActiveTotalEnergy = parseEnergyDataBySlice(datas, 142, 8, 6);
        // 反向有功费率1电能 8个字节
        var reverseActiveRate1Energy = parseEnergyDataBySlice(datas, 150, 8, 6);
        var reverseActiveRate2Energy = parseEnergyDataBySlice(datas, 158, 8, 6);
        var reverseActiveRate3Energy = parseEnergyDataBySlice(datas, 166, 8, 6);
        var reverseActiveRate4Energy = parseEnergyDataBySlice(datas, 174, 8, 6);
        var reverseActiveRate5Energy = parseEnergyDataBySlice(datas, 182, 8, 6);
        var reverseActiveRate6Energy = parseEnergyDataBySlice(datas, 190, 8, 6);
        var reverseActiveRate7Energy = parseEnergyDataBySlice(datas, 198, 8, 6);
        // 反向有功费率8电能 8个字节
        var reverseActiveRate8Energy = parseEnergyDataBySlice(datas, 206, 8, 6);
        // 组合无功1总电能 8个字节
        var combinedReactive1TotalEnergy = parseEnergyDataBySlice(datas, 214, 8, 6);
        // 组合无功1费率1电能 8个字节
        var combinedReactive1Rate1Energy = parseEnergyDataBySlice(datas, 222, 8, 6);
        var combinedReactive1Rate2Energy = parseEnergyDataBySlice(datas, 230, 8, 6);
        var combinedReactive1Rate3Energy = parseEnergyDataBySlice(datas, 238, 8, 6);
        var combinedReactive1Rate4Energy = parseEnergyDataBySlice(datas, 246, 8, 6);
        var combinedReactive1Rate5Energy = parseEnergyDataBySlice(datas, 254, 8, 6);
        var combinedReactive1Rate6Energy = parseEnergyDataBySlice(datas, 262, 8, 6);
        var combinedReactive1Rate7Energy = parseEnergyDataBySlice(datas, 270, 8, 6);
        // 组合无功1费率8电能 8个字节
        var combinedReactive1Rate8Energy = parseEnergyDataBySlice(datas, 278, 8, 6);
        // 组合无功2总电能 8个字节
        var combinedReactive2TotalEnergy = parseEnergyDataBySlice(datas, 286, 8, 6);
        // 组合无功2费率1电能 8个字节
        var combinedReactive2Rate1Energy = parseEnergyDataBySlice(datas, 294, 8, 6);
        var combinedReactive2Rate2Energy = parseEnergyDataBySlice(datas, 302, 8, 6);
        var combinedReactive2Rate3Energy = parseEnergyDataBySlice(datas, 310, 8, 6);
        var combinedReactive2Rate4Energy = parseEnergyDataBySlice(datas, 318, 8, 6);
        var combinedReactive2Rate5Energy = parseEnergyDataBySlice(datas, 326, 8, 6);
        var combinedReactive2Rate6Energy = parseEnergyDataBySlice(datas, 334, 8, 6);
        var combinedReactive2Rate7Energy = parseEnergyDataBySlice(datas, 342, 8, 6);
        // 组合无功2费率8电能 8个字节
        var combinedReactive2Rate8Energy = parseEnergyDataBySlice(datas, 350, 8, 6);
        // 电压数据块1 4个字节 A 相电压
        var voltageData1 = parseEnergyDataBySlice(datas, 358, 4, 3);
        // B 相电压
        var voltageData2 = parseEnergyDataBySlice(datas, 362, 4, 3);
        // C 相电压
        var voltageData3 = parseEnergyDataBySlice(datas, 366, 4, 3);
        // 电流数据块 6个字节 A 相电流
        var currentData1 = parseEnergyDataBySlice(datas, 370, 6, 3);
        // B 相电流
        var currentData2 = parseEnergyDataBySlice(datas, 376, 6, 3);
        // C 相电流
        var currentData3 = parseEnergyDataBySlice(datas, 382, 6, 3);
        // 电网频率 4个字节
        var gridFrequency = parseEnergyDataBySlice(datas, 388, 4, 2);
        // 当前有功需量 6个字节
        var currentActiveDemand = parseEnergyDataBySlice(datas, 392, 6, 2);
        // 当前无功需量 6个字节
        var currentReactiveDemand = parseEnergyDataBySlice(datas, 398, 6, 2);
        // 有功功率数据块 6个字节 总有功功率
        var activePowerData1 = parseEnergyDataBySlice(datas, 404, 6, 2);
        // A 相有功功率
        var activePowerData2 = parseEnergyDataBySlice(datas, 410, 6, 2);
        // B 相有功功率
        var activePowerData3 = parseEnergyDataBySlice(datas, 416, 6, 2);
        // C 相有功功率
        var activePowerData4 = parseEnergyDataBySlice(datas, 422, 6, 2);
        // 无功功率数据块 6个字节 总无功功率
        var reactivePowerData1 = parseEnergyDataBySlice(datas, 428, 6, 2);
        // A 相无功功率
        var reactivePowerData2 = parseEnergyDataBySlice(datas, 434, 6, 2);
        // B 相无功功率
        var reactivePowerData3 = parseEnergyDataBySlice(datas, 440, 6, 2);
        // C 相无功功率
        var reactivePowerData4 = parseEnergyDataBySlice(datas, 446, 6, 2);
        // 功率因数数据块 4个字节 总功率因数
        var powerFactorData1 = parseEnergyDataBySlice(datas, 452, 4, 1);
        // A 相功率因数
        var powerFactorData2 = parseEnergyDataBySlice(datas, 456, 4, 1);
        // B 相功率因数
        var powerFactorData3 = parseEnergyDataBySlice(datas, 460, 4, 1);
        // C 相功率因数
        var powerFactorData4 = parseEnergyDataBySlice(datas, 464, 4, 1);
        // 校验码
        var checkCode = datas.slice(468, 470);
        // 帧结束符
        var frameEnd = datas.slice(470, 472);

        var obj = {};
        obj.deviceKey = firstDataAddress;
        obj.data = {
            "periodReport": periodReport,
            "deviceKey": firstDataAddress,
            "time": timestamp,
            "kwhp": forwardActiveTotalEnergy,
            "kwhn": reverseActiveTotalEnergy,
            "ua": voltageData1,
            "ub": voltageData2,
            "uc": voltageData3,
            "ia": currentData1,
            "ib": currentData2,
            "ic": currentData3,
            "pt": activePowerData1,
            "pa": activePowerData2,
            "pb": activePowerData3,
            "pc": activePowerData4,
            "qt": reactivePowerData1,
            "q1": reactivePowerData2,
            "q2": reactivePowerData3,
            "q3": reactivePowerData4,
            "pft": powerFactorData1,
            "pf1": powerFactorData2,
            "pf2": powerFactorData3,
            "pf3": powerFactorData4
        };
        // 设置倍率
        setRate(obj.data, obj.deviceKey);
        obj.time = timestamp;
        resultDatas.push(obj)
    } else if (periodReport === '00840000') {
        // 登录 采集器
        // IMEI(ASCII) 30 个字节
        var imei = hexToAscii(parseData(datas, 34, 30));
        // IMSI(ASCII) 40 个字节
        var imsi = hexToAscii(parseData(datas, 64, 40));
        // ICCID(ASCII) 40 个字节
        var iccid = hexToAscii(parseData(datas, 104, 40));
        // 生产商(ASCII) 16 个字节
        var manufacturer = hexToAscii(parseData(datas, 144, 16));
        // 型号(ASCII) 16 个字节
        var model = hexToAscii(parseData(datas, 160, 16));
        // 硬件版本号(ASCII) 16 个字节
        var hardwareVersion = hexToAscii(parseData(datas, 176, 16));
        // 硬件版本日期(ASCII) 16 个字节
        var hardwareVersionDate = hexToAscii(parseData(datas, 192, 16));
        // 保留 16 个字节
        var reserve = parseData(datas, 208, 16)
        // 固件版本日期(ASCII) 16 个字节
        var firmwareVersionDate = hexToAscii(parseData(datas, 224, 16));
        // 电表数量 2 个字节
        var meterCount = parseData(datas, 240, 2)
        // 电表1唯一标识(电表地址) 12个字节 根据数量循环
        var meterAddress1 = '';
        var meterAddress2 = '';
        var meterAddress3 = '';
        if (parseInt(meterCount) > 0) {
            for (var i = 0; i < parseInt(meterCount, 16); i++) {
                // 电表地址
                if (i === 0) {
                    meterAddress1 = parseData(datas, 242, 12)
                }
                if (i === 1) {
                    meterAddress2 = parseData(datas, 254, 12)
                }
                if (i === 2) {
                    meterAddress3 = parseData(datas, 266, 12)
                }
            }
        }
        // 校验码 2个字节
        // 帧结束符  2个字节
        var obj = {};
        obj.deviceKey = moduleAddress;
        obj.data = {
            "periodReport": periodReport,
            "imei": imei,
            "imsi": imsi,
            "iccid": iccid,
            "manufacturer": manufacturer,
            "model": model,
            "hardware_version": hardwareVersion,
            "hardware_version_date": hardwareVersionDate,
            "firmware_version_date": firmwareVersionDate,
            "meter_count": meterCount,
            "meter_address_one": meterAddress1,
            "meter_address_two": meterAddress2,
            "meter_address_three": meterAddress3
        };
        resultDatas.push(obj)
    } else if (periodReport === '00840003') {
        // 日冻结数据上报
        var obj = {};
        obj.deviceKey = moduleAddress;
        obj.data = {
            "periodReport": periodReport
        };
        resultDatas.push(obj)

    } else if (periodReport === '00840001') {
        // 主站通信测试,登录帧一样的回复，指令码变成 00840001
        var obj = {};
        obj.deviceKey = moduleAddress;
        obj.data = {
            "periodReport": periodReport
        };
        resultDatas.push(obj)
    } else {
        return null;
    }
    return resultDatas;
}
// 数据编码
this.encode = function (msg) {
    var datas = msg.data;
    // 模块地址
    var moduleAddress = datas.slice(16, 18) + datas.slice(14, 16) + datas.slice(12, 14) + datas.slice(10, 12) + datas.slice(8, 10) + datas.slice(6, 8);
    // 帧起始符 68
    var frameStart1 = datas.slice(18, 20);
    // 控制码 22
    var controlCode = datas.slice(20, 22);
    // 数据域长度 00 DD
    var dataLength = datas.slice(22, 26);
    // 周期上报标识 16进制减去 33H  原数据 37 33 B7 33   转换为 00840004
    var periodReport = parseData(datas, 26, 8);
    var data = ''
    if (periodReport === '00840000' || periodReport === '00840001' || periodReport === '00840003' || periodReport === '00840004') {
        // 登录 和 主站通信测试 日冻结数据上报 15分钟上报
        var moduleAddress1 = datas.slice(6, 18)
        var periodReport1 = datas.slice(26, 34)
        // 正常回复： 68 模块地址 68 A2 数据域长度 周期上报标识 校验码 16
        data = '68' + moduleAddress1 + '68A204' + periodReport1
        // 校验码 CS
        var cs = 0;
        for (var i = 0; i < data.length; i += 2) {
            cs += parseInt(data.slice(i, i + 2), 16)
        }
        cs = cs.toString(16).slice(-2)
        data += cs
        data += '16'
    } else {
        return null;
    }
    var resultDatas = {};
    resultDatas.deviceKey = moduleAddress;
    resultDatas.data = data;
    return resultDatas;
}

const mag = {"data": "00EA68800200790859682200DD3733B733330A37373398A98B035533B6338C3B4739577336333333333333553433339834333385333333333333333333333333333333333333333836333333333333493433335C3333338C3433333333333333333333333333333333333349333333333333333B3333333A33333333333333333333333333333333333333333333333434333333333333733333333C33333384333333333333333333333333333333333333336B4333337543634CB3333333CC49B33583A664B3A935B3C863B33849B3333333B547B33A35B39C44B3333333993C33CABC3ABB33336BBBB816"}
this.decode(mag)
this.encode(mag)