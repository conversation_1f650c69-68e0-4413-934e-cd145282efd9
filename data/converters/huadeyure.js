// 华德余热-有人的物联网数据转换器
this.decode = function (msg) {
    // 最终要返回的数组
    var resultDatas = [];

    // 存储同一个 deviceKey 的数据，避免重复 deviceKey 重复出现
    var dataMap = {};

    // msg 中的 d 数组（根据你提供的数据结构，这里取 msg.d）
    var datas = msg.d;

    if (!datas) {
        // 如果 d 不存在或不是数组，则直接返回 null
        return null;
    }

    datas.forEach(function (item) {
        // item 中包含：sid, pid, v, s, ms, e 等
        if (!item.pid || typeof item.pid !== 'string') {
            return; // pid 不存在或非字符串，则跳过
        }

        // 检查 pid 中是否含有 '_'
        if (item.pid.indexOf('_') === -1) {
            // 如果没有 '_', 则跳过
            return;
        }

        // 用 '_' 分割 pid
        var pidSplit = item.pid.split('_');
        // 第一个片段作为 deviceKey
        var deviceKey = pidSplit.shift();
        // 剩下的部分合并成 属性标识
        var paramName = pidSplit.join('_');

        // 获取当前属性对应的值和时间
        var value = item.v;
        var timestamp = item.s;
         

        if (isNaN(value)) {
            // 如果 value 不能转成数值，则跳过
            return;
        }
        // 将 value 转成数值类型
        var valueNum;

        // 冷却水池温度	cooling_pool_temp
        // 机组回水池温度	return_pool_temp
        // 制热设定温度	heating_set_temp
        // 水箱进机组温度	water_tank_in_temp
        // 机组制热温度	unit_heating_temp
        // 压缩机电流	compressor_current
        // 都需要除以 10
        switch (paramName) {
            case 'cooling_pool_temp':
            case 'return_pool_temp':
            case 'heating_set_temp':
            case 'water_tank_in_temp':
            case 'unit_heating_temp':
            case 'compressor_current':
                valueNum = parseFloat(value) / 10;
                break;
            case 'run_indicator':
            case 'fault_indicator':
                valueNum = parseInt(value);
                break;
            default:
                valueNum = parseFloat(value);
                break;
        }

        // 如果在 dataMap 中还没有这个 deviceKey，就新建一个结构
        if (!dataMap[deviceKey]) {
            dataMap[deviceKey] = {
                deviceKey: deviceKey,
                data: {}
            };
        }

        // 将该属性和值放入 data 中
        dataMap[deviceKey].data[paramName] = valueNum;

        // 记录时间戳（如果有多个时间戳需求，可以调整这里的逻辑）
        //dataMap[deviceKey].data["time"] = timestamp * 1000;
    });

    // 遍历 dataMap，把合并好的结果 push 到 resultDatas
    for (var key in dataMap) {
        if (dataMap.hasOwnProperty(key)) {
            resultDatas.push(dataMap[key]);
        }
    }

    // 若没取到任何数据则可能为空数组，根据需要可自行处理
    return resultDatas.length > 0 ? resultDatas : null;
};

this.encode = function (msg) {
    // 该场景暂不需要上行编码
    return null;
}

const mag = {
    "f": "rp",
    "d": [
        {
            "sid": "ModbusRTU",
            "pid": "mKkd7DbGhSr72ADW_heating_set_temp",
            "v": "430",
            "s": 1735186261,
            "ms": 526,
            "e": 0
        },
        {
            "sid": "ModbusRTU",
            "pid": "mKkd7DbGhSr72ADW_cooling_pool_temp",
            "v": "186",
            "s": 1735186261,
            "ms": 384,
            "e": 0
        },
        {
            "sid": "ModbusRTU",
            "pid": "mKkd7DbGhSr72ADW_return_pool_temp",
            "v": "139",
            "s": 1735186261,
            "ms": 384,
            "e": 0
        },
        {
            "sid": "ModbusRTU",
            "pid": "mKkd7DbGhSr72ADW_unit_heating_temp",
            "v": "511",
            "s": 1735186261,
            "ms": 384,
            "e": 0
        },
        {
            "sid": "ModbusRTU",
            "pid": "mKkd7DbGhSr72ADW_water_tank_in_temp",
            "v": "421",
            "s": 1735186261,
            "ms": 384,
            "e": 0
        },
        {
            "sid": "ModbusRTU",
            "pid": "mKkd7DbGhSr72ADW_compressor_run_time",
            "v": "2232",
            "s": 1735186261,
            "ms": 240,
            "e": 0
        },
        {
            "sid": "ModbusRTU",
            "pid": "mKkd7DbGhSr72ADW_compressor_current",
            "v": "2038",
            "s": 1735186261,
            "ms": 99,
            "e": 0
        },
        {
            "sid": "S7-200 Smart TCP",
            "pid": "LST1_FMK_WDSDZ",
            "v": "17",
            "s": 1735186261,
            "ms": 23,
            "e": 0
        },
        {
            "sid": "S7-200 Smart TCP",
            "pid": "variable2",
            "v": "21",
            "s": 1735186261,
            "ms": 2,
            "e": 0
        },
        {
            "sid": "S7-200 Smart TCP",
            "pid": "SX1_YW",
            "v": "2.41658",
            "s": 1735186260,
            "ms": 981,
            "e": 0
        },
        {
            "sid": "S7-200 Smart TCP",
            "pid": "SX1_WD",
            "v": "19.6944",
            "s": 1735186260,
            "ms": 981,
            "e": 0
        },
        {
            "sid": "S7-200 Smart TCP",
            "pid": "variable5",
            "v": "0",
            "s": 1735186260,
            "ms": 959,
            "e": 0
        },
        {
            "sid": "S7-200 Smart TCP",
            "pid": "variable1",
            "v": "1",
            "s": 1735186260,
            "ms": 959,
            "e": 0
        },
        {
            "sid": "S7-200 Smart TCP",
            "pid": "variable3",
            "v": "0",
            "s": 1735186260,
            "ms": 959,
            "e": 0
        },
        {
            "sid": "S7-200 Smart TCP",
            "pid": "variable4",
            "v": "0",
            "s": 1735186260,
            "ms": 959,
            "e": 0
        },
        {
            "sid": "ModbusRTU",
            "pid": "mKkd7DbGhSr72ADW_fault_indicator",
            "v": "0",
            "s": 1735186260,
            "ms": 857,
            "e": 0
        },
        {
            "sid": "ModbusRTU",
            "pid": "mKkd7DbGhSr72ADW_run_indicator",
            "v": "1",
            "s": 1735186260,
            "ms": 857,
            "e": 0
        },
        {
            "sid": "ModbusRTU",
            "pid": "kwhp",
            "v": "293326",
            "s": 1735186260,
            "ms": 754,
            "e": 0
        },
        {
            "sid": "S7-200 Smart TCP",
            "pid": "XHB2_YX",
            "v": "0",
            "s": 1735186260,
            "ms": 738,
            "e": 0
        },
        {
            "sid": "S7-200 Smart TCP",
            "pid": "XHB1_YX",
            "v": "1",
            "s": 1735186260,
            "ms": 738,
            "e": 0
        },
        {
            "sid": "S7-200 Smart TCP",
            "pid": "PTG1_FM_GDW",
            "v": "0",
            "s": 1735186260,
            "ms": 738,
            "e": 0
        },
        {
            "sid": "S7-200 Smart TCP",
            "pid": "PTG1_FM_KDW",
            "v": "0",
            "s": 1735186260,
            "ms": 738,
            "e": 0
        },
        {
            "sid": "S7-200 Smart TCP",
            "pid": "PTG1_FM_GDW",
            "v": "0",
            "s": 1735186231,
            "ms": 291,
            "e": 0
        }
    ]
}

console.log(this.decode(mag));
this.encode(mag)
