this.decode = function (list) {
    var resultDatas = [];
    list.forEach(function(v){
        var obj ={}
        var dateee = new Date(v.time).toJSON();
        var dataTemp = new Date(+new Date(dateee) + 8 * 3600 * 1000).toISOString().replace(/T/g, ' ').replace(/\.[\d]{3}Z/, '')

        obj.deviceId = v.device_id

        obj.inPw = v.kwhp
        obj.p = v.pt
        obj.ia = v.ia
        obj.ib = v.ib
        obj.ic = v.ic
        obj.q = v.qt
        obj.inQv = v.kvarhn
        obj.ua = v.ua
        obj.ub = v.ub
        obj.uc = v.uc

        // 使用indexOf替代includes，提高兼容性
        var specialDevices = [
            '587665000404', 'sG4fcScZicmQ7Mbc'
        ];
        if(specialDevices.indexOf(v.device_id) !== -1){
            // 正向有功电能
            obj.inPw = v.kwhn
            // 转为正数
            obj.p = -v.pt
            obj.ia = -v.ia
            obj.ib = -v.ib
            obj.ic = -v.ic
            obj.q = -v.qt
        }
        obj.reportTime = dataTemp
        resultDatas.push(obj)
    })
    return resultDatas
};

this.encode = function (service,device) {
    return null;
}