// 中电电表转华润电力脚本
this.decode = function (list) {
    var result = {d: []};
    list.forEach(function (v) {
        var dateee = new Date(v.time).toJSON();
        var dataTemp = new Date(+new Date(dateee) + 8 * 3600 * 1000).toISOString().replace(/T/g, ' ').replace(/\.[\d]{3}Z/, '');

        var device_id = v.device_id;
        if (device_id === "pR4kczrepDxxkarb") {
            device_id = "WHSH11GC05";
        }
        var devs = {
            dev: device_id,
            d: []
        };

        if (v.kwhp) devs.d.push({tag: "el_kwhli", value: v.kwhp});
        if (v.kvarhp) devs.d.push({tag: "el_kvarhli", value: v.kvarhp});
        if (v.kwhn) devs.d.push({tag: "el_kwhle", value: v.kwhn});
        if (v.kvarhn) devs.d.push({tag: "el_kvarhle", value: v.kvarhn});
        if (v.pt) devs.d.push({tag: "el_pttl", value: v.pt});
        if (v.qt) devs.d.push({tag: "el_qttl", value: v.qt});
        if (v.pft) devs.d.push({tag: "el_costtl", value: v.pft});
        if (v.ia) devs.d.push({tag: "el_ia", value: v.ia});
        if (v.ib) devs.d.push({tag: "el_ib", value: v.ib});
        if (v.ic) devs.d.push({tag: "el_ic", value: v.ic});
        if (v.ua) devs.d.push({tag: "el_ua", value: v.ua});
        if (v.ub) devs.d.push({tag: "el_ub", value: v.ub});
        if (v.uc) devs.d.push({tag: "el_uc", value: v.uc});
        if (v.uab) devs.d.push({tag: "el_uab", value: v.uab});
        if (v.ubc) devs.d.push({tag: "el_ubc", value: v.ubc});
        if (v.uca) devs.d.push({tag: "el_uca", value: v.uca});

        result.d.push({
            devs: [devs],
            ts: dataTemp
        });
    });
    return result;
};
this.encode = function (service, device) {
    return null;
}